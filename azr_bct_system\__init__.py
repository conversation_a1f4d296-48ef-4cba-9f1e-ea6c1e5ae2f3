#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🧠 AZR BCT SYSTEM - POINT D'ENTRÉE PRINCIPAL
================================================================================

Architecture modulaire optimale pour le système AZR 3 Rollouts BCT
Remplace le monolithe rollouts.py par une structure modulaire sophistiquée

MODULES PRINCIPAUX :
- core/          : Cœur du système (BaseRollout, Manager, Config)
- rollouts/      : 3 Rollouts spécialisés (60%-30%-10%)
- validation/    : Métriques et validation AZR
- math/          : Moteur mathématique (50 équations AZR)
- environment/   : Environnement BCT et adaptations
- insights/      : Analyses avancées et révolution paradigmatique
- utils/         : Utilitaires et helpers
- tests/         : Tests complets (unitaires, intégration, performance)

AUTEUR : AZR System adapté BCT
VERSION : 2.0.0 (Architecture Modulaire)
================================================================================
"""

# ============================================================================
# 🎯 IMPORTS PRINCIPAUX - CŒUR DU SYSTÈME
# ============================================================================

# Core system
from .core.base_rollout import BaseAZRRollout
from .core.rollout_manager import AZRRolloutManager
from .core.azr_config import AZRConfig

# 3 Rollouts spécialisés
from .rollouts.analyzer_rollout import MultidimensionalAnalyzerRollout
from .rollouts.generator_rollout import SophisticatedHypothesisGeneratorRollout
from .rollouts.predictor_rollout import ContinuityDiscontinuityMasterPredictorRollout

# Validation et métriques
from .validation.validation_metrics import AZRValidationMetrics
from .validation.validation_manager import AZRValidationManager

# Moteur mathématique
from .math.azr_math_engine import AZRMathEngine
from .math.reward_system import AZRRewardSystem

# Environnement BCT
from .environment.baccarat_environment import BaccaratEnvironment
from .environment.bct_adapter import BCTAdapter

# ============================================================================
# 🚀 INTERFACE PRINCIPALE SIMPLIFIÉE
# ============================================================================

class AZRBCTSystem:
    """
    Interface principale simplifiée pour le système AZR BCT
    
    Point d'entrée unique pour toutes les fonctionnalités :
    - Initialisation automatique des 3 rollouts
    - Configuration centralisée
    - Validation intégrée
    - Interface avec bct.py
    """
    
    def __init__(self, config_path: str = None):
        """
        Initialise le système AZR BCT complet
        
        Args:
            config_path: Chemin vers fichier de configuration (optionnel)
        """
        # Configuration centralisée
        self.config = AZRConfig(config_path) if config_path else AZRConfig()
        
        # Gestionnaire principal des rollouts
        self.manager = AZRRolloutManager(self.config)
        
        # Environnement BCT
        self.environment = BaccaratEnvironment(self.config)
        
        # Adaptateur pour bct.py
        self.bct_adapter = BCTAdapter(self.manager, self.environment)
        
        # Validation intégrée
        self.validation = self.manager.validation_manager
        
    def execute_prediction_cycle(self, game_context: dict) -> dict:
        """
        Exécute un cycle complet de prédiction AZR BCT
        
        Args:
            game_context: Contexte du jeu (historique, état actuel, etc.)
            
        Returns:
            dict: Résultats complets avec prédiction S/O finale
        """
        return self.manager.execute_self_play_cycle(game_context)
    
    def get_so_prediction(self, game_context: dict) -> str:
        """
        Interface simplifiée pour obtenir prédiction S/O
        
        Args:
            game_context: Contexte du jeu
            
        Returns:
            str: Prédiction 'S' ou 'O'
        """
        results = self.execute_prediction_cycle(game_context)
        return results.get('rollout_3_results', {}).get('final_so_prediction', 'S')
    
    def get_validation_report(self) -> dict:
        """
        Rapport complet de validation du système
        
        Returns:
            dict: Métriques de performance et validation
        """
        return self.validation.get_validation_summary()
    
    def integrate_with_bct(self, bct_manager):
        """
        Intègre le système AZR avec bct.py
        
        Args:
            bct_manager: Instance du BCTManager de bct.py
        """
        return self.bct_adapter.integrate_with_bct_manager(bct_manager)

# ============================================================================
# 🔧 FONCTIONS UTILITAIRES D'INITIALISATION
# ============================================================================

def create_azr_system(config_path: str = None) -> AZRBCTSystem:
    """
    Factory function pour créer une instance du système AZR BCT
    
    Args:
        config_path: Chemin vers configuration (optionnel)
        
    Returns:
        AZRBCTSystem: Instance configurée du système
    """
    return AZRBCTSystem(config_path)

def quick_prediction(game_context: dict, config_path: str = None) -> str:
    """
    Fonction rapide pour obtenir une prédiction S/O
    
    Args:
        game_context: Contexte du jeu
        config_path: Configuration (optionnel)
        
    Returns:
        str: Prédiction 'S' ou 'O'
    """
    system = create_azr_system(config_path)
    return system.get_so_prediction(game_context)

# ============================================================================
# 📊 EXPORTS PUBLICS
# ============================================================================

__all__ = [
    # Interface principale
    'AZRBCTSystem',
    'create_azr_system',
    'quick_prediction',
    
    # Core classes
    'BaseAZRRollout',
    'AZRRolloutManager',
    'AZRConfig',
    
    # Rollouts spécialisés
    'MultidimensionalAnalyzerRollout',
    'SophisticatedHypothesisGeneratorRollout',
    'ContinuityDiscontinuityMasterPredictorRollout',
    
    # Validation
    'AZRValidationMetrics',
    'AZRValidationManager',
    
    # Math engine
    'AZRMathEngine',
    'AZRRewardSystem',
    
    # Environment
    'BaccaratEnvironment',
    'BCTAdapter'
]

# ============================================================================
# 📋 INFORMATIONS VERSION
# ============================================================================

__version__ = "2.0.0"
__author__ = "AZR System adapté BCT"
__description__ = "Système AZR 3 Rollouts pour Baccarat Counting Tool - Architecture Modulaire"
__architecture__ = "Modulaire (vs Monolithe rollouts.py)"

# ============================================================================
# 🚀 EXEMPLE D'UTILISATION
# ============================================================================

if __name__ == "__main__":
    # Exemple d'utilisation simple
    print("🧠 AZR BCT System - Architecture Modulaire")
    print(f"Version: {__version__}")
    print(f"Architecture: {__architecture__}")
    
    # Créer le système
    system = create_azr_system()
    
    # Exemple de contexte de jeu
    game_context = {
        'game_history': ['P', 'B', 'P', 'T', 'B'],
        'current_state': 'SYNC',
        'index1': [4, 5, 4, 0, 6],
        'index2': ['SYNC', 'DESYNC', 'SYNC', 'TIE', 'DESYNC']
    }
    
    # Obtenir prédiction
    prediction = system.get_so_prediction(game_context)
    print(f"Prédiction S/O: {prediction}")
    
    # Rapport de validation
    validation = system.get_validation_report()
    print(f"Learnability Score: {validation['kpis_performance']['learnability_score']:.3f}")
    print(f"Accuracy Score: {validation['kpis_performance']['accuracy_score']:.3f}")
