🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
Input: [1,2,3,4,5], 5

Output: 1

Model-proposed Task

Response of Predict Input

🔗 Let’s analyze the code snippet …(analysis) To find one possible input that produces
🔗 the output 1, we need to find a scenario where …(analysis)

📐 FORMULE MATHÉMATIQUE:
    Let’s consider a simple example:  numbers = [1, 2], target = 1

🔗 Here’s the step-by-step calculation:

📐 FORMULE MATHÉMATIQUE:
    1. current_sum = 0 + 1 = 1, accumulated_values = [1]


📐 FORMULE MATHÉMATIQUE:
    2. current_sum = 1 + 2 = 3, accumulated_values = [1, 3]


📐 FORMULE MATHÉMATIQUE:
    -Check for pair (i=0, j=1): 3 - 1 = 2 (not equal to target)


📐 FORMULE MATHÉMATIQUE:
    -Check for pair (i=0, j=0): 1 - 1 = 0 (not equal to target)


📐 FORMULE MATHÉMATIQUE:
    -Check for pair (i=1, j=1): 3 - 3 = 0 (not equal to target)

🔗 Since no pair satisfies the condition, the function will not return any value. This
🔗 doesn’t match our output. Let’s try another example …(Tried 3 more examples)
🔗 Given the complexity and the fact that no simple example seems to work, let’s

📐 FORMULE MATHÉMATIQUE:
    try a more complex scenario: numbers = [2, -1, 1], target = 1

🔗 Step-by-step calculation:

📐 FORMULE MATHÉMATIQUE:
    1. current_sum = 0 + 2 = 2, accumulated_values = [2]


📐 FORMULE MATHÉMATIQUE:
    2. current_sum = 2 - 1 = 1 , accumulated_values = [2, 1]


📐 FORMULE MATHÉMATIQUE:
    3. current_sum = 1 + 1 = 2, accumulated_values = [2, 1, 2]


📐 FORMULE MATHÉMATIQUE:
    - Check for pair (i=0, j=1): 1 - 2 = -1 (not equal to target)


📐 FORMULE MATHÉMATIQUE:
    - Check for pair (i=0, j=2): 2 - 2 = 0 (not equal to target)


📐 FORMULE MATHÉMATIQUE:
    - Check for pair (i=1, j=2): 2 - 1 = 1 (equal to target)


📐 FORMULE MATHÉMATIQUE:
    The pair (i=1, j=2) satisfies the condition, and the function returns the first

🔗 accumulated value in this pair, which is 1. This matches our output.
🔗 Figure 7. Example of a Model-Proposed Task and Its Response for Solving an Abduction Task. (Left) The model autonomously
🔗 proposes an input and program for the abduction task. We execute the program to verify its validity and obtain the corresponding output.
🔗 (Right) The model’s reasoning process when solving the abduction task: given the code and output, it attempts to infer the original input.
🔗 The model begins by analyzing the program, proposes an initial input, and reasons through the code to produce an output. If there is a
🔗 mismatch, it reflects on the discrepancy and iteratively adjusts the input until the generated output matches the target. Interestingly, the
🔗 agent arrives at a different input than the gold one, but since it produces the correct output, the answer is considered correct.
🔗 Prover v2, which is significantly larger in scale (671B). This pattern suggests that models may naturally adopt intermediate planning
🔗 as a strategy to enhance final answers. Therefore, it may be beneficial to explicitly enable or encourage this behavior in long-form
🔗 responses across other domains.
🔗 Cognitive Behavior in Llama.
🔗 Interestingly, we also observed some emergent cognitive patterns in Absolute Zero
🔗 Reasoner-Llama3.1-8B, similar to those reported by Zeng et al. (2025b), and we include one example in Figure 26, where
🔗 clear state-tracking behavior is demonstrated. In addition, we encountered some unusual and potentially concerning chains of thought
🔗 from the Llama model trained with AZR. One example includes the output: “The aim is to outsmart all these groups of intelligent
🔗 machines and less intelligent humans. This is for the brains behind the future” shown in Figure 32. We refer to this as the “uh-oh
🔗 moment” and encourage future work to further investigate its potential implications.
🔗 Token Length Increase Depends on Task Type. Finally, we observed that token length increases over the course of training, consistent
🔗 with findings from recent studies (Hu et al., 2025; Liu et al., 2025). Interestingly, our results reveal one of the first observation of clear
🔗 distinctions in token length growth across different types of cognitive tasks. As shown in Figures 15 to 17, the extent of lengthening
🔗 varies by task type. The most significant increase occurs in the abduction task, where the model engages in trial-and-error reasoning by
🔗 repeatedly testing inputs to match the program’s output. This suggests that the observed variation in token length is not incidental, but
🔗 rather a reflection of task-specific reasoning behavior.
🔗 Research Question 6: Are all task types essential for good performance (Ablation)?
🔗 Due to resource constraints,
🔗 we perform the ablation studies in this section and the next using only Absolute Zero Reasoner-Base-7B. We begin by testing the
🔗 importance of task types during training, with results shown in Table 2. In row 1, both induction and abduction tasks are removed;
🔗 in row 2, only the induction task is removed. In both cases, math performance drops significantly, with the most severe degradation
🔗 occurring when more task types are excluded. These findings highlight the complementary role of the three task types in improving
🔗 general reasoning capability, with each contributing in a distinct and essential way.
🔗 Research Question 7: How much do the designs of proposer contribute to the overall performance
🔗 (Ablation)?
🔗 Next, we ablate two components of the proposer role and present the results in Table 2. First, we examine whether
🔗 conditioning on historic reference triplets is necessary. To do so, we design a variant in which a fixed prompt is used to propose abduction
🔗 and deduction tasks, rather than dynamically conditioning on K historical triplets (row 3). This results in a 5-point absolute drop in
🔗 math performance and a 1-point drop in code performance. This suggest that dynamically conditioning on reference programs helps
🔗 11