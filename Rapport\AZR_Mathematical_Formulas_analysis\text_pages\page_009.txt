🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Model
🔗 Base
🔗 #data
🔗 HEval+
🔗 MBPP+
🔗 LCBv1-5
🔗 AME24
🔗 AME25
🔗 AMC
🔗 M500
🔗 Minva
🔗 Olypiad
🔗 CAvg
🔗 MAvg
🔗 AVG
🔗 Base Models
🔗 Qwen2.5-7B[73]
🔗 -
🔗 -
🔗 73.2
🔗 65.3
🔗 17.5
🔗 6.7
🔗 3.3
🔗 37.5
🔗 64.8
🔗 25.0
🔗 27.7
🔗 52.0
🔗 27.5
🔗 39.8
🔗 Qwen2.5-7B-Ins[73]
🔗 -
🔗 -
🔗 75.0
🔗 68.5
🔗 25.5
🔗 13.3
🔗 6.7
🔗 52.5
🔗 76.4
🔗 35.7
🔗 37.6
🔗 56.3
🔗 37.0
🔗 46.7
🔗 Qwen2.5-7B-Coder[26]
🔗 -
🔗 -
🔗 80.5
🔗 69.3
🔗 19.9
🔗 6.7
🔗 3.3
🔗 40.0
🔗 54.0
🔗 17.3
🔗 21.9
🔗 56.6
🔗 23.9
🔗 40.2
🔗 Qwen2.5-7B-<PERSON>[74]
🔗 -
🔗 -
🔗 61.0
🔗 57.9
🔗 16.2
🔗 10.0
🔗 16.7
🔗 42.5
🔗 64.2
🔗 15.4
🔗 28.0
🔗 45.0
🔗 29.5
🔗 37.3
🔗 Zero-Style Reasoners Trained on Curated Coding Data
🔗 AceCoder-RM[84]
🔗 Ins
🔗 22k
🔗 79.9
🔗 71.4
🔗 23.6
🔗 20.0
🔗 6.7
🔗 50.0
🔗 76.4
🔗 34.6
🔗 36.7
🔗 58.3
🔗 37.4
🔗 47.9
🔗 AceCoder-Rule[84]
🔗 Ins
🔗 22k
🔗 77.4
🔗 69.0
🔗 19.9
🔗 13.3
🔗 6.7
🔗 50.0
🔗 76.0
🔗 37.5
🔗 37.8
🔗 55.4
🔗 36.9
🔗 46.2
🔗 AceCoder-RM[84]
🔗 Coder
🔗 22k
🔗 78.0
🔗 66.4
🔗 27.5
🔗 13.3
🔗 3.3
🔗 27.5
🔗 62.6
🔗 29.4
🔗 29.0
🔗 57.3
🔗 27.5
🔗 42.4
🔗 AceCoder-Rule[84]
🔗 Coder
🔗 22k
🔗 80.5
🔗 70.4
🔗 29.0
🔗 6.7
🔗 6.7
🔗 40.0
🔗 62.8
🔗 27.6
🔗 27.4
🔗 60.0
🔗 28.5
🔗 44.3
🔗 CodeR1-LC2k[36]
🔗 Ins
🔗 2k
🔗 81.7
🔗 71.7
🔗 28.1
🔗 13.3
🔗 10.0
🔗 45.0
🔗 75.0
🔗 33.5
🔗 36.7
🔗 60.5
🔗 35.6
🔗 48.0
🔗 CodeR1-12k[36]
🔗 Ins
🔗 12k
🔗 81.1
🔗 73.5
🔗 29.3
🔗 13.3
🔗 3.3
🔗 37.5
🔗 74.0
🔗 35.7
🔗 36.9
🔗 61.3
🔗 33.5
🔗 47.4
🔗 Zero-Style Reasoners Trained on Curated Math Data
🔗 PRIME-Zero[9]
🔗 Coder
🔗 484k
🔗 49.4
🔗 51.1
🔗 11.0
🔗 23.3
🔗 23.3
🔗 67.5
🔗 81.2
🔗 37.9
🔗 41.8
🔗 37.2
🔗 45.8
🔗 41.5
🔗 SimpleRL-Zoo[85]
🔗 Base
🔗 8.5k
🔗 73.2
🔗 63.2
🔗 25.6
🔗 16.7
🔗 3.3
🔗 57.5
🔗 77.0
🔗 35.7
🔗 41.0
🔗 54.0
🔗 38.5
🔗 46.3
🔗 Oat-Zero[38]
🔗 Math
🔗 8.5k
🔗 62.2
🔗 59.0
🔗 15.2
🔗 30.0
🔗 16.7
🔗 62.5
🔗 80.0
🔗 34.9
🔗 41.6
🔗 45.5
🔗 44.3
🔗 44.9
🔗 ORZ[23]
🔗 Base
🔗 57k
🔗 80.5
🔗 64.3
🔗 22.0
🔗 13.3
🔗 16.7
🔗 60.0
🔗 81.8
🔗 32.7
🔗 45.0
🔗 55.6
🔗 41.6
🔗 48.6
🔗 Absolute Zero Training w/ No Curated Data (Ours)
🔗 AZR (Ours)
🔗 Base
🔗 0
🔗 71.3
🔗 -1.9
🔗 69.1
🔗 +3.8
🔗 25.3
🔗 +7.8
🔗 13.3
🔗 +6.6
🔗 13.3
🔗 +10.0
🔗 52.5
🔗 +15.0 74.4
🔗 +9.6
🔗 38.2
🔗 +13.2
🔗 38.5
🔗 +10.8
🔗 55.2
🔗 +3.2
🔗 38.4
🔗 +10.9 46.8
🔗 +7.0
🔗 AZR (Ours)
🔗 Coder
🔗 0
🔗 83.5
🔗 +3.0
🔗 69.6
🔗 +0.3
🔗 31.7
🔗 +11.8
🔗 20.0
🔗 +13.3
🔗 10.0
🔗 +6.7
🔗 57.5
🔗 +17.5 72.6
🔗 +22.6
🔗 36.4
🔗 +19.1
🔗 38.2
🔗 +16.3
🔗 61.6
🔗 +5.0
🔗 39.1
🔗 +15.2 50.4
🔗 +10.2
🔗 Table 1. Performance of RL-Trained Reasoner on Reasoning Benchmarks Based on Qwen2.5-7B Models. Performance of various
🔗 models is evaluated on three standard code benchmarks (HumanEval+, MBPP+, LCBv1-5 and six math benchmarks (AIME’24, AIME’25,
🔗 AMC’23, MATH500, Minerva, OlympiadBench). Average performance across coding and math benchmarks is calculated as average of
🔗 the two averages: AVG = (CAvg + MAvg)/2. We use + for absolute percentage increase from base model. All models are trained using
🔗 different variants of the Qwen2.5-7B model, with the variant and data usage labeled, more details listed in Table 4
🔗 Baselines.
🔗 For our main results, we use Qwen2.5-7B as the base model, along with its specialized base model variants:
🔗 Qwen2.5-7B-Coder, Qwen2.5-7B-Instruct, and Qwen2.5-Math-7B (Yang et al., 2024a; Hui et al., 2024; Yang et al., 2024b).
🔗 Furthermore, the zero-style models are usually trained specifically on either code or math data; and only Eurus-2-7B-PRIME-Zero(Cui
🔗 et al., 2025) was trained jointly on both domains. For code data models, we present four variants of the AceCoder (Zeng et al., 2025a)
🔗 and two different CodeR1 models (Liu & Zhang, 2025). For math data models, we have Qwen2.5-Math-7B-Oat-Zero (Liu et al.,
🔗 2025), Open-Reasoner-Zero-7B (ORZ) (Hu et al., 2025), Qwen-2.5-7B-SimpleRL-Zoo (Zeng et al., 2025b). All baseline models’
🔗 training data and initialization settings are summarized in Table 4. For follow-up scaling experiments, we compare each AZR model
🔗 against its own corresponding base model, due to the lack of established baselines across different parameter scales. Finally, we compare
🔗 our Llama3.1-8B-trained model with Llama-3.1-8B-SimpleRL-Zoo (Zeng et al., 2025b) and the base model.
🔗 4.2. Results
🔗 Research Question 1: How does AZR compare to other zero setting models trained with human expert
🔗 data?
🔗 We present the main results of reasoning models trained under both the standard zero and our proposed absolute zero settings
🔗 in Table 1. Notably, Absolute Zero Reasoner-Coder-7B achieves state-of-the-art performance in both the 7B overall average and
🔗 the coding average categories. Despite being entirely out-of-distribution for both math and code reasoning benchmarks, it surpasses the
🔗 previous best model by 1.8 absolute percentages. Even more strikingly, it outperforms models trained with expert-curated human data in
🔗 the coding category by 0.3 absolute percentages, while never having access to such data itself.
🔗 Strong Cross-domain Generalization. To assess cross-domain generalization after RLVR, we evaluate math performance before and
🔗 after training, comparing AZR models with other expert code models, since AZR was trained in coding environments. After training,
🔗 most expert code models showed minimal changes or even declines in performance compared to their base versions, with an average
🔗 increase of only 0.65 points across these models, indicating very limited cross-domain generalization. In contrast, AZR base and coder
🔗 models achieved gains of 10.9 and 15.2 percentage points, respectively, demonstrating substantially stronger generalized reasoning
🔗 improvements. Similarly, although also out-of-distribution on human-defined code generation tasks, our AZR models improved by 3.2
🔗 and 5.0 points, while the math models on average showed just a moderate increases in coding (+2.0 on average).
🔗 Overall, these results highlight the surprising effectiveness of our approach. Unlike other RLVR models trained and evaluated on
🔗 human-defined tasks, our AZR models demonstrate strong general reasoning capabilities without any direct training on downstream
🔗 human-defined math or coding data, only had access to self-proposed tasks during training.
🔗 Research Question 2: How do initializing from different base model variants (base vs. coder) affect
🔗 performance?
🔗 As shown in Table 1, the coder variant achieved better overall performance in both math and coding after the AZR
🔗 9