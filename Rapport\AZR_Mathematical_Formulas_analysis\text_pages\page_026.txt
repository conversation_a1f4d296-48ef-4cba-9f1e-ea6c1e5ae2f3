🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 def f(numbers):
🔗 # Step 1: Filter out even numbers
🔗 filtered_numbers = [num for num in numbers if num % 2 != 0]
🔗 # Step 2: Calculate the sum of the remaining odd numbers
🔗 sum_of_odd_numbers = sum(filtered_numbers)
🔗 # Step 3: Reverse the order of the remaining odd numbers
🔗 reversed_odd_numbers = filtered_numbers[::-1]
🔗 # Step 4: Calculate the product of the reversed odd numbers
🔗 product_of_reversed_odd_numbers = 1
🔗 for num in reversed_odd_numbers:
🔗 product_of_reversed_odd_numbers *= num
🔗 # Step 5: Calculate the sum of the digits of the product
🔗 sum_of_digits_of_product = sum(int(digit) for digit in str(product_of_reversed_odd_numbers))
🔗 # Step 6: Modify the original list by adding the sum of the digits to each even number
🔗 # and subtracting it from each odd number
🔗 modified_numbers = []
🔗 for num in numbers:
🔗 if num % 2 == 0:
🔗 modified_numbers.append(num + sum_of_digits_of_product)
🔗 else:
🔗 modified_numbers.append(num - sum_of_digits_of_product)
🔗 # Step 7: Return the modified list
🔗 return modified_numbers
🔗 Figure 19. Learned “ReAct” style program generation. We observe that even after outputting the CoT and during answering when the
🔗 answer requires outputting code, the model usually uses comments in code to interleave its thinking and “acting”, an example is shown
🔗 here for induction task output by Absolute Zero Reasoner-Coder-14b during training. Similar observations are recently observed
🔗 in much bigger models (671b) for math proofs with DeepSeek Prover V2 (Ren et al., 2025).
🔗 25
🔗 Figure 19. Learned “ReAct” style program generation. We observe that even after outputting the CoT and during answering when the
🔗 answer requires outputting code, the model usually uses comments in code to interleave its thinking and “acting”, an example is shown
🔗 here for induction task output by Absolute Zero Reasoner-Coder-14b during training. Similar observations are recently observed in
🔗 much bigger models (671b) for math proofs with DeepSeek Prover V2 (Ren et al., 2025).
🔗 26