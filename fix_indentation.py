#!/usr/bin/env python3
"""
Script pour corriger automatiquement l'indentation du fichier Rollouts.py
Convertit toutes les indentations en 4 espaces standard Python
"""

import re

def fix_indentation(file_path):
    """Corrige l'indentation d'un fichier Python"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    current_indent = 0
    
    for line_num, line in enumerate(lines, 1):
        # Supprimer les espaces de fin
        line = line.rstrip() + '\n'
        
        # Si la ligne est vide, la garder telle quelle
        if not line.strip():
            fixed_lines.append(line)
            continue
            
        # Compter l'indentation actuelle (espaces au début)
        original_indent = len(line) - len(line.lstrip())
        content = line.lstrip()
        
        # Déterminer le niveau d'indentation correct
        if content.startswith('class '):
            current_indent = 0
        elif content.startswith('def ') and not content.startswith('def __'):
            # Méthode de classe
            if current_indent == 0:  # Fonction globale
                current_indent = 0
            else:  # Méthode de classe
                current_indent = 4
        elif content.startswith('def __'):
            # Méthode spéciale de classe
            current_indent = 4
        elif content.startswith('@'):
            # Décorateur
            if current_indent == 0:
                current_indent = 0
            else:
                current_indent = 4
        elif content.startswith('"""') or content.startswith("'''"):
            # Docstring - garder l'indentation actuelle
            pass
        elif content.startswith('#'):
            # Commentaire - garder l'indentation actuelle
            pass
        elif content.strip() in ['pass', 'return', 'break', 'continue']:
            # Instructions simples dans une méthode
            if current_indent == 0:
                current_indent = 4
            else:
                current_indent = 8
        elif content.startswith('if ') or content.startswith('for ') or content.startswith('while ') or content.startswith('try:') or content.startswith('with '):
            # Structures de contrôle
            if current_indent == 4:  # Dans une méthode
                current_indent = 8
            elif current_indent == 0:  # Au niveau global
                current_indent = 4
        elif content.startswith('else:') or content.startswith('elif ') or content.startswith('except') or content.startswith('finally:'):
            # Clauses else/elif/except
            if current_indent >= 8:
                current_indent = 8
            else:
                current_indent = 4
        elif original_indent > 0:
            # Contenu indenté - ajuster selon le contexte
            if current_indent == 4:  # Dans une méthode
                if content.startswith('return ') or content.startswith('self.'):
                    current_indent = 8
                else:
                    current_indent = 8
            elif current_indent == 0:  # Au niveau global
                current_indent = 4
        
        # Appliquer l'indentation corrigée
        fixed_line = ' ' * current_indent + content
        fixed_lines.append(fixed_line)
        
        print(f"Ligne {line_num}: {original_indent} -> {current_indent} espaces")
    
    # Écrire le fichier corrigé
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Indentation corrigée dans {file_path}")

if __name__ == "__main__":
    fix_indentation("Rollouts.py")
    print("Correction terminée!")
