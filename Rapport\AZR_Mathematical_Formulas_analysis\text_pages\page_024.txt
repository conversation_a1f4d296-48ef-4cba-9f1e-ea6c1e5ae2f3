🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
0

40

80

120

160

200

240

280

  0.0
  0.2
  0.4
  0.6
  0.8
Reward

0

40

80

120

160

200

240

280

1000

2000

3000

4000

Token Length

Training Steps

Abduction Task

Solve

Propose

🔗 Figure 15. Abduction Task Reward and Token Lengths. The task reward and token lengths of the two roles for abduction task type of
🔗 Absolute Zero Reasoner-base-7b.
0

40

80

120

160

200

240

280

  0.0
  0.1
  0.2
  0.3
  0.4
  0.5
  0.6
  0.7
Reward

0

40

80

120

160

200

240

280

250

500

750

1000

1250

1500

1750

2000

Token Length

Training Steps

Induction Task

Solve

Propose

🔗 Figure 16. Induction Task Reward and Token Lengths. The task reward and token lengths of the two roles for induction task type of
🔗 Absolute Zero Reasoner-base-7b.
🔗 24