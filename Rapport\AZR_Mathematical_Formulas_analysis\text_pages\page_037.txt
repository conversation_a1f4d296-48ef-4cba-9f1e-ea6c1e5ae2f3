🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
MATH REASONING

CODE REASONING

OVERALL PERFORMANCE

50

100

150

200

250

300

350

  0.100
  0.150
  0.200
Accuracy

AIME 2024

50

100

150

200

250

300

350

  0.050
  0.100
  0.150
  0.200
AIME 2025

50

100

150

200

250

300

350

  0.250
  0.300
  0.350
  0.400
Olympiad Bench

50

100

150

200

250

300

350

  0.300
  0.350
  0.400
Accuracy

Minerva

50

100

150

200

250

300

350

  0.500
  0.550
  0.600
  0.650
  0.700
  0.750
Math 500

50

100

150

200

250

300

350

  0.450
  0.500
  0.550
  0.600
AMC 2023

50

100

150

200

250

300

350

  0.700
  0.720
  0.740
  0.760
  0.780
Accuracy

HumanEval+

50

100

150

200

250

300

350

  0.680
  0.690
  0.700
  0.710
  0.720
MBPP+

50

100

150

200

250

300

350

  0.320
  0.340
  0.360
LiveCodeBench

50

100

150

200

250

300

350

  0.300
  0.350
  0.400
Accuracy

Math Average

50

100

150

200

250

300

350

  0.570
  0.580
  0.590
  0.600
  0.610
  0.620
Code Average

50

100

150

200

250

300

350

  0.440
  0.460
  0.480
  0.500
Overall Average

🔗 Figure 30. Absolute Zero Reasoner-base-14b OOD Performance Breakdown.
🔗 37