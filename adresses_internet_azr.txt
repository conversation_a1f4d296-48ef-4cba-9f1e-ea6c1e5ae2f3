# TOUTES LES ADRESSES INTERNET EXTRAITES DU FICHIER HTML AZR
# Fichier source: analyse_rollouts/AZR/Absolute Zero_ Reinforced Self-play Reasoning with Zero Data.html
# Date d'extraction: 2025-06-15
# Méthode: Ouverture dans navigateur + analyse complète du code HTML

## RÉSUMÉ EXÉCUTIF:
Après ouverture du fichier dans un navigateur et analyse complète, voici TOUTES les adresses internet présentes dans le fichier HTML, telles qu'elles apparaissent:

## 1. LIENS CSS ET RESSOURCES EXTERNES (1 lien):
https://cdn.mathpix.com/fonts/cmu.css

## 2. LIENS VERS DES IMAGES ET FIGURES (5 liens visibles dans le navigateur):

### Figure 27 (Metrics on Proposed Tasks):
https://cdn.mathpix.com/cropped/2025_06_13_d6d741aed439cc3501d5g-34.jpg?height=551&width=1606&top_left_y=1806&top_left_x=208

### Figure 28 (Math Reasoning - Performance Breakdown):
https://cdn.mathpix.com/cropped/2025_06_13_d6d741aed439cc3501d5g-35.jpg?height=1413&width=1719&top_left_y=643&top_left_x=176

### Figure 29 (Absolute Zero Reasoner-Coder-7b OOD Performance):
https://cdn.mathpix.com/cropped/2025_06_13_d6d741aed439cc3501d5g-36.jpg?height=1397&width=1717&top_left_y=651&top_left_x=177

### Figure 30 (Absolute Zero Reasoner-base-14b OOD Performance):
https://cdn.mathpix.com/cropped/2025_06_13_d6d741aed439cc3501d5g-37.jpg?height=1413&width=1725&top_left_y=643&top_left_x=176

### Figure 31 (Absolute Zero Reasoner-Coder-14b OOD Performance):
https://cdn.mathpix.com/cropped/2025_06_13_d6d741aed439cc3501d5g-38.jpg?height=1408&width=1725&top_left_y=315&top_left_x=173

## 3. LIENS TECHNIQUES/NAMESPACES (non visibles à l'utilisateur):

### MathML Namespace (répété 1164+ fois):
http://www.w3.org/1998/Math/MathML

### SVG Namespace (dans les formules mathématiques):
http://www.w3.org/2000/svg

## ANALYSE DÉTAILLÉE:

### LIENS VISIBLES DANS LE NAVIGATEUR:
- **1 lien CSS** pour les polices MathPix
- **5 liens d'images** pour les figures et graphiques
- **Total: 6 liens visibles**

### LIENS TECHNIQUES (non visibles):
- **1164+ occurrences** de http://www.w3.org/1998/Math/MathML (namespace MathML)
- **Nombreuses occurrences** de http://www.w3.org/2000/svg (namespace SVG)

## CONSTATATION IMPORTANTE:

**AUCUNE SECTION "REFERENCES" BIBLIOGRAPHIQUES TRADITIONNELLE N'EST PRÉSENTE**

Le fichier HTML ne contient PAS de:
- Liens vers des articles académiques
- Liens vers des sites web de recherche
- Liens vers des ressources bibliographiques externes
- Section "References" ou "Bibliography" avec des URLs

## NATURE DU DOCUMENT:

Ce fichier HTML semble être:
- Un document technique/présentation sur AZR (Absolute Zero Reasoner)
- Contenant principalement des formules mathématiques (MathML)
- Des figures et graphiques (images hébergées)
- Du code et des exemples
- Mais SANS bibliographie académique traditionnelle

## CONCLUSION:

Les seules adresses internet réellement présentes et visibles dans le navigateur sont:
1. Les ressources techniques (CSS, images)
2. Les namespaces XML/SVG (techniques)

Il n'y a PAS de liens vers des sources externes, articles, ou références bibliographiques dans ce document.
