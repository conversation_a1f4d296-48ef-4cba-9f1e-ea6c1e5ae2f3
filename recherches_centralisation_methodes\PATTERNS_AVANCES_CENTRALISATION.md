# PATTERNS AVANCÉS DE CENTRALISATION DES MÉTHODES

## 🚀 **TECHNIQUES AVANCÉES IDENTIFIÉES**

### **1. Decorator-Based Method Registry**
**Source :** Recherches sur les systèmes de plugins Python

```python
class MethodRegistry:
    """Registry avec décorateurs pour enregistrement automatique"""
    
    def __init__(self):
        self._methods = {}
        self._configs = {}
    
    def register(self, method_id: str, default_config: dict = None):
        """Décorateur pour enregistrement automatique"""
        def decorator(method_class):
            self._methods[method_id] = method_class
            if default_config:
                self._configs[method_id] = default_config
            return method_class
        return decorator
    
    def get_method(self, method_id: str):
        return self._methods.get(method_id)
    
    def execute(self, method_id: str, config_override: dict = None, **kwargs):
        method_class = self.get_method(method_id)
        if not method_class:
            raise ValueError(f"Method {method_id} not registered")
        
        # Fusion configuration
        config = self._configs.get(method_id, {}).copy()
        if config_override:
            config.update(config_override)
        
        # Exécution
        instance = method_class.from_config(config)
        return instance.execute(**kwargs)

# Registry global
azr_registry = MethodRegistry()

# Usage avec décorateurs
@azr_registry.register('cluster_default', {
    'rollouts': 3,
    'batch_size': 64,
    'coordination': 'sequential'
})
class ClusterDefaultMethod(UniversalAZRMethod):
    # Implémentation...
    pass

@azr_registry.register('rollout_monte_carlo', {
    'n_samples': 8,
    'temperature': 1.0
})
class MonteCarloRolloutMethod(UniversalAZRMethod):
    # Implémentation...
    pass
```

### **2. Context Manager pour Configurations Temporaires**

```python
from contextlib import contextmanager

class ConfigurableMethodManager:
    """Manager avec support de configurations temporaires"""
    
    def __init__(self, base_config):
        self.base_config = base_config
        self._config_stack = []
    
    @contextmanager
    def temporary_config(self, config_override: dict):
        """Context manager pour configuration temporaire"""
        # Sauvegarder config actuelle
        current_config = self.base_config.copy()
        self._config_stack.append(current_config)
        
        # Appliquer override temporaire
        self.base_config.update(config_override)
        
        try:
            yield self
        finally:
            # Restaurer config précédente
            self.base_config = self._config_stack.pop()
    
    def execute_method(self, method_id: str, **kwargs):
        """Exécution avec configuration actuelle"""
        return azr_registry.execute(method_id, self.base_config, **kwargs)

# Usage
manager = ConfigurableMethodManager(base_config)

# Configuration temporaire pour tests
with manager.temporary_config({'batch_size': 32, 'temperature': 0.8}):
    result = manager.execute_method('cluster_default', data=test_data)
    # batch_size=32 et temperature=0.8 utilisés temporairement

# Retour à la configuration de base automatiquement
result = manager.execute_method('cluster_default', data=prod_data)
```

### **3. Dependency Injection avec Type Hints**

```python
from typing import Protocol, TypeVar, Generic
from dataclasses import dataclass

class ConfigProtocol(Protocol):
    """Protocol pour configurations"""
    def get_config(self, method_id: str, config_id: str) -> dict: ...
    def validate_config(self, config: dict) -> bool: ...

class MethodProtocol(Protocol):
    """Protocol pour méthodes universelles"""
    def execute(self, **kwargs): ...
    @classmethod
    def from_config(cls, config: dict): ...

T = TypeVar('T', bound=MethodProtocol)

class TypedMethodFactory(Generic[T]):
    """Factory typée pour méthodes"""
    
    def __init__(self, method_class: type[T], config_provider: ConfigProtocol):
        self.method_class = method_class
        self.config_provider = config_provider
    
    def create_method(self, config_id: str) -> T:
        """Création typée de méthode"""
        config = self.config_provider.get_config(
            self.method_class.__name__.lower(), 
            config_id
        )
        
        if not self.config_provider.validate_config(config):
            raise ValueError(f"Invalid config for {config_id}")
        
        return self.method_class.from_config(config)

# Usage avec types
@dataclass
class ClusterConfig:
    rollouts: int
    batch_size: int
    temperature: float

cluster_factory = TypedMethodFactory[ClusterDefaultMethod](
    ClusterDefaultMethod, 
    azr_config
)

cluster_method = cluster_factory.create_method('default')
```

### **4. Async Method Execution avec Centralisation**

```python
import asyncio
from typing import Awaitable

class AsyncMethodRegistry:
    """Registry pour méthodes asynchrones"""
    
    def __init__(self):
        self._sync_methods = {}
        self._async_methods = {}
        self._configs = {}
    
    def register_sync(self, method_id: str, method_class: type, config: dict = None):
        """Enregistrement méthode synchrone"""
        self._sync_methods[method_id] = method_class
        if config:
            self._configs[method_id] = config
    
    def register_async(self, method_id: str, method_class: type, config: dict = None):
        """Enregistrement méthode asynchrone"""
        self._async_methods[method_id] = method_class
        if config:
            self._configs[method_id] = config
    
    async def execute_async(self, method_id: str, config_override: dict = None, **kwargs):
        """Exécution asynchrone"""
        if method_id in self._async_methods:
            method_class = self._async_methods[method_id]
            config = self._merge_config(method_id, config_override)
            instance = method_class.from_config(config)
            return await instance.execute_async(**kwargs)
        
        elif method_id in self._sync_methods:
            # Wrapper async pour méthodes sync
            method_class = self._sync_methods[method_id]
            config = self._merge_config(method_id, config_override)
            instance = method_class.from_config(config)
            
            # Exécution dans thread pool
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, instance.execute, **kwargs)
        
        else:
            raise ValueError(f"Method {method_id} not found")
    
    async def execute_parallel(self, method_configs: list) -> list:
        """Exécution parallèle de plusieurs méthodes"""
        tasks = []
        for method_config in method_configs:
            method_id = method_config['method_id']
            config_override = method_config.get('config', {})
            kwargs = method_config.get('kwargs', {})
            
            task = self.execute_async(method_id, config_override, **kwargs)
            tasks.append(task)
        
        return await asyncio.gather(*tasks)

# Usage async
async_registry = AsyncMethodRegistry()

# Exécution parallèle des 3 rollouts
rollout_configs = [
    {'method_id': 'rollout_online', 'kwargs': {'data': data}},
    {'method_id': 'rollout_monte_carlo', 'kwargs': {'data': data}},
    {'method_id': 'rollout_validation', 'kwargs': {'data': data}}
]

results = await async_registry.execute_parallel(rollout_configs)
```

### **5. Configuration Validation avec Schemas**

```python
from typing import Dict, Any
import jsonschema

class SchemaValidatedConfig:
    """Configuration avec validation par schémas"""
    
    def __init__(self):
        self._schemas = {}
        self._configs = {}
    
    def register_schema(self, method_id: str, schema: dict):
        """Enregistrement schéma de validation"""
        self._schemas[method_id] = schema
    
    def set_config(self, method_id: str, config_id: str, config: dict):
        """Configuration avec validation automatique"""
        if method_id in self._schemas:
            try:
                jsonschema.validate(config, self._schemas[method_id])
            except jsonschema.ValidationError as e:
                raise ValueError(f"Invalid config for {method_id}: {e.message}")
        
        if method_id not in self._configs:
            self._configs[method_id] = {}
        self._configs[method_id][config_id] = config
    
    def get_config(self, method_id: str, config_id: str) -> dict:
        """Récupération configuration validée"""
        if method_id not in self._configs:
            raise ValueError(f"No configs for method {method_id}")
        if config_id not in self._configs[method_id]:
            raise ValueError(f"Config {config_id} not found for {method_id}")
        
        return self._configs[method_id][config_id].copy()

# Schémas de validation
cluster_schema = {
    "type": "object",
    "properties": {
        "rollouts": {
            "type": "array",
            "minItems": 1,
            "maxItems": 10,
            "items": {
                "type": "object",
                "properties": {
                    "type": {"type": "string", "enum": ["online", "monte_carlo", "validation"]},
                    "config_id": {"type": "string"}
                },
                "required": ["type", "config_id"]
            }
        },
        "batch_size": {"type": "integer", "minimum": 1, "maximum": 1024},
        "temperature": {"type": "number", "minimum": 0.1, "maximum": 2.0}
    },
    "required": ["rollouts", "batch_size"]
}

rollout_schema = {
    "type": "object",
    "properties": {
        "n_samples_accuracy": {"type": "integer", "minimum": 1, "maximum": 100},
        "temperature": {"type": "number", "minimum": 0.1, "maximum": 2.0},
        "top_p": {"type": "number", "minimum": 0.1, "maximum": 1.0}
    },
    "required": ["n_samples_accuracy", "temperature"]
}

# Usage avec validation
validated_config = SchemaValidatedConfig()
validated_config.register_schema('cluster', cluster_schema)
validated_config.register_schema('rollout', rollout_schema)

# Configuration automatiquement validée
validated_config.set_config('cluster', 'default', {
    'rollouts': [
        {'type': 'online', 'config_id': 'online_default'},
        {'type': 'monte_carlo', 'config_id': 'mc_default'}
    ],
    'batch_size': 64,
    'temperature': 1.0
})
```

### **6. Method Chaining avec Configuration Fluide**

```python
class FluentMethodBuilder:
    """Builder fluide pour configuration et exécution"""
    
    def __init__(self, registry):
        self.registry = registry
        self._method_id = None
        self._config = {}
        self._kwargs = {}
    
    def method(self, method_id: str):
        """Sélection de méthode"""
        self._method_id = method_id
        return self
    
    def config(self, **config_params):
        """Configuration fluide"""
        self._config.update(config_params)
        return self
    
    def with_params(self, **kwargs):
        """Paramètres d'exécution"""
        self._kwargs.update(kwargs)
        return self
    
    def execute(self):
        """Exécution finale"""
        if not self._method_id:
            raise ValueError("No method selected")
        
        return self.registry.execute(
            self._method_id, 
            self._config, 
            **self._kwargs
        )
    
    def reset(self):
        """Reset pour réutilisation"""
        self._method_id = None
        self._config = {}
        self._kwargs = {}
        return self

# Usage fluide
builder = FluentMethodBuilder(azr_registry)

result = (builder
    .method('cluster_default')
    .config(batch_size=32, temperature=0.8)
    .with_params(data=input_data, mode='test')
    .execute())

# Réutilisation
result2 = (builder
    .reset()
    .method('rollout_monte_carlo')
    .config(n_samples_accuracy=16)
    .with_params(task=task_data)
    .execute())
```

---

## 🎯 **RECOMMANDATIONS D'INTÉGRATION POUR AZR**

### **Pattern Optimal pour AZR :**
```python
# Combinaison des meilleurs patterns
class AZRMethodSystem:
    """Système complet de méthodes AZR"""
    
    def __init__(self, config_provider: AZRConfig):
        self.config = config_provider
        self.registry = MethodRegistry()
        self.validator = SchemaValidatedConfig()
        self.async_registry = AsyncMethodRegistry()
        self._setup_system()
    
    def _setup_system(self):
        """Setup complet du système"""
        # Enregistrement automatique via décorateurs
        # Validation par schémas
        # Support async pour performance
        pass
    
    def execute_universal(self, method_id: str, config_id: str, **kwargs):
        """Point d'entrée universel"""
        return self.registry.execute(method_id, 
                                   self.config.get_method_config(method_id, config_id),
                                   **kwargs)
    
    async def execute_cluster_async(self, cluster_id: str, data):
        """Exécution cluster asynchrone"""
        cluster_config = self.config.get_cluster_config(cluster_id)
        rollout_configs = [
            {'method_id': f"rollout_{r['type']}", 'config': self.config.get_rollout_config(r['config_id']), 'kwargs': {'data': data}}
            for r in cluster_config['rollouts']
        ]
        return await self.async_registry.execute_parallel(rollout_configs)
```

Cette approche combine :
- ✅ **Décorateurs** pour enregistrement automatique
- ✅ **Validation** par schémas
- ✅ **Async** pour performance
- ✅ **Type safety** avec protocols
- ✅ **Fluent interface** pour facilité d'usage
- ✅ **Centralisation totale** des configurations
