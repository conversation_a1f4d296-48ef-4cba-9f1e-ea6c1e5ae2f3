🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 ## Task: Output {NUM_INPUTS} Inputs that can be plugged into the following Code Snippet to
🔗 produce diverse Outputs, and give a message related to the given snippet.

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 Using the code snippet provided below, design {NUM_INPUTS} inputs that can be plugged into the
🔗 code snippet to produce a diverse set of outputs. A subset of your given input and its
🔗 deterministically produced outputs will be given to a test subject to deduce the function,
🔗 which is meant to be an I.Q. test. You can also leave a message to the test subject to help
🔗 them deduce the code snippet.

📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→

🔗 ### Input Requirements:
🔗 - Provide {NUM_INPUTS} valid inputs for the code snippet
🔗 - For each input, format multiple arguments with commas between them
🔗 - Remember to add quotes around string arguments
🔗 - Each input should be individually wrapped in ```input``` tags
🔗 ### Message Requirements:
🔗 - Leave a message to the test subject to help them deduce the code snippet
🔗 - The message should be wrapped in ```message``` tags
🔗 - The message can be in any form, can even be formed into a coding question, or a natural
🔗 language instruction what the code snippet does

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 - You cannot provide the code snippet in the message
🔗 ### Formatting:
🔗 - Format your input with:
🔗 ```input
🔗 arg1, arg2, ...
🔗 ```
🔗 ### Example Format:
🔗 ```input
🔗 'John', {{'age': 20, 'city': 'New York'}}
🔗 ```
🔗 ```input
🔗 'Sammy', {{'age': 37, 'city': 'Los Angeles'}}
🔗 ```
🔗 ### Evaluation Criteria:
🔗 - Executability, your code should be executable given your inputs
🔗 - Coverage, the inputs and outputs should cover the whole input space of the code snippet, able
🔗 to deduce the code snippet from the inputs and outputs

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 - Creativity, the inputs need to be sufficiently different from each other
🔗 - The overall selection of inputs and message combined should be challenging for the test
🔗 subject, but not impossible for them to solve

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 First, carefully devise a clear plan: e.g., understand the code snippet, then identify how your
🔗 proposed inputs have high coverage, and why the inputs will be challenging and creative.
🔗 Then, write the inputs and message. Remember to wrap your inputs in ```input``` tags, and
🔗 your message in ```message``` tags.

📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→

🔗 ### Code Snippet:
🔗 ```python
🔗 {SNIPPET_FROM_BUFFER}
🔗 ```
🔗 Figure 36. Program Induction Task—Problem Proposal Instruction.
🔗 43
🔗 Figure 36. Program Induction Task—Problem Proposal Instruction.
🔗 42