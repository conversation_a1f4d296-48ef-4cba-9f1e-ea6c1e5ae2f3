# Cas d'Usage Avancés pour la Conversion LaTeX vers Python

## 1. APPLICATIONS EN MACHINE LEARNING

### Conversion de formules de loss functions
```python
# Exemple: Cross-entropy loss
latex_loss = r"-\sum_{i=1}^{n} y_i \log(\hat{y}_i)"

def convert_ml_formula(latex_str):
    """Conversion spécialisée pour ML"""
    from latex2sympy2 import latex2sympy
    import sympy as sp
    
    # Préprocessing pour ML
    ml_replacements = {
        r'\\hat\{([^}]+)\}': r'\1_hat',  # Variables prédites
        r'\\bar\{([^}]+)\}': r'\1_mean', # Moyennes
        r'\\tilde\{([^}]+)\}': r'\1_tilde', # Variables transformées
    }
    
    processed = latex_str
    for pattern, replacement in ml_replacements.items():
        import re
        processed = re.sub(pattern, replacement, processed)
    
    return latex2sympy(processed)

# Conversion vers PyTorch/TensorFlow
def sympy_to_pytorch(sympy_expr):
    """Conversion SymPy vers PyTorch"""
    import torch
    from sympy.utilities.lambdify import lambdify
    
    # Extraction des variables
    variables = list(sympy_expr.free_symbols)
    
    # Création de la fonction PyTorch
    torch_func = lambdify(variables, sympy_expr, 'torch')
    
    return torch_func, variables
```

### Dérivation automatique pour backpropagation
```python
def create_gradient_function(latex_formula, var_name):
    """Création de fonctions de gradient à partir de LaTeX"""
    from sympy import diff, symbols
    
    # Conversion LaTeX vers SymPy
    expr = latex2sympy(latex_formula)
    
    # Variable par rapport à laquelle dériver
    var = symbols(var_name)
    
    # Calcul du gradient
    gradient = diff(expr, var)
    
    # Conversion vers fonction Python
    from sympy.utilities.lambdify import lambdify
    grad_func = lambdify(var, gradient, 'numpy')
    
    return grad_func

# Exemple d'usage
latex_cost = r"\frac{1}{2m}\sum_{i=1}^{m}(h_\theta(x^{(i)}) - y^{(i)})^2"
gradient_func = create_gradient_function(latex_cost, 'theta')
```

## 2. CALCUL SCIENTIFIQUE AVANCÉ

### Équations différentielles
```python
def solve_ode_from_latex(latex_ode, initial_conditions=None):
    """Résolution d'EDO à partir de LaTeX"""
    from sympy import Function, dsolve, symbols, Eq
    
    # Conversion de l'équation différentielle
    # Exemple: \frac{dy}{dx} + y = 0
    
    x, C1 = symbols('x C1')
    y = Function('y')
    
    # Parsing manuel pour les dérivées (latex2sympy a des limitations)
    if r'\frac{dy}{dx}' in latex_ode:
        # Remplacement par notation SymPy
        processed = latex_ode.replace(r'\frac{dy}{dx}', 'y(x).diff(x)')
        
        # Conversion et résolution
        eq = latex2sympy(processed)
        solution = dsolve(eq, y(x))
        
        return solution
    
    return None

# Exemple d'usage
ode_latex = r"\frac{dy}{dx} + 2y = e^{-x}"
solution = solve_ode_from_latex(ode_latex)
```

### Transformées de Laplace et Fourier
```python
def laplace_transform_from_latex(latex_func, t_var='t', s_var='s'):
    """Transformée de Laplace à partir de LaTeX"""
    from sympy import laplace_transform, symbols
    
    t, s = symbols(f'{t_var} {s_var}')
    
    # Conversion de la fonction
    func = latex2sympy(latex_func)
    
    # Calcul de la transformée
    F, a, cond = laplace_transform(func, t, s)
    
    return F, cond

# Exemple
latex_func = r"e^{-at}\sin(\omega t)"
laplace_result, conditions = laplace_transform_from_latex(latex_func)
```

## 3. PHYSIQUE ET INGÉNIERIE

### Mécanique quantique
```python
def quantum_operator_from_latex(latex_op):
    """Conversion d'opérateurs quantiques"""
    from sympy import I, hbar, symbols
    from sympy.physics.quantum import *
    
    # Remplacements spécifiques à la physique quantique
    quantum_replacements = {
        r'\\hat\{H\}': 'H_op',  # Hamiltonien
        r'\\hat\{p\}': 'p_op',  # Impulsion
        r'\\hat\{x\}': 'x_op',  # Position
        r'\\hbar': 'hbar',      # Constante de Planck réduite
        r'\\psi': 'psi',        # Fonction d'onde
    }
    
    processed = latex_op
    for pattern, replacement in quantum_replacements.items():
        import re
        processed = re.sub(pattern, replacement, processed)
    
    return latex2sympy(processed)

# Exemple: Équation de Schrödinger
schrodinger_latex = r"i\hbar\frac{\partial\psi}{\partial t} = \hat{H}\psi"
quantum_eq = quantum_operator_from_latex(schrodinger_latex)
```

### Électromagnétisme
```python
def maxwell_equations_from_latex():
    """Équations de Maxwell en LaTeX vers SymPy"""
    
    maxwell_equations = [
        r"\nabla \cdot \mathbf{E} = \frac{\rho}{\epsilon_0}",  # Gauss
        r"\nabla \cdot \mathbf{B} = 0",                        # Gauss magnétique
        r"\nabla \times \mathbf{E} = -\frac{\partial \mathbf{B}}{\partial t}",  # Faraday
        r"\nabla \times \mathbf{B} = \mu_0\mathbf{J} + \mu_0\epsilon_0\frac{\partial \mathbf{E}}{\partial t}"  # Ampère
    ]
    
    # Conversion avec notation vectorielle
    converted_equations = []
    for eq in maxwell_equations:
        # Préprocessing pour notation vectorielle
        processed = preprocess_vector_notation(eq)
        try:
            sympy_eq = latex2sympy(processed)
            converted_equations.append(sympy_eq)
        except:
            print(f"Conversion échouée pour: {eq}")
    
    return converted_equations

def preprocess_vector_notation(latex_str):
    """Préprocessing pour notation vectorielle"""
    vector_replacements = {
        r'\\mathbf\{([A-Z])\}': r'\1_vec',
        r'\\nabla \\cdot': 'div',
        r'\\nabla \\times': 'curl',
        r'\\nabla': 'grad',
    }
    
    result = latex_str
    for pattern, replacement in vector_replacements.items():
        import re
        result = re.sub(pattern, replacement, result)
    
    return result
```

## 4. FINANCE QUANTITATIVE

### Modèles de pricing d'options
```python
def black_scholes_from_latex():
    """Modèle Black-Scholes à partir de LaTeX"""
    
    # Équation de Black-Scholes
    bs_latex = r"\frac{\partial V}{\partial t} + \frac{1}{2}\sigma^2 S^2 \frac{\partial^2 V}{\partial S^2} + rS\frac{\partial V}{\partial S} - rV = 0"
    
    # Solution analytique pour call européen
    call_latex = r"C = S_0 N(d_1) - Ke^{-rT}N(d_2)"
    
    # Conversion
    bs_eq = latex2sympy(bs_latex)
    call_formula = latex2sympy(call_latex)
    
    return bs_eq, call_formula

def monte_carlo_formula_from_latex(latex_formula):
    """Conversion de formules Monte Carlo"""
    
    # Exemple: Estimation d'espérance
    mc_latex = r"\mathbb{E}[f(X)] \approx \frac{1}{N}\sum_{i=1}^{N} f(X_i)"
    
    # Préprocessing pour notation probabiliste
    prob_replacements = {
        r'\\mathbb\{E\}': 'E',
        r'\\mathbb\{P\}': 'P',
        r'\\mathbb\{V\}': 'Var',
    }
    
    processed = latex_formula
    for pattern, replacement in prob_replacements.items():
        import re
        processed = re.sub(pattern, replacement, processed)
    
    return latex2sympy(processed)
```

## 5. OPTIMISATION ET RECHERCHE OPÉRATIONNELLE

### Programmation linéaire
```python
def linear_program_from_latex(objective_latex, constraints_latex):
    """Conversion de programmes linéaires"""
    from sympy import symbols, Matrix
    
    # Conversion de la fonction objectif
    objective = latex2sympy(objective_latex)
    
    # Conversion des contraintes
    constraints = []
    for constraint in constraints_latex:
        try:
            constraint_expr = latex2sympy(constraint)
            constraints.append(constraint_expr)
        except:
            print(f"Contrainte non convertie: {constraint}")
    
    return objective, constraints

# Exemple d'usage
objective = r"\max \sum_{i=1}^{n} c_i x_i"
constraints = [
    r"\sum_{i=1}^{n} a_{ij} x_i \leq b_j",
    r"x_i \geq 0"
]

obj, cons = linear_program_from_latex(objective, constraints)
```

### Algorithmes génétiques
```python
def fitness_function_from_latex(latex_fitness):
    """Conversion de fonctions de fitness"""
    
    # Exemple: Fonction de Rosenbrock
    rosenbrock_latex = r"\sum_{i=1}^{n-1} [100(x_{i+1} - x_i^2)^2 + (1 - x_i)^2]"
    
    # Conversion
    fitness_expr = latex2sympy(latex_fitness)
    
    # Génération de fonction Python optimisée
    from sympy.utilities.lambdify import lambdify
    variables = list(fitness_expr.free_symbols)
    
    fitness_func = lambdify(variables, fitness_expr, 'numpy')
    
    return fitness_func, variables
```

## 6. TRAITEMENT DU SIGNAL

### Transformées et filtres
```python
def signal_processing_from_latex(latex_signal):
    """Conversion de formules de traitement du signal"""
    
    # Transformée de Fourier discrète
    dft_latex = r"X_k = \sum_{n=0}^{N-1} x_n e^{-i2\pi kn/N}"
    
    # Filtre passe-bas
    lowpass_latex = r"H(\omega) = \frac{1}{1 + i\omega/\omega_c}"
    
    # Conversion avec gestion des exponentielles complexes
    signal_replacements = {
        r'e\^\{([^}]+)\}': r'exp(\1)',
        r'\\omega': 'omega',
        r'\\pi': 'pi',
    }
    
    processed = latex_signal
    for pattern, replacement in signal_replacements.items():
        import re
        processed = re.sub(pattern, replacement, processed)
    
    return latex2sympy(processed)
```

## 7. STATISTIQUES ET PROBABILITÉS

### Distributions et tests
```python
def statistical_formula_from_latex(latex_stat):
    """Conversion de formules statistiques"""
    
    # Distribution normale
    normal_latex = r"\frac{1}{\sigma\sqrt{2\pi}} e^{-\frac{(x-\mu)^2}{2\sigma^2}}"
    
    # Test du chi-carré
    chi2_latex = r"\chi^2 = \sum_{i=1}^{n} \frac{(O_i - E_i)^2}{E_i}"
    
    # Préprocessing pour statistiques
    stat_replacements = {
        r'\\mu': 'mu',
        r'\\sigma': 'sigma',
        r'\\chi\^2': 'chi2',
        r'\\alpha': 'alpha',
        r'\\beta': 'beta',
    }
    
    processed = latex_stat
    for pattern, replacement in stat_replacements.items():
        import re
        processed = re.sub(pattern, replacement, processed)
    
    return latex2sympy(processed)

# Génération de fonctions de densité
def create_pdf_function(latex_pdf):
    """Création de fonction de densité de probabilité"""
    from sympy.utilities.lambdify import lambdify
    
    pdf_expr = statistical_formula_from_latex(latex_pdf)
    variables = list(pdf_expr.free_symbols)
    
    pdf_func = lambdify(variables, pdf_expr, 'numpy')
    
    return pdf_func, variables
```

## 8. INTÉGRATION AVEC JUPYTER ET VISUALISATION

### Notebooks interactifs
```python
def create_interactive_formula(latex_formula):
    """Création de formule interactive dans Jupyter"""
    from IPython.display import display, Math, Latex
    from ipywidgets import interact, FloatSlider
    import matplotlib.pyplot as plt
    import numpy as np
    
    # Conversion LaTeX vers SymPy
    expr = latex2sympy(latex_formula)
    variables = list(expr.free_symbols)
    
    # Affichage de la formule
    display(Math(latex_formula))
    
    # Création de fonction numérique
    from sympy.utilities.lambdify import lambdify
    func = lambdify(variables, expr, 'numpy')
    
    # Widget interactif
    if len(variables) == 1:
        var = variables[0]
        
        def plot_function(param_value=1.0):
            x_vals = np.linspace(-10, 10, 1000)
            y_vals = func(x_vals) if len(variables) == 1 else func(x_vals, param_value)
            
            plt.figure(figsize=(10, 6))
            plt.plot(x_vals, y_vals)
            plt.title(f"${latex_formula}$")
            plt.grid(True)
            plt.show()
        
        interact(plot_function, param_value=FloatSlider(min=-5, max=5, step=0.1, value=1))
    
    return func

# Usage dans Jupyter
# create_interactive_formula(r"x^2 + ax + b")
```

## 9. GÉNÉRATION DE CODE OPTIMISÉ

### Compilation vers C/Fortran
```python
def compile_latex_to_c(latex_formula, function_name="math_func"):
    """Compilation LaTeX vers C"""
    from sympy.utilities.codegen import codegen
    from sympy import symbols
    
    # Conversion LaTeX vers SymPy
    expr = latex2sympy(latex_formula)
    variables = list(expr.free_symbols)
    
    # Génération de code C
    code_gen = codegen((function_name, expr), "C", "math_module")
    
    return code_gen

def generate_vectorized_function(latex_formula):
    """Génération de fonction vectorisée optimisée"""
    import numba
    from sympy.utilities.lambdify import lambdify
    
    expr = latex2sympy(latex_formula)
    variables = list(expr.free_symbols)
    
    # Fonction NumPy
    numpy_func = lambdify(variables, expr, 'numpy')
    
    # Compilation JIT avec Numba
    @numba.jit(nopython=True)
    def optimized_func(*args):
        return numpy_func(*args)
    
    return optimized_func
```
