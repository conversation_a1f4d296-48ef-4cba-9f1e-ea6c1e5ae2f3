#!/usr/bin/env python3
"""
Programme de découpage HTML SIMPLE et EFFICACE pour Absolute Zero
Découpe fidèlement le fichier HTML en 10 parties complètes sans perte d'information.
"""

import os
from pathlib import Path
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleAbsoluteZeroSplitter:
    """Splitter HTML simple et efficace basé sur les numéros de lignes exacts."""
    
    def __init__(self, input_file: str, output_dir: str):
        self.input_file = Path(input_file)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Définition EXACTE des sections basée sur l'analyse du fichier
        self.sections = {
            "01_TITRE_ET_AUTEURS.html": {
                "start": 1,
                "end": 1328,
                "description": "Titre principal et auteurs"
            },
            "02_INTRODUCTION.html": {
                "start": 1329,
                "end": 1343,
                "description": "Section 1. Introduction"
            },
            "03_PARADIGME_ABSOLUTE_ZERO.html": {
                "start": 1344,
                "end": 2583,
                "description": "Section 2. The Absolute Zero Paradigm"
            },
            "04_ABSOLUTE_ZERO_REASONER.html": {
                "start": 2584,
                "end": 5095,
                "description": "Section 3. Absolute Zero Reasoner"
            },
            "05_EXPERIENCES.html": {
                "start": 5096,
                "end": 6842,
                "description": "Section 4. Experiments"
            },
            "06_TRAVAUX_CONNEXES.html": {
                "start": 6843,
                "end": 6849,
                "description": "Section 5. Related Work"
            },
            "07_CONCLUSION.html": {
                "start": 6850,
                "end": 6871,
                "description": "Section 6. Conclusion and Discussion"
            },
            "08_REFERENCES.html": {
                "start": 6872,
                "end": 6995,
                "description": "References"
            },
            "09_ANNEXES.html": {
                "start": 6996,
                "end": 7828,
                "description": "Appendix A et B"
            },
            "10_EXEMPLES_TACHES.html": {
                "start": 7829,
                "end": 12447,
                "description": "Appendix C et exemples de tâches"
            }
        }
        
        self.html_lines = []
        self.head_content = ""
        self.styles_content = ""
    
    def load_html(self) -> bool:
        """Charge le fichier HTML et extrait les composants."""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.html_lines = content.split('\n')
            logger.info(f"✅ Fichier chargé: {len(self.html_lines)} lignes")
            
            # Extraction du head et des styles (lignes 1-600 environ)
            self.head_content = '\n'.join(self.html_lines[:600])
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur de chargement: {e}")
            return False
    
    def extract_section_content(self, start: int, end: int) -> str:
        """Extrait le contenu d'une section entre les lignes spécifiées."""
        # Ajustement pour index 0
        start_idx = start - 1
        end_idx = end
        
        if start_idx < 0:
            start_idx = 0
        if end_idx > len(self.html_lines):
            end_idx = len(self.html_lines)
        
        section_lines = self.html_lines[start_idx:end_idx]
        return '\n'.join(section_lines)
    
    def create_complete_html(self, section_name: str, content: str, description: str) -> str:
        """Crée un fichier HTML complet avec head, styles et contenu."""
        
        # Template HTML complet
        html_template = f'''<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="UTF-8">
    <title>Absolute Zero: {description}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.mathpix.com/fonts/cmu.css"/>
    <style>
  html,body {{
    width: 100%;
    height: 100%;
  }}
  *, *::before,*::after {{
    box-sizing: border-box;
  }}
  @-ms-viewport {{
    width: device-width;
  }}
  body {{
    margin: 0;
    color: #1E2029;
    font-size: 14px;
    line-height: normal;
  }}
  hr {{
    box-sizing: content-box;
    height: 0;
    overflow: visible;
  }}
  h1, h2, h3, h4, h5, h6 {{
    margin-top: 0;
    margin-bottom: 0.5em;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }}
  p {{
    margin-top: 0;
    margin-bottom: 1em;
  }}
  ol, ul, dl {{
    margin-top: 0;
    margin-bottom: 1em;
  }}
  ol ol, ul ul, ol ul, ul ol {{
    margin-bottom: 0;
  }}
  dt {{
    font-weight: 500;
  }}
  dd {{
    margin-bottom: 0.5em;
    margin-left: 0;
  }}
  blockquote {{
    margin: 0 0 1em;
  }}
  dfn {{
    font-style: italic;
  }}
  b, strong {{
    font-weight: bolder;
  }}
  small {{
    font-size: 80%;
  }}
  sub, sup {{
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline;
  }}
  sub {{
    bottom: -0.25em;
  }}
  sup {{
    top: -0.5em;
  }}
  a {{
    color: #0B93ff;
    text-decoration: none;
    background-color: transparent;
    outline: none;
    cursor: pointer;
    transition: color 0.3s;
  }}
  a:hover {{
    color: #33aaff;
  }}
  a:active {{
    color: #0070d9;
  }}
  a:active, a:hover {{
    text-decoration: none;
    outline: 0;
  }}
  a[disabled] {{
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
    pointer-events: none;
  }}
  pre, code, kbd, samp {{
    font-size: 1em;
  }}
  pre {{
    margin-top: 0;
    margin-bottom: 1em;
    overflow: auto;
  }}
  figure {{
    margin: 0 0 1em;
  }}
  img {{
    vertical-align: middle;
    border-style: none;
  }}
  svg:not(:root) {{
    overflow: hidden;
  }}
  table {{
    border-collapse: collapse;
  }}
  caption {{
    padding-top: 0.75em;
    padding-bottom: 0.3em;
    color: rgba(0, 0, 0, 0.45);
    text-align: left;
    caption-side: bottom;
  }}
  th {{
    text-align: inherit;
  }}

mjx-container[jax="SVG"] {{
  direction: ltr;
}}

mjx-container[jax="SVG"] > svg {{
  overflow: visible;
  min-height: 1px;
  min-width: 1px;
}}

mjx-container[jax="SVG"] > svg a {{
  fill: blue;
  stroke: blue;
}}

mjx-assistive-mml {{
  position: absolute !important;
  top: 0px;
  left: 0px;
  clip: rect(1px, 1px, 1px, 1px);
  padding: 1px 0px 0px 0px !important;
  border: 0px !important;
  display: block !important;
  width: auto !important;
  overflow: hidden !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}}

mjx-assistive-mml[display="block"] {{
  width: 100% !important;
}}

mjx-container[jax="SVG"][display="true"] {{
  display: block;
  text-align: center;
  margin: 1em 0;
}}

mjx-container[jax="SVG"][display="true"][width="full"] {{
  display: flex;
}}

mjx-container[jax="SVG"][justify="left"] {{
  text-align: left;
}}

mjx-container[jax="SVG"][justify="right"] {{
  text-align: right;
}}

g[data-mml-node="merror"] > g {{
  fill: red;
  stroke: red;
}}

g[data-mml-node="merror"] > rect[data-background] {{
  fill: yellow;
  stroke: none;
}}

g[data-mml-node="mtable"] > line[data-line], svg[data-table] > g > line[data-line] {{
  stroke-width: 70px;
  fill: none;
}}

g[data-mml-node="mtable"] > rect[data-frame], svg[data-table] > g > rect[data-frame] {{
  stroke-width: 70px;
  fill: none;
}}

g[data-mml-node="mtable"] > .mjx-dashed, svg[data-table] > g > .mjx-dashed {{
  stroke-dasharray: 140;
}}

g[data-mml-node="mtable"] > .mjx-dotted, svg[data-table] > g > .mjx-dotted {{
  stroke-linecap: round;
  stroke-dasharray: 0,140;
}}

g[data-mml-node="mtable"] > g > svg {{
  overflow: visible;
}}

[jax="SVG"] mjx-tool {{
  display: inline-block;
  position: relative;
  width: 0;
  height: 0;
}}

[jax="SVG"] mjx-tool > mjx-tip {{
  position: absolute;
  top: 0;
  left: 0;
}}

mjx-tool > mjx-tip {{
  display: inline-block;
  padding: .2em;
  border: 1px solid #888;
  font-size: 70%;
  background-color: #F8F8F8;
  color: black;
  box-shadow: 2px 2px 5px #AAAAAA;
}}

g[data-mml-node="maction"][data-toggle] {{
  cursor: pointer;
}}

mjx-status {{
  display: block;
  position: fixed;
  left: 1em;
  bottom: 1em;
  min-width: 25%;
  padding: .2em .4em;
  border: 1px solid #888;
  font-size: 90%;
  background-color: #F8F8F8;
  color: black;
}}

foreignObject[data-mjx-xml] {{
  font-family: initial;
  line-height: normal;
  overflow: visible;
}}

mjx-container[jax="SVG"] path[data-c], mjx-container[jax="SVG"] use[data-c] {{
  stroke-width: 3;
}}

    #setText > div {{
        justify-content: inherit;
        margin-top: 0;
        margin-bottom: 1em;
        
        
    }}
    
    
    
    #setText div:last-child {{
        margin-bottom: 0 !important;
    }}

    #setText > br, #preview-content br {{
        line-height: 1.2;
    }}

    #preview-content > div {{
        margin-top: 0;
        margin-bottom: 1em;
        
    }}    
    
    .proof > div, .theorem > div {{
        margin-top: 1rem;
    }}

    #preview-content table {{
      margin-bottom: 1em;
    }}

    #setText table {{
      margin-bottom: 1em;
    }}
    
    #preview-content .sub-table table, #setText .sub-table table {{
      margin-bottom: 0;
    }}

    mjx-container {{
      text-indent: 0;
      overflow-y: hidden;
      overflow-x: auto;
      padding-top: 1px;
      padding-bottom: 1px;
      
      
    }}
    
    
    
    .math-inline mjx-container {{
        display: inline-block !important;
        page-break-inside: avoid;
        max-width: 100%;
        padding: 0;
        line-height: 0;
    }}
    .math-inline[data-overflow="visible"] mjx-container {{
      overflow: visible;
    }}
    .math-inline mjx-container mjx-assistive-mml {{
      max-width: 100%;
    }}
    .math-block {{
        align-items: center;
        page-break-after: auto;
        page-break-inside: avoid;
        margin: 0;
        display: block; /* mjx-container has block */
    }}
    
    .math-inline {{
      display: inline-flex; /* mjx-container has inline-block. To prevent displacement during use overflow-x: auto;*/
      max-width: 100%;
    }}
    
    .math-block[data-width="full"] {{
      overflow-x: auto;
      display: flex; /* mjx-container has flex */
    }}
    
    svg .math-inline {{
      display: initial;
      max-width: initial;
    }}
    
    svg .math-inline mjx-container {{
      max-width: initial;
    }}
    
    svg mjx-container {{
      overflow: visible;
      padding: 0;
    }}
    
    svg math-block[data-width="full"] {{
      overflow: visible;
    }}
    
    .math-block,.math-inline {{
      --mmd-highlight-color: rgba(0, 147, 255, 0.25);
      --mmd-highlight-text-color: #1e2029;
    }}

    .math-block[data-highlight-color] mjx-container[jax="SVG"] > svg {{
      background-color: var(--mmd-highlight-color);
    }}    
    
    .math-block[data-highlight-text-color] mjx-container[jax="SVG"] > svg {{
      color: var(--mmd-highlight-text-color);
    }}    
    .math-inline[data-highlight-color] mjx-container[jax="SVG"] {{
      background-color: var(--mmd-highlight-color);
    }}    
    
    .math-inline[data-highlight-text-color] mjx-container[jax="SVG"] {{
      color: var(--mmd-highlight-text-color);
    }}
    
    .math-block p {{
        flex-shrink: 1;
    }}
    .math-block mjx-container {{
        margin: 0 !important;
    }}
    .math-error {{
        background-color: yellow;
        color: red;
    }}

    #preview-content img, #setText img {{
        max-width: 100%;
    }}
    
    #preview-content blockquote,  #setText blockquote {{
        page-break-inside: avoid;
        color: #666;
        margin: 0 0 1em 0;
        padding-left: 3em;
        border-left: .5em solid #eee;
    }}

    #preview-content pre, #setText pre {{
        border: none;
        padding: 0;
        overflow: auto;
        font-size: 85%;
        line-height: 1.45;
        border-radius: 6px;
        box-sizing: border-box;
        background: #f8f8fa;
    }}
    #preview-content pre code, #setText pre code{{
        padding: 1rem;
        display: block;
        overflow-x: auto;
        line-height: 24px;
    }}
    .empty {{
        text-align: center;
        font-size: 18px;
        padding: 50px 0 !important;
    }}

    #setText table, #preview-content table {{
        display: table; 
        overflow: auto;
        max-width: 100%;
        border-collapse: collapse;
        page-break-inside: avoid;
    }}
      
    #setText table th, #preview-content table th {{
        text-align: center;
        font-weight: bold;
    }}
    
    #setText table td, #preview-content table td,
    #setText table th, #preview-content table th {{
        border: 1px solid #dfe2e5;
        padding: 6px 13px;
    }}
      
    #setText table tr, #preview-content table tr {{
        background-color: #fff;
        border-top: 1px solid #c6cbd1;
    }}
    
    #setText table tr:nth-child(2n), #preview-content table tr:nth-child(2n) {{
        background-color: #f6f8fa;
    }}

    
    #setText .main-title, #setText .author, #preview-content .main-title, #preview-content .author  {{
        text-align: center;
        margin: 0 auto;
    }}
    
    #preview-content .main-title, #setText .main-title {{
        line-height: 1.2;
        margin-bottom: 1em;
    }}

    #preview-content .author, #setText .author  {{
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }}

    #preview-content .author p, #setText .author p {{
        min-width: 30%;
        max-width: 50%;
        padding: 0 7px;
    }}

    #preview-content .author > p > span, #setText .author > p > span {{
        display: block;
        text-align: center;
    }}

    #preview-content .section-title, #setText .section-title {{
        margin-top: 1.5em;
    }}

    #preview-content .abstract, #setText .abstract {{
        text-align: justify;
        margin-bottom: 1em;
    }}

    #preview-content .abstract p, #setText .abstract p {{
        margin-bottom: 0;
    }}

  #preview {{
    font-family: 'CMU Serif', 'Georgia', Helvetica, Arial, sans-serif;
    font-size: 17px;
    visibility: visible;
    word-break: break-word;
    padding: 2.5em;
    max-width: 800px;
    margin: auto;
    box-sizing: content-box;
  }}

  #preview h1, #preview h2, #preview h3, #preview h4, #preview h5, #preview strong {{
    font-family: 'CMU Serif Bold', 'Georgia', Helvetica, Arial, sans-serif;
  }}

  #preview  i, #preview  em {{
    font-family: 'CMU Serif Italic', 'Georgia', Helvetica, Arial, sans-serif;
  }}
</style>
</head>
<body>
  <div id="preview" class="preview scrollEditor">
    <div id="container-ruller" />
    <div id="preview-content">
{content}
    </div>
  </div>
</body>
</html>'''
        
        return html_template

    def split_all_sections(self) -> bool:
        """Découpe toutes les sections."""
        try:
            for section_name, config in self.sections.items():
                logger.info(f"🔄 Traitement de {section_name}...")

                # Extraction du contenu
                content = self.extract_section_content(config['start'], config['end'])

                # Création du HTML complet
                complete_html = self.create_complete_html(
                    section_name,
                    content,
                    config['description']
                )

                # Sauvegarde
                output_path = self.output_dir / section_name
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(complete_html)

                logger.info(f"✅ {section_name} créé: {len(complete_html):,} caractères")

            return True

        except Exception as e:
            logger.error(f"❌ Erreur lors du découpage: {e}")
            return False

    def validate_split(self) -> bool:
        """Valide que toutes les sections ont été créées."""
        try:
            created_files = list(self.output_dir.glob("*.html"))
            expected_files = len(self.sections)

            logger.info(f"📊 Fichiers créés: {len(created_files)}/{expected_files}")

            total_size = 0
            for file_path in created_files:
                size = file_path.stat().st_size
                total_size += size
                logger.info(f"📄 {file_path.name}: {size:,} octets")

            logger.info(f"📈 Taille totale: {total_size:,} octets")

            return len(created_files) == expected_files

        except Exception as e:
            logger.error(f"❌ Erreur lors de la validation: {e}")
            return False

    def run(self) -> bool:
        """Exécute le découpage complet."""
        logger.info("🚀 DÉBUT DU DÉCOUPAGE ABSOLUTE ZERO")

        if not self.load_html():
            return False

        if not self.split_all_sections():
            return False

        if not self.validate_split():
            return False

        logger.info("🎯 DÉCOUPAGE TERMINÉ AVEC SUCCÈS!")
        return True

def main():
    """Fonction principale."""
    input_file = "../AZR/Absolute Zero_ Reinforced Self-play Reasoning with Zero Data.html"
    output_dir = "."

    print("🎯 PROGRAMME DE DÉCOUPAGE HTML ABSOLUTE ZERO")
    print("=" * 50)

    splitter = SimpleAbsoluteZeroSplitter(input_file, output_dir)
    success = splitter.run()

    if success:
        print("\n🎉 DÉCOUPAGE RÉUSSI!")
        print(f"📁 Fichiers créés dans: {output_dir}")
        print("📋 10 parties complètes générées sans perte d'information")
        print("\n📝 FICHIERS CRÉÉS:")
        for i, section in enumerate(splitter.sections.keys(), 1):
            print(f"  {i:2d}. {section}")
    else:
        print("\n❌ ÉCHEC du découpage")
        print("Vérifiez les logs pour plus de détails")

if __name__ == "__main__":
    main()
