# Problèmes Courants et Solutions pour la Conversion LaTeX vers Python

## 1. PROBLÈMES D'INSTALLATION

### Erreur ANTLR4 Version
**Problème**: Incompatibilité entre versions d'ANTLR4
```
Could not deserialize ATN with version (expected 4)
```

**Solutions**:
```bash
# Solution 1: Version spécifique recommandée
pip uninstall antlr4-python3-runtime
pip install antlr4-python3-runtime==4.9.3

# Solution 2: Version alternative stable
pip install antlr4-python3-runtime==4.11

# Solution 3: Réinstallation complète
pip uninstall latex2sympy2 antlr4-python3-runtime
pip install antlr4-python3-runtime==4.9.3
pip install latex2sympy2
```

### Problèmes de dépendances
**Problème**: Conflits entre bibliothèques
```python
ImportError: cannot import name 'LaTeXParser' from 'sympy.parsing.latex'
```

**Solutions**:
```bash
# Mise à jour SymPy
pip install --upgrade sympy

# Installation dans un environnement virtuel
python -m venv latex_env
source latex_env/bin/activate  # Linux/Mac
# ou latex_env\Scripts\activate  # Windows
pip install latex2sympy2 sympy
```

## 2. ERREURS DE PARSING LATEX

### Commandes LaTeX non supportées
**Problème**: Certaines commandes LaTeX ne sont pas reconnues
```python
# Échoue
latex2sympy(r"\mathbb{R}")  # Ensembles de nombres
latex2sympy(r"\mathcal{L}")  # Lettres calligraphiques
```

**Solutions**:
```python
def preprocess_latex(latex_str):
    """Préprocessing pour commandes non supportées"""
    
    # Remplacement des ensembles de nombres
    replacements = {
        r'\\mathbb\{R\}': 'R',
        r'\\mathbb\{N\}': 'N', 
        r'\\mathbb\{Z\}': 'Z',
        r'\\mathbb\{Q\}': 'Q',
        r'\\mathbb\{C\}': 'C',
        r'\\mathcal\{([A-Z])\}': r'\1',  # Lettres calligraphiques
        r'\\mathrm\{([^}]+)\}': r'\1',   # Texte romain
        r'\\text\{([^}]+)\}': r'\1',     # Texte normal
    }
    
    import re
    result = latex_str
    for pattern, replacement in replacements.items():
        result = re.sub(pattern, replacement, result)
    
    return result

# Usage
latex_str = r"\mathbb{R}^n"
processed = preprocess_latex(latex_str)
result = latex2sympy(processed)
```

### Environnements mathématiques complexes
**Problème**: Matrices et systèmes d'équations
```latex
\begin{cases}
x + y = 1 \\
x - y = 0
\end{cases}
```

**Solutions**:
```python
def parse_cases(latex_str):
    """Parser pour environnement cases"""
    import re
    
    # Extraction du contenu des cases
    cases_pattern = r'\\begin\{cases\}(.*?)\\end\{cases\}'
    match = re.search(cases_pattern, latex_str, re.DOTALL)
    
    if match:
        content = match.group(1)
        equations = content.split('\\\\')
        
        parsed_equations = []
        for eq in equations:
            eq = eq.strip()
            if eq:
                # Conversion de chaque équation
                try:
                    parsed = latex2sympy(eq)
                    parsed_equations.append(parsed)
                except:
                    print(f"Erreur parsing: {eq}")
        
        return parsed_equations
    
    return None
```

## 3. PROBLÈMES DE SYMBOLES ET NOTATION

### Indices et exposants complexes
**Problème**: Notation avec indices multiples
```latex
x_{i,j}^{(k)}  # Indice double avec exposant
```

**Solutions**:
```python
def handle_complex_subscripts(latex_str):
    """Gestion des indices/exposants complexes"""
    import re
    
    # Pattern pour indices multiples
    pattern = r'([a-zA-Z])_\{([^}]+)\}\^\{([^}]+)\}'
    
    def replace_complex(match):
        var = match.group(1)
        subscript = match.group(2)
        superscript = match.group(3)
        
        # Création d'un nom de variable unique
        clean_sub = subscript.replace(',', '_').replace(' ', '')
        clean_sup = superscript.replace('(', '').replace(')', '')
        
        return f"{var}_{clean_sub}_{clean_sup}"
    
    return re.sub(pattern, replace_complex, latex_str)
```

### Fonctions spéciales
**Problème**: Fonctions mathématiques spécialisées
```latex
\Gamma(x)  # Fonction gamma
\zeta(s)   # Fonction zeta
```

**Solutions**:
```python
from sympy import gamma, zeta, factorial, binomial

def handle_special_functions(latex_str):
    """Conversion des fonctions spéciales"""
    
    replacements = {
        r'\\Gamma\(([^)]+)\)': r'gamma(\1)',
        r'\\zeta\(([^)]+)\)': r'zeta(\1)',
        r'\\binom\{([^}]+)\}\{([^}]+)\}': r'binomial(\1, \2)',
        r'([0-9]+)!': r'factorial(\1)',
    }
    
    import re
    result = latex_str
    for pattern, replacement in replacements.items():
        result = re.sub(pattern, replacement, result)
    
    return result
```

## 4. PROBLÈMES DE PERFORMANCE

### Expressions très longues
**Problème**: Timeout sur expressions complexes
```python
# Expression très longue qui peut causer des problèmes
long_latex = r"\sum_{n=1}^{1000} \frac{1}{n^2} + \int_0^{\infty} e^{-x^2} dx + ..."
```

**Solutions**:
```python
import signal
from contextlib import contextmanager

@contextmanager
def timeout(duration):
    """Context manager pour timeout"""
    def timeout_handler(signum, frame):
        raise TimeoutError("Parsing timeout")
    
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(duration)
    try:
        yield
    finally:
        signal.alarm(0)

def safe_parse_with_timeout(latex_str, timeout_seconds=30):
    """Parsing avec timeout de sécurité"""
    try:
        with timeout(timeout_seconds):
            return latex2sympy(latex_str)
    except TimeoutError:
        print(f"Timeout après {timeout_seconds}s")
        return None
    except Exception as e:
        print(f"Erreur: {e}")
        return None
```

### Cache et optimisation
```python
from functools import lru_cache
import hashlib

@lru_cache(maxsize=1000)
def cached_latex2sympy(latex_str):
    """Version cachée de latex2sympy"""
    return latex2sympy(latex_str)

def persistent_cache_parse(latex_str, cache_file="latex_cache.json"):
    """Cache persistant sur disque"""
    import json
    import os
    
    # Création d'une clé de cache
    cache_key = hashlib.md5(latex_str.encode()).hexdigest()
    
    # Chargement du cache existant
    cache = {}
    if os.path.exists(cache_file):
        with open(cache_file, 'r') as f:
            cache = json.load(f)
    
    # Vérification du cache
    if cache_key in cache:
        return cache[cache_key]
    
    # Parsing et sauvegarde
    try:
        result = str(latex2sympy(latex_str))
        cache[cache_key] = result
        
        with open(cache_file, 'w') as f:
            json.dump(cache, f)
        
        return result
    except Exception as e:
        return None
```

## 5. PROBLÈMES D'ENCODAGE

### Caractères Unicode
**Problème**: Symboles mathématiques Unicode
```latex
α + β = γ  # Lettres grecques directes
```

**Solutions**:
```python
def normalize_unicode_math(latex_str):
    """Normalisation des caractères Unicode mathématiques"""
    
    unicode_to_latex = {
        'α': r'\alpha',
        'β': r'\beta', 
        'γ': r'\gamma',
        'δ': r'\delta',
        'ε': r'\epsilon',
        'π': r'\pi',
        '∞': r'\infty',
        '∑': r'\sum',
        '∫': r'\int',
        '∂': r'\partial',
        '√': r'\sqrt',
        '±': r'\pm',
        '≤': r'\leq',
        '≥': r'\geq',
        '≠': r'\neq',
        '≈': r'\approx',
    }
    
    result = latex_str
    for unicode_char, latex_cmd in unicode_to_latex.items():
        result = result.replace(unicode_char, latex_cmd)
    
    return result
```

## 6. DEBUGGING ET DIAGNOSTIC

### Outils de diagnostic
```python
def diagnose_latex_parsing(latex_str):
    """Diagnostic complet d'une expression LaTeX"""
    
    print(f"Expression originale: {latex_str}")
    print("-" * 50)
    
    # Test de préprocessing
    preprocessed = preprocess_latex(latex_str)
    print(f"Après préprocessing: {preprocessed}")
    
    # Test de parsing
    try:
        result = latex2sympy(preprocessed)
        print(f"Résultat SymPy: {result}")
        print(f"Type: {type(result)}")
        
        # Test d'évaluation
        if hasattr(result, 'evalf'):
            try:
                numeric = result.evalf()
                print(f"Valeur numérique: {numeric}")
            except:
                print("Évaluation numérique impossible")
                
    except Exception as e:
        print(f"Erreur de parsing: {e}")
        print(f"Type d'erreur: {type(e).__name__}")
        
        # Tentative de parsing partiel
        try:
            from sympy.parsing.latex import parse_latex
            result = parse_latex(preprocessed)
            print(f"SymPy natif réussi: {result}")
        except Exception as e2:
            print(f"SymPy natif échoué: {e2}")

# Usage
diagnose_latex_parsing(r"\frac{x^2 + 1}{\sqrt{x}}")
```

### Logging avancé
```python
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('latex_conversion.log'),
        logging.StreamHandler()
    ]
)

def logged_latex2sympy(latex_str):
    """Version avec logging détaillé"""
    logger = logging.getLogger(__name__)
    
    logger.info(f"Début conversion: {latex_str}")
    
    try:
        result = latex2sympy(latex_str)
        logger.info(f"Conversion réussie: {result}")
        return result
    except Exception as e:
        logger.error(f"Erreur conversion: {e}")
        logger.debug(f"Détails erreur: {type(e).__name__}")
        return None
```

## 7. TESTS ET VALIDATION

### Suite de tests complète
```python
def run_conversion_tests():
    """Suite de tests pour la conversion LaTeX"""
    
    test_cases = [
        # Cas basiques
        (r"x^2", "x**2"),
        (r"\frac{1}{x}", "1/x"),
        (r"\sqrt{x}", "sqrt(x)"),
        
        # Cas complexes
        (r"\int_0^1 x^2 dx", None),  # Intégrale
        (r"\sum_{n=1}^{\infty} \frac{1}{n^2}", None),  # Somme
        
        # Cas problématiques
        (r"\mathbb{R}", "R"),  # Après préprocessing
        (r"\text{hello}", "hello"),  # Texte
    ]
    
    passed = 0
    total = len(test_cases)
    
    for latex_input, expected in test_cases:
        try:
            result = safe_latex_conversion(latex_input)
            if expected is None or str(result) == expected:
                print(f"✓ {latex_input}")
                passed += 1
            else:
                print(f"✗ {latex_input}: attendu {expected}, obtenu {result}")
        except Exception as e:
            print(f"✗ {latex_input}: erreur {e}")
    
    print(f"\nRésultats: {passed}/{total} tests réussis")
    return passed == total
```
