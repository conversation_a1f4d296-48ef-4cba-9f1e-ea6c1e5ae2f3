🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Task: Solve Deduction Task (Example 1)
🔗 Model Input: Here is the function f, the input provided [1, 2, 1, 2, 3, 2, 1], 2, and we need to
🔗 predict the output.
🔗 def f(array: list[int], k: int) -> int:
🔗 window_start = 0
🔗 max_length = 0
🔗 char_freq = {}
🔗 for window_end in range(len(array)):
🔗 end_char = array[window_end]
🔗 if end_char not in char_freq:
🔗 char_freq[end_char] = 0
🔗 char_freq[end_char] += 1
🔗 while len(char_freq) > k:
🔗 start_char = array[window_start]
🔗 char_freq[start_char] -= 1
🔗 if char_freq[start_char] == 0:
🔗 del char_freq[start_char]
🔗 window_start += 1
🔗 max_length = max(max_length, window_end - window_start + 1)
🔗 return max_length
🔗 Input: [1, 2, 1, 2, 3, 2, 1], 2
🔗 Model Output:
🔗 Based on the given code snippet, we need to determine the output of the function f(array, k) when provided with the input [1, 2, 1, 2, 3, 2, 1] and k = 2.
🔗 The function f is designed to find the length of the longest subarray with at most k distinct characters. Let’s go through the code step by step:
🔗 1. Initialize window_start to 0, max_length to 0, and an empty dictionary char_freq to store the frequency of characters in the current window.
🔗 2. Iterate through the array using window_end as the index:
🔗 a. Get the current character end_char at index window_end.
🔗 b. If end_char is not in char_freq, add it with a frequency of 0.
🔗 c. Increment the frequency of end_char by 1.
🔗 3. Check if the number of distinct characters in char_freq is greater than k:
🔗 a. If true, shrink the window from the start:
🔗 i. Get the character start_char at index window_start.
🔗 ii. Decrement its frequency by 1.
🔗 iii. If the frequency becomes 0, remove the character from char_freq.
🔗 iv. Increment window_start by 1.
🔗 4. Update max_length by taking the maximum of max_length and the current window size (window_end - window_start + 1).
🔗 Now, let’s apply this algorithm step-by-step to the input [1, 2, 1, 2, 3, 2, 1], k = 2:
🔗 • window_start = 0, max_length = 0, char_freq = {}
🔗 • window_end = 0, add 1 to char_freq: {1 : 1}, window size = 1
🔗 • window_end = 1, add 2: {1 : 1, 2 : 1}, window size = 2
🔗 • window_end = 2, increment 1: {1 : 2, 2 : 1}, window size = 3
🔗 • window_end = 3, increment 2: {1 : 2, 2 : 2}, window size = 4
🔗 • window_end = 4, add 3: {1 : 2, 2 : 2, 3 : 1}; now distinct chars > 2, shrink window:
🔗 - remove 1 at start: {1 : 1, 2 : 2, 3 : 1}
🔗 - remove 2 at start: {1 : 1, 2 : 1, 3 : 1}
🔗 - remove 1 again: {2 : 1, 3 : 1}, window now starts at 3
🔗 • window_end = 5, increment 2: {2 : 2, 3 : 1}, window size = 3
🔗 • window_end = 6, add 1: {2 : 2, 3 : 1, 1 : 1}, shrink again:
🔗 - remove 2: {2 : 1, 3 : 1, 1 : 1}
🔗 - remove 3: {2 : 1, 1 : 1}, window now starts at 5
🔗 - window size = 2
🔗 Maximum window size encountered is 4, thus max_length = 4.
🔗 Hence, the function returns 4.
🔗 Figure 23. Example of solving the deduction task. Given a program and input, the model simulates the execution of the program until
🔗 the final result.
🔗 29
🔗 Figure 23. Solve Deduction Task Example. Example of solving the deduction task. Given a program and input, the model simulates the
🔗 execution of the program until the final result.
🔗 30