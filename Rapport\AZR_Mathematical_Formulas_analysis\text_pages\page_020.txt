🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Appendix
🔗 Appendix Contents
🔗 A Reinforcement Learning with Verifiable Rewards.
🔗 21
🔗 B
🔗 Implementation Details
🔗 21
🔗 C More Results
🔗 22
🔗 C.1
🔗 Out-of-Distribution Performance Breakdown . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 22
🔗 C.2
🔗 In-Distribution Results . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 22
🔗 C.3
🔗 Interplay Between Propose and Solve Roles . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 22
🔗 C.4
🔗 Complexity and Diversity Metrics of AZR Proposed Tasks . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 32
🔗 C.5
🔗 Generated Code Complexity Dynamics Between Abd/Ded and Ind.
🔗 . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 32
🔗 D Alternative Approaches Considered
🔗 49
🔗 D.1
🔗 Error Deduction Task . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 49
🔗 D.2
🔗 Composite Functions as Curriculum Learning
🔗 . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 49
🔗 D.3 Toying with the Initial p(z) . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 49
🔗 D.4
🔗 Extra Rewards . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 49
🔗 D.5
🔗 Environment Transition . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .
🔗 50
🔗 20