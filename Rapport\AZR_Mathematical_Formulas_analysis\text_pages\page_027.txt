🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Task: Propose Abduction Task
🔗 Model Input: (k random selected example programs as few-shot input)
🔗 Model Output:
🔗 For this task, we need an intelligent problem that requires deep algorithmic reasoning and demands long multi step reasoning. A
🔗 typical type of problem that fits these requirements is the Matrix Reconstruction from a given jumbled li st of elements, where
🔗 we have to figure out the matrix’s dimensions and individual values. However, this task will involve multiple steps, increasing its
🔗 complexity and creativity. We will create a function ‘f‘ that transforms a jumbled list of elements into a two-dimensional matrix,
🔗 then back to a list, given a specific reconstruction pattern. We will retrieve the original list from the reconstructed matrix. Here,
🔗 we will avoid random and datetime operations to ensure determinism. The function will be structured to reconstruct the matrix
🔗 elements in a unique and challenging way. We will provide an input that, after reconstruction, presents a unique list that can be
🔗 easily deduced from the given output in a test piece.
🔗 def f(jumbled_elements: list) -> list:
🔗 if not jumbled_elements:
🔗 return []
🔗 # Calculate the square root of the length of elements to estimate the matrix dimensions
🔗 n = len(jumbled_elements)
🔗 sqrt_n = int(n ** 0.5)
🔗 if n > sqrt_n ** 2:
🔗 sqrt_n += 1
🔗 if n < sqrt_n ** 2:
🔗 sqrt_n -= 1
🔗 # Now, we need to reconstruct the matrix elements in a challenging way using specific patterns
🔗 # For this, we can reconstruct the matrix using zigzag patterns or hill-climbing patterns
🔗 # Here, a hill-climbing pattern is used where we traverse the matrix from the extreme
🔗 # right and downward Then traverse the matrix from the second to last element to the
🔗 # extreme bottom. Repeat the process until the entire matrix is traversed.
🔗 # Initialize the matrix
🔗 matrix = [[0 for _ in range(sqrt_n)] for _ in range(sqrt_n)]
🔗 cur_idx = 0
🔗 # Fill the matrix in a zigzag pattern
🔗 for i in range(sqrt_n):
🔗 if i % 2 == 0:
🔗 for j in range(sqrt_n):
🔗 matrix[j][i] = j * sqrt_n + i + cur_idx
🔗 cur_idx += 1
🔗 else:
🔗 for j in range(sqrt_n - 1, -1, -1):
🔗 matrix[j][i] = j * sqrt_n + n - i - cur_idx - 1
🔗 cur_idx += 1
🔗 # Reconstruct the list into a matrix by repeating the process
🔗 reconstructed_list = []
🔗 for i in range(sqrt_n):
🔗 for j in range(sqrt_n):
🔗 reconstructed_list.append(matrix[i][j])
🔗 # Now, we need to calculate another matrix based on a function F applied to the current matrix
🔗 # F is a mathematical function that can be complex, such as factorial, prime check,
🔗 # or another transformation For example, transform each element by adding 1 if it's prime,
🔗 # subtracting 1 if it's even, or leaving it unchanged, Implement any specific transformation
🔗 #
🔗 function here. Here, for demonstration, each element is left unchanged
🔗 transformed_result = reconstructed_list
🔗 return transformed_result
🔗 Output: [0, 11, 10, 1, 5, 16, 15, 6, 10, 21, 20, 11, 15, 26, 25, 16]
🔗 Figure 20. An example response of proposing an abduction task. The model uses comments as reasoning when generating the program.
🔗 26
🔗 Figure 20. Propose Abduction Task Example. An example response of proposing an abduction task. The model uses comments as
🔗 reasoning when generating the program.
🔗 27