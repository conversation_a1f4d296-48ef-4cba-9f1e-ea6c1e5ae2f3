🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Algorithm 1 Self-Play Training of Absolute Zero Reasoner (AZR)
🔗 Require: Pretrained base LLM πθ; batch size B; #references K; iterations T

📐 FORMULE MATHÉMATIQUE:
    1: Dded, Dabd, Dind ←InitSeeding(πθ)

🔗 ▷see §3.3.1
🔗 2: for t ←1 to T do
🔗 3:
🔗 for b ←1 to B do
🔗 ▷PROPOSE PHASE
🔗 4:

📐 FORMULE MATHÉMATIQUE:
    p ∼Dabd ∪Dded

🔗 ▷sample a program for induction task proposal
🔗 5:

📐 FORMULE MATHÉMATIQUE:
    in


📐 FORMULE MATHÉMATIQUE:
    π


📐 FORMULE MATHÉMATIQUE:
    N


📐 FORMULE MATHÉMATIQUE:
    n=1, mπ ←πpropose


📐 FORMULE MATHÉMATIQUE:
    θ

🔗 (ind, p)
🔗 ▷generate N inputs and a description
🔗 6:
🔗 if

📐 FORMULE MATHÉMATIQUE:
    (in


📐 FORMULE MATHÉMATIQUE:
    π, on

🔗 π)

📐 FORMULE MATHÉMATIQUE:
    N


📐 FORMULE MATHÉMATIQUE:
    n=1 ←ValidateByExecuting


📐 FORMULE MATHÉMATIQUE:
     p, {in


📐 FORMULE MATHÉMATIQUE:
    π}, syntax

🔗  then
🔗 ▷validate I/Os, see §3.3.3
🔗 7:

📐 FORMULE MATHÉMATIQUE:
    Dind ←Dind ∪


📐 FORMULE MATHÉMATIQUE:
    (p, {(in


📐 FORMULE MATHÉMATIQUE:
    π, on


📐 FORMULE MATHÉMATIQUE:
    π)}, mπ)

🔗 ▷update induction buffer
🔗 8:
🔗 for α ∈{ded, abd} do
🔗 9:

📐 FORMULE MATHÉMATIQUE:
     pk, ik, ok


📐 FORMULE MATHÉMATIQUE:
    K


📐 FORMULE MATHÉMATIQUE:
    k=1 ∼Dα

🔗 ▷sample K reference examples
🔗 10:

📐 FORMULE MATHÉMATIQUE:
    (pπ, iπ) ←πpropose


📐 FORMULE MATHÉMATIQUE:
    θ


📐 FORMULE MATHÉMATIQUE:
     α, {(pk, ik, ok)}


📐 FORMULE MATHÉMATIQUE:
    

🔗 ▷propose new task
🔗 11:

📐 FORMULE MATHÉMATIQUE:
    if oπ ←ValidateByExecuting


📐 FORMULE MATHÉMATIQUE:
     pπ, iπ, syntax,safety,determinism

🔗  then
🔗 ▷see §3.3.3
🔗 12:

📐 FORMULE MATHÉMATIQUE:
    Dα ←Dα ∪


📐 FORMULE MATHÉMATIQUE:
    (pπ, iπ, oπ)

🔗 ▷if valid, update deduction or abduction buffers
🔗 13:
🔗 for all α ∈{ded, abd, ind} do
🔗 ▷SOLVE PHASE
🔗 14:
🔗 (x, y⋆) ←SamplePrepareTasks

📐 FORMULE MATHÉMATIQUE:
     Dα, B, t


📐 FORMULE MATHÉMATIQUE:
    

🔗 ▷x, y⋆prepared based on α, see §3.3.3&3.3.4
🔗 15:

📐 FORMULE MATHÉMATIQUE:
    yπ ∼πsolve


📐 FORMULE MATHÉMATIQUE:
    θ

🔗 (x)
🔗 16:

📐 FORMULE MATHÉMATIQUE:
    Reward: Use proposed task triplets and solved answers to get rpropose & rsolve

🔗 ▷see §3.1
🔗 17:
🔗 RL update: use Task Relative REINFORCE++ to update πθ
🔗 ▷see §3.3.5
🔗 3.3.2. Task Proposal Inputs and Buffer Management
🔗 During the actual self-play stage of AZR, we use the task buffer in three ways. First, for the proposer of abduction and deduction tasks,
🔗 we uniformly sample K past triplets from the buffer, present them as in-context examples to the proposer and let it generate a new task.
🔗 The design is to show it past examples, and prompt it to generate a different one to promote diversity (Zhao et al., 2025a). Second, we
🔗 sample one triplet from the union of abduction and deduction buffers Dabd
🔗 S Dded, and present the program p from that triplet to the
🔗 induction proposer to generate a set of N matching inputs {in} and a natural language message m. Lastly, to maintain stable training, if
🔗 a batch of solver problems contains fewer than B valid proposed tasks (proposer not adhering to formatting), we fill the remainder by
🔗 uniformly sampling from the corresponding task buffer of previously validated triplets.
🔗 The buffer grows for abduction and deduction tasks whenever π propose a valid triplet (p, i, o), regardless if it gets any task reward.

📐 FORMULE MATHÉMATIQUE:
    Similarly, for induction tasks, all valid triplets (p, {in, on}), m are added to the buffer.

🔗 3.3.3. Constructing Valid Tasks
🔗 Proposal Task Validation. We first describe how we construct valid tasks from the proposals generated by the policy π. For deduction
🔗 and abduction tasks, each proposal consists of a program and an input (p, i). To validate the task, we use the task validation procedure
🔗 (steps shown below) on the input to obtain the correct output o, resulting in a complete triplet (p, i, o). For induction tasks, given a

📐 FORMULE MATHÉMATIQUE:
    program p the policy proposes a set of inputs {in} and message m. We also use the task validation procedure on each of the input in


📐 FORMULE MATHÉMATIQUE:
    in the set to obtain a corresponding output on, forming a set of input-output pairs {in, on}. We do not impose any constraints on m.

🔗 The resulting task is considered valid only when all inputs yield valid outputs and the formatting requirements are satisfied. The task
🔗 validation procedure entails:
🔗 1. Program Integrity. We first use Python to run the program p with the input i. If no errors are raised and something is returned, we
🔗 then gather the output o of that (p, i) pair and determine that the program at least has valid syntax.
🔗 2. Program Safety. We also check whether a program is safe for execution by restricting the use of certain sensitive packages that might
🔗 cause harm to the Python environment, i.e., os.sys, sys, shutil. The list of packages used to filter out invalid programs is
🔗 provided in Figure 8. This list is also included in the instructions when prompting the language model to generate questions. See
🔗 Figures 34 to 36.
🔗 3. Check for Determinism. In our setting, we only consider deterministic programs, i.e., p ∈Pdeterministic ⊂P, where P is the space
🔗 of all valid programs and I is the space of all valid inputs:
🔗 7