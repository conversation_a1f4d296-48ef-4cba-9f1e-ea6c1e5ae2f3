#!/usr/bin/env python3
"""
Programme de découpage HTML pour Absolute Zero Research Paper
Découpe fidèlement le fichier HTML en 10 parties complètes sans perte d'information.

Basé sur les meilleures pratiques de BeautifulSoup et HTMLHeaderTextSplitter.
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from bs4 import BeautifulSoup, Tag, NavigableString
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AbsoluteZeroHTMLSplitter:
    """
    Splitter HTML spécialisé pour le document Absolute Zero.
    Préserve TOUTE l'information et structure HTML.
    """
    
    def __init__(self, input_file: str, output_dir: str):
        self.input_file = Path(input_file)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Définition des sections et leurs délimiteurs
        self.sections_config = {
            "01_TITRE_ET_AUTEURS.html": {
                "start_patterns": ["<!DOCTYPE html"],
                "end_patterns": ["</div></div>"],
                "include_patterns": ["main-title", "author"],
                "description": "Titre principal, auteurs et affiliations"
            },
            "02_INTRODUCTION.html": {
                "start_patterns": ["1. Introduction"],
                "end_patterns": ["2. The Absolute Zero Paradigm"],
                "section_headers": ["h2"],
                "description": "Section 1. Introduction complète"
            },
            "03_PARADIGME_ABSOLUTE_ZERO.html": {
                "start_patterns": ["2. The Absolute Zero Paradigm"],
                "end_patterns": ["3. Absolute Zero Reasoner"],
                "section_headers": ["h2"],
                "description": "Section 2. The Absolute Zero Paradigm"
            },
            "04_ABSOLUTE_ZERO_REASONER.html": {
                "start_patterns": ["3. Absolute Zero Reasoner"],
                "end_patterns": ["4. Experiments"],
                "section_headers": ["h2"],
                "description": "Section 3. Absolute Zero Reasoner"
            },
            "05_EXPERIENCES.html": {
                "start_patterns": ["4. Experiments"],
                "end_patterns": ["5. Related Work"],
                "section_headers": ["h2"],
                "description": "Section 4. Experiments"
            },
            "06_TRAVAUX_CONNEXES.html": {
                "start_patterns": ["5. Related Work"],
                "end_patterns": ["6. Conclusion and Discussion"],
                "section_headers": ["h2"],
                "description": "Section 5. Related Work"
            },
            "07_CONCLUSION.html": {
                "start_patterns": ["6. Conclusion and Discussion"],
                "end_patterns": ["References"],
                "section_headers": ["h2"],
                "description": "Section 6. Conclusion and Discussion"
            },
            "08_REFERENCES.html": {
                "start_patterns": ["References"],
                "end_patterns": ["A. Reinforcement Learning"],
                "section_headers": ["h2"],
                "description": "Bibliographie complète"
            },
            "09_ANNEXES.html": {
                "start_patterns": ["A. Reinforcement Learning", "B. Implementation Details", "C. More Results"],
                "end_patterns": ["Task: Propose Deduction Task", "Model-proposed Task"],
                "section_headers": ["h2"],
                "description": "Appendix A, B, C - Détails techniques"
            },
            "10_EXEMPLES_TACHES.html": {
                "start_patterns": ["Task: Propose", "Model-proposed Task", "Task: Solve"],
                "end_patterns": ["</body>", "</html>"],
                "section_headers": ["h2"],
                "description": "Exemples de tâches et résolutions"
            }
        }
        
        self.html_content = None
        self.soup = None
        
    def load_html(self) -> bool:
        """Charge le fichier HTML source."""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.html_content = f.read()
            
            self.soup = BeautifulSoup(self.html_content, 'html.parser')
            logger.info(f"✅ Fichier HTML chargé: {len(self.html_content)} caractères")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du chargement: {e}")
            return False
    
    def extract_html_structure(self) -> Dict:
        """Extrait la structure HTML complète."""
        structure = {
            'head': str(self.soup.head) if self.soup.head else "",
            'styles': [],
            'scripts': [],
            'body_attrs': self.soup.body.attrs if self.soup.body else {},
            'sections': []
        }
        
        # Extraction des styles
        for style in self.soup.find_all('style'):
            structure['styles'].append(str(style))
            
        # Extraction des scripts
        for script in self.soup.find_all('script'):
            structure['scripts'].append(str(script))
            
        return structure
    
    def find_section_boundaries(self) -> Dict[str, Tuple[int, int]]:
        """Trouve les limites exactes de chaque section dans le HTML avec BeautifulSoup."""
        boundaries = {}

        # Recherche des sections par leurs titres H2
        h2_sections = self.soup.find_all('h2', class_='section-title')

        # Mapping des sections trouvées
        section_mapping = {
            "01_TITRE_ET_AUTEURS.html": (1, 1328),  # Début jusqu'avant Introduction
            "02_INTRODUCTION.html": (1329, 1343),   # 1. Introduction
            "03_PARADIGME_ABSOLUTE_ZERO.html": (1344, 2583),  # 2. The Absolute Zero Paradigm
            "04_ABSOLUTE_ZERO_REASONER.html": (2584, 5095),   # 3. Absolute Zero Reasoner
            "05_EXPERIENCES.html": (5096, 6842),    # 4. Experiments
            "06_TRAVAUX_CONNEXES.html": (6843, 6849),  # 5. Related Work
            "07_CONCLUSION.html": (6850, 6871),     # 6. Conclusion and Discussion
            "08_REFERENCES.html": (6872, 6995),     # References
            "09_ANNEXES.html": (6996, 7828),        # Appendix A, B
            "10_EXEMPLES_TACHES.html": (7829, 12447) # Appendix C + Examples
        }

        # Validation avec le contenu réel
        html_lines = self.html_content.split('\n')

        for section_name, (start, end) in section_mapping.items():
            # Vérification que les limites sont valides
            if start < len(html_lines) and end <= len(html_lines):
                boundaries[section_name] = (start - 1, end)  # Ajustement pour index 0
                logger.info(f"📍 {section_name}: lignes {start} à {end}")
            else:
                logger.warning(f"⚠️ Limites invalides pour {section_name}")

        return boundaries
    
    def create_complete_html_section(self, section_name: str, content_lines: List[str]) -> str:
        """Crée un fichier HTML complet pour une section."""
        
        # Template HTML complet avec tous les styles
        html_template = f"""<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="UTF-8">
    <title>Absolute Zero: {self.sections_config[section_name]['description']}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.mathpix.com/fonts/cmu.css"/>
    {self._extract_styles()}
</head>
<body>
  <div id="preview" class="preview scrollEditor">
    <div id="container-ruller" />
    <div id="preview-content">
      {chr(10).join(content_lines)}
    </div>
  </div>
</body>
</html>"""
        
        return html_template
    
    def _extract_styles(self) -> str:
        """Extrait tous les styles CSS du document original."""
        styles = []
        for style_tag in self.soup.find_all('style'):
            styles.append(str(style_tag))
        return '\n'.join(styles)
    
    def split_html_by_sections(self) -> bool:
        """Découpe le HTML en sections complètes."""
        try:
            boundaries = self.find_section_boundaries()
            html_lines = self.html_content.split('\n')

            for section_name, (start_line, end_line) in boundaries.items():
                logger.info(f"🔄 Traitement de {section_name}...")

                # Extraction du contenu brut de la section
                section_content = html_lines[start_line:end_line]

                # Traitement spécial pour la première section (titre + auteurs)
                if section_name == "01_TITRE_ET_AUTEURS.html":
                    section_content = self._extract_title_and_authors()
                else:
                    # Nettoyage et préparation du contenu
                    section_content = self._clean_section_content(section_content)

                # Création du HTML complet
                complete_html = self.create_complete_html_section(section_name, section_content)

                # Sauvegarde
                output_path = self.output_dir / section_name
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(complete_html)

                logger.info(f"✅ {section_name} créé: {len(complete_html)} caractères")

            return True

        except Exception as e:
            logger.error(f"❌ Erreur lors du découpage: {e}")
            return False

    def _extract_title_and_authors(self) -> List[str]:
        """Extrait spécifiquement le titre et les auteurs."""
        content = []

        # Recherche du titre principal
        title = self.soup.find('h1', class_='main-title')
        if title:
            content.append(str(title))

        # Recherche des auteurs
        authors = self.soup.find('div', class_='author')
        if authors:
            content.append(str(authors))

        return content
    
    def _clean_section_content(self, content_lines: List[str]) -> List[str]:
        """Nettoie le contenu d'une section en préservant la structure."""
        cleaned = []
        
        for line in content_lines:
            # Préservation de toutes les balises HTML importantes
            if any(tag in line.lower() for tag in ['<div', '<h1', '<h2', '<h3', '<p', '<ul', '<li', '<table', '<tr', '<td', '<th', '<img', '<svg', '<math', '<mjx-']):
                cleaned.append(line)
            elif line.strip():  # Garde les lignes non-vides
                cleaned.append(line)
        
        return cleaned
    
    def validate_split(self) -> bool:
        """Valide que le découpage est complet et sans perte."""
        try:
            total_original_size = len(self.html_content)
            total_split_size = 0
            
            for section_file in self.output_dir.glob("*.html"):
                with open(section_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    total_split_size += len(content)
                    logger.info(f"📊 {section_file.name}: {len(content)} caractères")
            
            logger.info(f"📈 Taille originale: {total_original_size}")
            logger.info(f"📈 Taille totale découpée: {total_split_size}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la validation: {e}")
            return False
    
    def run(self) -> bool:
        """Exécute le découpage complet."""
        logger.info("🚀 Début du découpage HTML Absolute Zero")
        
        if not self.load_html():
            return False
        
        if not self.split_html_by_sections():
            return False
        
        if not self.validate_split():
            return False
        
        logger.info("✅ Découpage terminé avec succès!")
        return True

def main():
    """Fonction principale."""
    input_file = "analyse_rollouts/AZR/Absolute Zero_ Reinforced Self-play Reasoning with Zero Data.html"
    output_dir = "analyse_rollouts/AZR_Decoupage"
    
    splitter = AbsoluteZeroHTMLSplitter(input_file, output_dir)
    success = splitter.run()
    
    if success:
        print("🎯 DÉCOUPAGE RÉUSSI!")
        print(f"📁 Fichiers créés dans: {output_dir}")
        print("📋 10 parties complètes générées sans perte d'information")
    else:
        print("❌ ÉCHEC du découpage")

if __name__ == "__main__":
    main()
