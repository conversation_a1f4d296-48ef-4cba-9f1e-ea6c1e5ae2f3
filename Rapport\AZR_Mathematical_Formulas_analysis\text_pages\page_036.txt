🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
MATH REASONING

CODE REASONING

OVERALL PERFORMANCE

50

100

150

200

250

300

350

  0.050
  0.100
  0.150
  0.200
Accuracy

AIME 2024

50

100

150

200

250

300

350

  0.000
  0.020
  0.040
  0.060
  0.080
  0.100
AIME 2025

50

100

150

200

250

300

350

  0.340
  0.360
  0.380
Olympiad Bench

50

100

150

200

250

300

350

  0.275
  0.300
  0.325
  0.350
  0.375
Accuracy

Minerva

50

100

150

200

250

300

350

  0.680
  0.700
  0.720
  0.740
  0.760
Math 500

50

100

150

200

250

300

350

  0.400
  0.450
  0.500
  0.550
  0.600
AMC 2023

50

100

150

200

250

300

350

  0.810
  0.820
  0.830
  0.840
  0.850
Accuracy

HumanEval+

50

100

150

200

250

300

350

  0.680
  0.690
  0.700
  0.710
  0.720
MBPP+

50

100

150

200

250

300

350

  0.260
  0.280
  0.300
  0.320
LiveCodeBench

50

100

150

200

250

300

350

  0.320
  0.340
  0.360
  0.380
  0.400
Accuracy

Math Average

50

100

150

200

250

300

350

  0.580
  0.590
  0.600
  0.610
  0.620
  0.630
Code Average

50

100

150

200

250

300

350

  0.460
  0.480
  0.500
Overall Average

🔗 Figure 29. Absolute Zero Reasoner-Coder-7b OOD Performance Breakdown.
🔗 36