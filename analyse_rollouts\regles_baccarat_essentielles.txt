RÈGLES ESSENTIELLES DU BACCARAT (PLAYER BANKER)
===============================================

🎯 RÈGLES FONDAMENTALES
-----------------------

Configuration :
- Adversaire : Le joueur joue contre la Maison
- Cartes : 8 jeux de 52 cartes standard (416 cartes total)
- Deux mains : BANKER (Banquier) et PLAYER (Joueur)
- Cut card : Placée au 3/4 du paquet, après la 312ème carte

Valeur des cartes :
- As = 1 point
- <PERSON><PERSON> 2-9 = <PERSON><PERSON> faciale
- 10, <PERSON><PERSON>, <PERSON>, Roi = 0 point
- Si total > 9 : On garde seulement le chiffre des unités (ex: 15 = 5)

🎲 DÉROULEMENT D'UNE PARTIE
---------------------------

0. Brûlage des cartes (une seule fois avant toutes les manches) :
- Le croupier tire 1 carte et la montre
- Il tire ensuite autant de cartes supplémentaires que la valeur de cette carte
- Exception : Si 10, J, Q, K → il tire 10 cartes supplémentaires
- Toutes ces cartes sont mises de côté et ne sont pas réutilisées
- Nombre de cartes brûlées : MINIMUM 2 cartes (As) / MAXIMUM 11 cartes (10,J,Q,K)

1. Phase de mise :
- Les paris doivent être placés AVANT la distribution
- 3 options de paris :
  * BANKER : Pari sur la victoire du banquier
  * PLAYER : Pari sur la victoire du joueur
  * TIE : Pari sur une égalité
- Aucune modification après le début de la distribution

2. Distribution :
- 2 cartes distribuées face visible à chaque main
- Minimum : 2 cartes par main
- Maximum : 3 cartes par main

📋 RÈGLES DE TIRAGE OBLIGATOIRES
--------------------------------

POUR LE JOUEUR (PLAYER) :
- 0-1-2-3-4-5 : TIRE une 3ème carte
- 6-7 : RESTE (ne tire pas)
- 8-9 : NATUREL (partie terminée)

POUR LE BANQUIER (BANKER) :

Si le joueur RESTE (a 6 ou 7) :
- 0-1-2-3-4-5 : TIRE une 3ème carte
- 6-7 : RESTE
- 8-9 : NATUREL

Si le joueur a tiré une 3ème carte :

Total Banker | TIRE si 3ème carte joueur = | RESTE si 3ème carte joueur =
-------------|----------------------------|-----------------------------
3            | 0-1-2-3-4-5-6-7-9         | 8
4            | 2-3-4-5-6-7               | 0-1-8-9
5            | 4-5-6-7                   | 0-1-2-3-8-9
6            | 6-7                       | 0-1-2-3-4-5-8-9
7            | RESTE TOUJOURS            | RESTE TOUJOURS
8-9          | NATUREL                   | NATUREL

🏆 DÉTERMINATION DU GAGNANT
---------------------------

- La main la plus proche de 9 gagne
- Naturel (8 ou 9) : Résultat immédiat, aucune carte supplémentaire
- En cas d'égalité : TIE déclaré

💰 PAIEMENTS
------------

- BANKER gagnant : 19-20 (0.95:1) - Commission de 5%
- PLAYER gagnant : 1-1 (paiement égal)
- TIE : 9-1
- Si TIE : Les paris Banker et Player sont remboursés

Avantage de la maison :
- BANKER : 1.06%
- PLAYER : 1.24%
- TIE : 4.84%

🔢 COMPTAGE PAIR/IMPAIR DES CARTES
----------------------------------

Principe général :
À chaque distribution (brûlage ou manche), on compte le nombre total de cartes
sur le tapis et on détermine si ce nombre est PAIR ou IMPAIR.

🔥 BRÛLAGE (une seule fois) :
- Nombre de cartes brûlées : 2 à 11 cartes
- Résultats possibles :
  * PAIR : 2, 4, 6, 8, 10 cartes
  * IMPAIR : 3, 5, 7, 9, 11 cartes

🎲 MANCHES (répétées) :
- Nombre de cartes par manche : 4, 5 ou 6 cartes
- Résultats possibles :
  * PAIR : 4 cartes (aucune 3ème carte) ou 6 cartes (deux 3èmes cartes)
  * IMPAIR : 5 cartes (une seule 3ème carte)

📊 SYNTHÈSE :
Une partie = 1 brûlage + X manches
Chaque événement (brûlage ou manche) produit un résultat PAIR ou IMPAIR
selon le nombre total de cartes distribuées à ce moment.

Exemples :
- Brûlage : As tiré → 2 cartes → PAIR
- Manche 1 : 4 cartes initiales + 1 troisième → 5 cartes → IMPAIR
- Manche 2 : 4 cartes initiales + 0 troisième → 4 cartes → PAIR

⚠️ RÈGLES IMPORTANTES
---------------------

1. Aucune décision du joueur - Tout est automatique selon le tableau
2. Pas de règles optionnelles - Le tableau de tirage est strict
3. Une mise = un coup - Les mises ne sont valides que pour un coup
4. Objectif : Se rapprocher le plus possible de 9
5. Fin de partie : La partie s'arrête dès que la cut card est atteinte


=== FIN DES RÈGLES ===

🎯 DISTINCTION FONDAMENTALE POUR BCT-AZR
========================================

⚠️ DEUX CONCEPTS TOTALEMENT DIFFÉRENTS À NE PAS CONFONDRE :

1️⃣ SCORE (Valeur des cartes) :
- Calcul : Somme des valeurs des cartes (modulo 10)
- Détermine : QUI GAGNE la manche
- Exemples :
  * BANKER : 7+2 = 9, PLAYER : 5+3 = 8 → BANKER gagne (9 > 8)
  * BANKER : 6+1 = 7, PLAYER : 4+3 = 7 → TIE (égalité des scores)
- Résultats possibles : BANKER / PLAYER / TIE

2️⃣ NOMBRE DE CARTES (Comptage physique) :
- Calcul : Nombre total de cartes distribuées dans la manche
- Détermine : PAIR ou IMPAIR selon le décompte physique
- Possibilités :
  * pair_4 : 4 cartes (2+2, aucune 3ème carte)
  * impair_5 : 5 cartes (2+2+1, une seule 3ème carte)
  * pair_6 : 6 cartes (2+2+2, deux 3èmes cartes)

🔍 TERMINOLOGIE PRÉCISE
======================

Une main c'est lorsque des cartes sont distribuées.
Une manche c'est lorsque lors d'une main il y a un Player ou un Banker.
Une partie complète comporte 60 manches.

⚠️ RÈGLES IMPORTANTES DU COMPTAGE :
- S'il y a un TIE (correspond à '--' dans le programme) le compteur de manches ne s'incrémente pas.
- Depuis la main 0 jusqu'à la fin de la partie, les cartes sont distribuées une seule fois à chaque main.
- Le compteur de manche s'incrémente seulement s'il y a un P ou un B.
- Après la main 0 : pour toutes les mains après la main 0, les 3 seules possibilités sont : pair_4 / pair_6 / impair_5 pour l'index 1
- C'est-à-dire qu'après la main 0, chaque main sera composée soit de 4, 5 ou 6 cartes.

La main 0 correspond au brûlage des cartes (nombre de cartes brûlées lors du brûalge : min=2 ; max=11).

=== COMPRÉHENSION CORRIGÉE ET PRÉCISÉE ===
