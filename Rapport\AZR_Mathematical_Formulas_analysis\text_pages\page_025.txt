🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
0

40

80

120

160

200

240

280

  0.0
  0.2
  0.4
  0.6
  0.8
  1.0
Reward

0

40

80

120

160

200

240

280

400

600

800

1000

1200

1400

Token Length

Training Steps

Deduction Task

Solve

Propose

🔗 Figure 17. Deduction Task Reward and Token Lengths. The task reward and token lengths of the two roles for deduction task type of
🔗 Absolute Zero Reasoner-base-7b.
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Figure 17. Deduction Task Reward and Token Lengths. The task reward and token lengths of the two roles for deduction task type of
🔗 Absolute Zero Reasoner-base-7b.
🔗 Task: Propose Deduction Task
🔗 Model Input: (k random selected example programs as few-shot input)
🔗 Model Output:
🔗 This task requires creating a new Python function that takes an input and returns a value. The function should have at least one
🔗 input parameter, be deterministic, and require state tracking across multiple data transformations. The input should be a test case
🔗 provided to a subject for the purpose of deducing the output. The code and the output should be challenging enough to require
🔗 algorithmic reasoning without using restricted words or packages.
🔗 def f(prices: list[int], budget: int):
🔗 n = len(prices)
🔗 profit = [0] * n
🔗 for i in range(1, n):
🔗 for j in range(i):
🔗 if prices[i] > prices[j]:
🔗 profit[i] = max(profit[i], profit[j] + prices[i] - prices[j])
🔗 return max(profit + [profit[-1] - prices[-1] if budget >= prices[-1] else 0])
🔗 Input: [7, 1, 5, 3, 6, 4], 7
🔗 Figure 18. An example response of proposing a deduction task. k programs are selected as few-shot examples to the model. The generated
🔗 new program will then be verified through execution.
🔗 24
🔗 Figure 18. Propose Deduction Task Example. An example response of proposing a deduction task. k programs are selected as few-shot
🔗 examples to the model. The generated new program will then be verified through execution.
🔗 25