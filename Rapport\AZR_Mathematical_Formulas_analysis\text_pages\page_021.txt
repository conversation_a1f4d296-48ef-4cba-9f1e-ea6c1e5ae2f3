🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 A. Reinforcement Learning with Verifiable Rewards.
🔗 We use reinforcement learning to update our learner LLM, rewarding it based on a task-specific reward function rf, where the subscript
🔗 f indicates the task. The goal of the RL agent is to maximize the expected discounted sum of rewards. We adopt an online variant of RL,
🔗 REINFORCE++, which is optimized using the original PPO objective:

📐 FORMULE MATHÉMATIQUE:
    LPPO(θ) = Eq∼P (Q), o∼πθold (O|q)


📐 FORMULE MATHÉMATIQUE:
    "

🔗 1

📐 FORMULE MATHÉMATIQUE:
    |o|


📐 FORMULE MATHÉMATIQUE:
    |o|


📐 FORMULE MATHÉMATIQUE:
    X


📐 FORMULE MATHÉMATIQUE:
    t=1


📐 FORMULE MATHÉMATIQUE:
    min  


📐 FORMULE MATHÉMATIQUE:
    st(θ)Anorm


📐 FORMULE MATHÉMATIQUE:
    f,q , clip (st(θ), 1 −ϵ, 1 + ϵ) Anorm


📐 FORMULE MATHÉMATIQUE:
    f,q


📐 FORMULE MATHÉMATIQUE:
    


📐 FORMULE MATHÉMATIQUE:
    #


📐 FORMULE MATHÉMATIQUE:
    ,

🔗 (9)
🔗 where st(θ) is the probability ratio between the new and old policies at timestep t, and Anorm
🔗 f,q is the normalized advantage.
🔗 REINFORCE++ computes the normalized advantage as:
🔗 Anorm

📐 FORMULE MATHÉMATIQUE:
    f,q = rf,q −mean({Af,q}B)


📐 FORMULE MATHÉMATIQUE:
    std({Af,q}B)


📐 FORMULE MATHÉMATIQUE:
    ,

🔗 (10)
🔗 where rf,q is the outcome reward for question q, task f, mean and std are calculated across the global batch with batch size B. Note that
🔗 we do not apply any KL penalty to the loss or reward.
🔗 B. Implementation Details
🔗 We built Absolute Zero Reasoner upon the veRL codebase (Sheng et al., 2025). For code execution, we incorporated components from
🔗 the QwQ Python executor. For safer code execution, we recommend using API-based services such as E2B instead.
🔗 All experiments were conducted on clusters of A800 GPUs.
🔗 Training Hyperparameters.
🔗 We show the hyperparameters used in our training in Table 3. We do not change them for any of
🔗 the runs.
🔗 Parameter
🔗 Value
🔗 Model Configuration
🔗 Max Prompt Length
🔗 6144
🔗 Max Response Length
🔗 8096
🔗 Seed Batch Factor
🔗 4
🔗 Max Programs
🔗 16384
🔗 Training Settings
🔗 Train Batch Size
🔗 64 * 6
🔗 Learning Rate
🔗 1e-6
🔗 Optimizer
🔗 AdamW
🔗 Grad Clip
🔗 1.0
🔗 Total Steps
🔗 500
🔗 RL Settings
🔗 Algorithm
🔗 TRR++ (Section 3.3.5)
🔗 KL Loss
🔗 False
🔗 KL Reward
🔗 False
🔗 Entropy Coefficient
🔗 0.001
🔗 PPO Epochs
🔗 1
🔗 N Rollouts
🔗 1
🔗 Rollout Temperature
🔗 1.0
🔗 Rollout Top-P
🔗 1.0
🔗 K References
🔗 6
🔗 N Samples to Estimate Task Accuracy
🔗 8
🔗 Table 3. Hyperparameters Used During AZR Self-play Training.
🔗 21