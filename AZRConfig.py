################################################################################
#                                                                              #
#  📋 SECTION 1 : CONFIGURATION CENTRALISÉE AZR                               #
#                                                                              #
################################################################################

import multiprocessing
from typing import Dict, Any

class AZRConfig:
    """
    Configuration centralisée pour tous les paramètres AZR
    
    PRINCIPE : Aucune valeur codée en dur dans les méthodes
    Toutes les spécialisations des 8 clusters centralisées ici
    """
    
    def __init__(self):
        # ====================================================================
        # PARAMÈTRES SYSTÈME FONDAMENTAUX
        # ====================================================================
        
        # Architecture BCT (3 rollouts seulement)
        self.nb_rollouts = 3  # BCT utilise 3 rollouts spécialisés
        self.nb_cores = multiprocessing.cpu_count()  # 8 cœurs
        self.max_memory_gb = 28
        
        # Valeurs de base universelles
        self.zero_value = 0
        self.one_value = 1
        self.two_value = 2
        self.three_value = 3
        
        # ====================================================================
        # SYSTÈME DE COMPTAGE BACCARAT (4 INDEX + 1 CALCULÉ)
        # ====================================================================
        
        # INDEX 1 : Comptage PAIR/IMPAIR des cartes
        self.card_count_categories = {
            'pair_4': 4,    # Aucune 3ème carte
            'pair_6': 6,    # Deux 3èmes cartes
            'impair_5': 5   # Une 3ème carte
        }
        
        # Brûlage possible (2-11 cartes)
        self.burn_card_range = {
            'min': 2,
            'max': 11,
            'categories': {
                'pair_2': 2, 'pair_4': 4, 'pair_6': 6, 'pair_8': 8, 'pair_10': 10,
                'impair_3': 3, 'impair_5': 5, 'impair_7': 7, 'impair_9': 9, 'impair_11': 11
            }
        }
        
        # INDEX 2 : États SYNC/DESYNC
        self.sync_states = ['SYNC', 'DESYNC']
        self.initial_sync_mapping = {
            'PAIR': 'SYNC',
            'IMPAIR': 'DESYNC'
        }

        # INDEX 3 : Résultats P/B/T (ancien INDEX 4)
        self.game_results = ['PLAYER', 'BANKER', 'TIE']
        self.pb_results = ['PLAYER', 'BANKER']  # Pour calculs S/O

        # INDEX 4 : Conversions S/O (ancien INDEX 5)
        self.so_conversions = ['S', 'O', '--']  # Same, Opposite, Première manche

        # ====================================================================
        # LIMITES ET CONTRAINTES
        # ====================================================================
        
        # Limites par partie
        self.max_manches_per_game = 60  # Fenêtre de 60 manches (2^60 possibilités)
        self.max_hands_per_game = 100   # Incluant TIE
        
        # ====================================================================
        # CONFIGURATION BCT PRINCIPALE (3 ROLLOUTS SEULEMENT)
        # ====================================================================

        # BCT utilise 3 rollouts spécialisés au lieu de 8 clusters
        # Configuration simplifiée et optimisée pour le système ternaire
    
    def get_rollout_params(self, rollout_id: int) -> Dict[str, Any]:
        """
        Retourne les paramètres spécialisés pour un rollout donné

        MÉTHODE UNIVERSELLE BCT : Utilisée par les 3 rollouts pour obtenir
        leurs paramètres spécialisés (remplace l'ancien système de clusters)
        """
        return self.get_bct_rollout_params(rollout_id)
    
    def get_system_limits(self) -> Dict[str, int]:
        """Retourne les limites système pour optimisation mémoire/CPU"""
        return {
            'nb_rollouts': 3,  # BCT utilise 3 rollouts seulement
            'nb_cores': self.nb_cores,
            'max_memory_gb': self.max_memory_gb,
            'memory_per_rollout_mb': (self.max_memory_gb * 1024) // 3
        }

    def get_bct_rollout_params(self, rollout_id: int) -> Dict[str, Any]:
        """
        🎯 CONFIGURATION UNIVERSELLE BCT - SYSTÈME RÉVOLUTIONNAIRE

        Configuration centralisée pour les 3 rollouts du cluster principal BCT.
        Permet l'universalisation des méthodes : 1 méthode → 3 comportements.

        SYSTÈME TERNAIRE BCT :
        - pair_4 : 4 cartes distribuées (aucune 3ème carte) - PRIORITÉ 3
        - impair_5 : 5 cartes distribuées (une 3ème carte) - PRIORITÉ 1 (commutateur d'état)
        - pair_6 : 6 cartes distribuées (deux 3èmes cartes) - PRIORITÉ 2

        ROLLOUTS SPÉCIALISÉS :
        - Rollout 1 (Analyseur) : Détection biais structurels ≤ 60ms
        - Rollout 2 (Générateur) : Génération séquences ≤ 50ms
        - Rollout 3 (Prédicteur) : Prédiction finale S/O ≤ 60ms

        Args:
            rollout_id: 1 (Analyseur), 2 (Générateur), 3 (Prédicteur)

        Returns:
            Dict: Configuration spécialisée pour le rollout
        """
        # Configuration de base ternaire BCT
        bct_base = {
            # ================================================================
            # SYSTÈME TERNAIRE BCT (ORDRE DE PRIORITÉ)
            # ================================================================
            'pair_4_threshold': 0.5,           # PRIORITÉ 3 (minimale)
            'impair_5_threshold': 0.5,         # PRIORITÉ 1 (maximale - commutateur d'état)
            'pair_6_threshold': 0.5,           # PRIORITÉ 2 (moyenne)

            # Ordre de priorité d'analyse (SANS BIAIS ARBITRAIRES)
            'impair_5_priority': 1,            # PRIORITÉ MAXIMALE
            'pair_6_priority': 2,              # PRIORITÉ MOYENNE
            'pair_4_priority': 3,              # PRIORITÉ MINIMALE

            # ================================================================
            # TIMING OPTIMISÉ (≤ 170ms TOTAL)
            # ================================================================
            'max_analysis_time_ms': 60,        # Temps maximum par rollout
            'correlation_depth': 10,           # Profondeur corrélations
            'sequence_max_length': 20,         # Longueur max séquences

            # ================================================================
            # ÉTATS COMBINÉS CALCULÉS DYNAMIQUEMENT
            # ================================================================
            # Note: Les états combinés sont maintenant calculés à la demande
            # via la propriété combined_state de BaccaratHand

            # ================================================================
            # EXPLOITATION BIAIS STRUCTURELS (SANS SEUILS ARBITRAIRES)
            # ================================================================
            'bias_exploitation_enabled': True,     # Exploitation activée
            'correlation_analysis_enabled': True,  # Analyse corrélations
            'confidence_minimum': 0.0,             # Pas de minimum forcé
            'priority_based_analysis': True,       # Analyse basée sur priorités

            # ================================================================
            # PARAMÈTRES SYSTÈME BCT
            # ================================================================
            'system_type': 'bct_ternary',          # Type système
            'avoid_averages': True,                # Anti-moyennes
            'exploit_structural_bias': True        # Exploitation biais
        }

        # ================================================================
        # SPÉCIALISATIONS PAR ROLLOUT
        # ================================================================

        if rollout_id == 1:  # 🔍 ROLLOUT 1 : ANALYSEUR
            return {
                **bct_base,
                # Spécialisation analyse (SANS SEUILS ARBITRAIRES)
                'correlation_depth': 15,           # Analyse plus profonde
                'max_analysis_time_ms': 60,        # Temps max analyse
                'priority_impair_5': True,         # Priorité impair_5
                'analyze_all_correlations': True,  # Toutes corrélations
                'rollout_specialization': 'analyzer'
            }

        elif rollout_id == 2:  # 🎯 ROLLOUT 2 : GÉNÉRATEUR
            return {
                **bct_base,
                # Spécialisation génération (SANS BIAIS)
                'max_analysis_time_ms': 50,        # Plus rapide
                'generation_diversity': 0.8,       # Diversité séquences
                'sequence_optimization': True,     # Optimisation séquences
                'exploit_ternary_patterns': True,  # Patterns ternaires
                'rollout_specialization': 'generator'
            }

        elif rollout_id == 3:  # 🏆 ROLLOUT 3 : PRÉDICTEUR
            return {
                **bct_base,
                # Spécialisation prédiction (SANS BIAIS)
                'correlation_depth': 8,            # Plus rapide
                'max_analysis_time_ms': 60,        # Temps max prédiction
                'so_prediction_optimization': True, # Optimisation S/O
                'rollout_specialization': 'predictor'
            }

        # Configuration par défaut (rollout_id invalide)
        return {
            **bct_base,
            'rollout_specialization': 'default',
            'error': f'rollout_id {rollout_id} invalide, utilisation configuration par défaut'
        }