🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Task: Solve Deduction Task (Llama 8B)
🔗 Model Input: Here is the function f, the input provided 'cookie', and we need to predict the output.
🔗 def f(input_string: str):
🔗 """Function to check if a string contains given substrings."""
🔗 substring = "code"
🔗 # Start with `substring` as the first substring.
🔗 curr_index = substring
🔗 # Slice out the required substring from the input string
🔗 sliced_out_string = input_string[0:len(substring)]
🔗 if (sliced_out_string == substring):
🔗 return True
🔗 # Check if there are any remaining substrings to be checked
🔗 if (len(curr_index) > len(input_string)):
🔗 return False
🔗 return False
🔗 Input: 'cookie'
🔗 Output: False
🔗 Model Output:

📐 FORMULE MATHÉMATIQUE:
    I know that the line ‘curr_index = substring‘ is just initialization of the curr_index parameter variable and does not affect the output.

🔗 Hence, I can remove it completely to view the status of code before any output gets generated.
🔗 Now I have a single variable, “‘curr_index“‘ which is set to ”codecandide”.

📐 FORMULE MATHÉMATIQUE:
    The second line of code, ‘slice_out_string = input_string[0:len(substring)]‘, assigns the result of slicing the input_string to the left

🔗 of index 0 and to the right of index len(substring), i.e., the index of the last character in the substring. It then assigns this value to
🔗 the variable “‘slice_out_string“‘.

📐 FORMULE MATHÉMATIQUE:
    The third line, ‘if (slice_out_string == substring)‘ checks whether the “‘slice_out_string“‘ is equal to the value of substring. Because

🔗 curr_index is set to ”codecandide”, ”code” appears on the left of the first index in input_string, and thus, “‘slice_out_string“‘ assumes
🔗 the value ”code”. That means for a minute the “‘if statement“‘ is satisfied and will continue the code execution afterwards.
🔗 However, according to the True/False statement, we see that the value of the first index of input_string does not exist hence while it
🔗 does output ”True”, ”False” is outputted in main. So, the output of the whole program is shown below:

📐 FORMULE MATHÉMATIQUE:
    ‘f(input_string = ”cookie”)‘ then output: False

🔗 Figure 26. Absolute Zero Reasoner-Llama3.1-8b CoT Example on Solving Output Deduction Task.
🔗 The Absolute Zero
🔗 Reasoner-Llama3.1-8b model appears to have strengthened its state-tracking behaviors during the course of training. We illustrate an
🔗 example here.
🔗 Figure 27. Metrics on Proposed Tasks. We break down the proposed task metrics into program complexity and diversity across programs
🔗 and answers. An upward trend is observed in all metrics, indicating that AZR implicitly optimizes for these qualities as training progresses.
🔗 35
🔗 Figure 26. Absolute Zero Reasoner-Llama3.1-8b CoT Example on Solving Output Deduction Task.
🔗 The Absolute Zero
🔗 Reasoner-Llama3.1-8b model appears to have strengthened its state-tracking behaviors during the course of training. We illus-
🔗 trate an example here.
0

80

160

240

0

  0.47
ComplexiPy Score

0

80

160

240

0

  0.20
Halstead Measure

0

80

160

240

0

  0.78
AST Edit Distance

0

80

160

240

0

  0.99
Answer Diversity

Training Steps

Complexity

Diversity

🔗 Figure 27. Metrics on Proposed Tasks. We break down the proposed task metrics into program complexity and diversity across programs
🔗 and answers. An upward trend is observed in all metrics, indicating that AZR implicitly optimizes for these qualities as training progresses.
🔗 34