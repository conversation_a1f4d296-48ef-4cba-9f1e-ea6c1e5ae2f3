# 📚 NOUVELLES DÉCOUVERTES - SECTIONS 3.2 & 3.3 AZR

## 📋 INFORMATIONS GÉNÉRALES

**Sections analysées :** 3.2 (Learning Different Modes of Reasoning) & 3.3 (Absolute Zero Reasoner Learning Algorithm)
**Date d'extraction :** 12 juin 2025
**Source :** Image fournie par l'utilisateur - Sections 3.2 & 3.3 du paper AZR
**Statut :** Informations complémentaires ajoutées aux fichiers existants

---

## 🧠 **SECTION 3.2 : MODES DE RAISONNEMENT DÉTAILLÉS**

### **🎯 Triplet Fondamental AZR**
```
(p, i, o) où :
- p ∈ P : programme (fonction de code)
- i ∈ I : input (entrée du programme)  
- o ∈ O : output (sortie du programme)
```

### **🔍 MODE DEDUCTION : Prédiction logique étape par étape**

#### **PROPOSER (Deduction)**
```
- AZR conditionné sur type tâche α = deduction
- <PERSON><PERSON>ère K exemples de référence depuis buffer P_deduction
- Environment exécute p(i) pour calculer o
- Triplet (p, i, o) ajouté au buffer si pas d'erreur de sortie
```

#### **SOLVER (Deduction)**
```
- Modèle prédit output o étant donné programme p et input i
- Vérification avec égalité type-aware en Python
- Gère variations (ordre des sets, fractions, etc.)
- Capture step-by-step logical reasoning
```

### **🔮 MODE ABDUCTION : Inférence plausible depuis output**

#### **PROPOSER (Abduction)**
```
- Politique π^propose génère (p, i) conditionné sur o
- Type tâche α = abduction changé en input du modèle
- Génère paire (p, i) conditionnée sur o + exemples de référence
- Infers plausible input given program and output
```

#### **SOLVER (Abduction)**
```
- Modèle reçoit (p, o) et prédit i_*
- Solution vérifiée en checkant si p(i_*) = o
- Note: Programmes peuvent ne pas être bijectifs
- Enables trial-and-error or online search
```

### **🌱 MODE INDUCTION : Synthèse depuis exemples input-output**

#### **PROPOSER (Induction)**
```
- AZR échantillonne programme valide depuis P_induction ∪ P_deduction
- Génère N nouveaux inputs + message m
- Utilise environment pour calculer outputs correspondants
- Représentation: (p, {(i^n, o^n)}, m) stockée dans buffer P_induction
```

#### **SOLVER (Induction)**
```
- Modèle reçoit première moitié des paires input-output + message m
- Doit synthétiser programme p_* qui mappe inputs cachés vers outputs
- Encourage généralisation et décourage overfitting via held-out examples
- Requires generalization from partial information
```

---

## 🔧 **SECTION 3.3 : ALGORITHME D'APPRENTISSAGE AZR**

### **📊 FIGURE 5 : SEED AZR ZERO TRIPLET**

#### **Programme Triplet d'Identité**
```python
# Le plus simple triplet fourni à AZR pour bootstrap
def f(x):
    return x

Input: "Hello World"
Output: "Hello World"
```

**Signification :** Ce triplet identité est le seul programme fourni à AZR pour initier son auto-apprentissage. Il illustre la capacité d'AZR à démarrer depuis un état minimal.

### **🚀 BUFFER INITIALIZATION (Section 3.3.1)**

#### **Processus d'Initialisation Détaillé**
```
1. Génération seed set de triplets valides avec base language model
2. Chaque prompt échantillonne jusqu'à K triplets depuis current set
3. Filtrage, exécution et validation des triplets
4. Stockage dans buffers appropriés selon type de tâche (deduction/abduction/induction)
```

#### **Formules d'Initialisation Spécialisées**
```
Pour Deduction et Abduction :
P^0_deduction = P^0_abduction = P_seed
où P_seed = B × S (B = batch size, S = facteur fixé à 4)

Pour Induction :
P^0_induction = B × S programmes échantillonnés depuis P_seed
Génère matching input sets et messages, collecte valid samples jusqu'à |P^0_induction| = B × S
```

#### **Variables Clés du Processus**
```
- B = batch size (taille du batch d'entraînement)
- S = 4 (facteur fixé dans tous les expériments)
- K = nombre d'exemples de référence par prompt
- N = nombre d'inputs générés pour induction
- P_seed = ensemble initial de triplets seed
```

### **🔄 Mécanisme de Bootstrap**

#### **Étapes du Bootstrap AZR**
```
1. Initialisation avec triplet identité minimal (Figure 5)
2. Génération de nouveaux triplets via base LLM
3. Validation et exécution des programmes générés
4. Expansion progressive des buffers par type de raisonnement
5. Auto-amélioration continue via feedback loop
```

#### **Gestion des Buffers Spécialisés**
```
P_deduction : Stockage des triplets pour raisonnement déductif
P_abduction : Stockage des triplets pour raisonnement abductif  
P_induction : Stockage des triplets pour raisonnement inductif
```

---

## 🎯 **INNOVATIONS TECHNIQUES RÉVÉLÉES**

### **🧠 Code Executor comme Interface Flexible**
- **Dual Role** : Interface ET environnement vérifiable
- **Automatic Construction** : Exécution et validation automatiques
- **Program Space** : Espace de programmes F, input space I, output space O

### **🔄 Trois Modes de Raisonnement Unifiés**
- **Deduction** : Logique forward (programme + input → output)
- **Abduction** : Logique backward (programme + output → input)
- **Induction** : Généralisation (exemples → programme)

### **📊 Système de Buffers Sophistiqué**
- **Séparation par type** : Buffers spécialisés pour chaque mode
- **Initialisation contrôlée** : Formules précises B × S
- **Bootstrap minimal** : Démarrage depuis triplet identité unique

### **⚡ Validation Déterministe**
- **Type-aware equality** : Gestion des variations Python
- **Execution verification** : Validation par exécution réelle
- **Error handling** : Gestion robuste des erreurs de programme

---

## 🔗 **LIENS AVEC LES FICHIERS EXISTANTS**

### **Fichiers Enrichis**
- ✅ `04_FONCTIONNEMENT_TECHNIQUE_AZR.md` : Détails sections 3.2 & 3.3 ajoutés
- ✅ `01_EQUATIONS_MATHEMATIQUES_AZR_FONDAMENTALES.md` : Équations buffers et triplets ajoutées

### **Cohérence avec Documentation Existante**
- **Triplet identité** : Confirme les mentions dans les synthèses existantes
- **Modes de raisonnement** : Précise les descriptions générales déjà présentes
- **Buffers** : Complète les références aux P_deduction/P_abduction/P_induction

### **Nouvelles Informations Uniques**
- **Détails précis des sections 3.2 & 3.3** avec formulations exactes du paper
- **Processus d'initialisation** avec formules B × S détaillées
- **Mécanismes de vérification** type-aware Python spécifiques
- **Spécificités des modes** (bijection, held-out examples, trial-and-error)

---

## 🚀 **IMPLICATIONS POUR L'ADAPTATION BCT**

### **Adaptation des Triplets pour BCT**
```python
# Triplet BCT adapté du triplet AZR (p, i, o)
(pattern_INDEX12, context_game, prediction_SO)

où :
- pattern_INDEX12 : Pattern détecté dans INDEX 1&2 (équivalent programme p)
- context_game : Contexte de jeu actuel (équivalent input i)
- prediction_SO : Prédiction S/O résultante (équivalent output o)
```

### **Modes de Raisonnement BCT**
```
DEDUCTION BCT : (pattern, context) → S/O
ABDUCTION BCT : (pattern, S/O) → context probable
INDUCTION BCT : {(context, S/O)} → nouveau pattern
```

### **Buffers BCT Spécialisés**
```
P_deduction_bct : Patterns déductifs INDEX 1&2 → INDEX 3&4
P_abduction_bct : Inférences causales S/O → Patterns probables
P_induction_bct : Règles générales synthétisées depuis historique
```

---

## 🔧 **ALGORITHME 1 COMPLET - DÉTAILS LIGNE PAR LIGNE**

### **📋 ALGORITHME 1 : SELF-PLAY TRAINING D'AZR**

#### **Paramètres d'Entrée**
```
Require:
- Pretrained base LLM π_θ
- Batch size B
- References K
- Iterations T
```

#### **Lignes 1-2 : Initialisation**
```python
1: P_ded, P_abd, P_ind ← INITSEEDING(π_θ)     # voir §3.3.1
2: for t ← 1 to T do                          # Boucle d'entraînement principale
```

#### **Lignes 3-7 : PROPOSE PHASE - Induction**
```python
3:   for b ← 1 to B do                        # ▷ PROPOSE PHASE
4:     p ∼ P_ind ∪ P_ded                      # ▷ sample a program for induction task proposal
5:     {i^n}_{n=1}, m_x ← π_θ^propose(ind, p)  # ▷ generate N inputs and a description
6:     {(i^n, o^n)} ← VALIDATEBYEXECUTING(p, {i^n}, SYNTAX) then  # ▷ validate I/Os, see §3.3.3
7:       P_ind ← P_ind ∪ {(p, {(i^n, o^n)}, m_x)}  # ▷ update induction buffer
```

#### **Lignes 8-12 : PROPOSE PHASE - Deduction & Abduction**
```python
8:   for α ∈ {ded, abd} do
9:     (p_k, i_k, o_k)^K_{k=1} ← P_α           # ▷ sample K reference examples
10:    (p_x, i_x) ← π_θ^propose(α, {(p_k, i_k, o_k)})  # ▷ propose new task
11:    if o_x ← VALIDATEBYEXECUTING(p_x, i_x, SYNTAX,SAFETY,DETERMINISM) then  # ▷ see §3.3.3
12:      P_α ← P_α ∪ {(p_x, i_x, o_x)}         # ▷ if valid, update deduction or abduction buffers
```

#### **Lignes 13-15 : SOLVE PHASE**
```python
13:  for all α ∈ {ded, abd, ind} do            # ▷ SOLVE PHASE
14:    (x, y*) ← SAMPLEPREPAREDTASKS(P_α, B, t)  # ▷ x, y* prepared based on α, see §3.3.4&3.4
15:    y_x ∼ π_θ^solve(x)                       # ▷ solve
```

#### **Lignes 16-17 : REWARD & RL UPDATE**
```python
16: Reward: Use proposed task triplets and solved answers to get r_propose & r_solve  # ▷ see §3.1
17: RL update: use Task Relative REINFORCE++ to update π_θ  # ▷ see §3.3.5
```

### **🔧 FONCTIONS CLÉS IMPLÉMENTÉES**

#### **VALIDATEBYEXECUTING - Validation Multi-Critères**
```python
def validate_by_executing(program, inputs, *validation_flags):
    """
    Validation complète selon les flags spécifiés

    Flags disponibles:
    - SYNTAX: Vérification syntaxe Python
    - SAFETY: Vérification packages dangereux (os, sys, shutil)
    - DETERMINISM: Vérification reproductibilité
    """
    results = []

    for flag in validation_flags:
        if flag == 'SYNTAX':
            if not check_python_syntax(program):
                return None
        elif flag == 'SAFETY':
            if uses_dangerous_packages(program):
                return None
        elif flag == 'DETERMINISM':
            if not is_deterministic(program, inputs):
                return None

    # Exécution si toutes validations passées
    try:
        outputs = execute_program_safely(program, inputs)
        return outputs
    except Exception:
        return None
```

#### **SAMPLEPREPAREDTASKS - Préparation par Mode**
```python
def sample_prepared_tasks(buffer, batch_size, iteration):
    """
    Échantillonnage et préparation selon le mode de raisonnement

    Returns:
        (x, y*): Problème formaté et solution de référence
    """
    prepared_tasks = []

    for _ in range(batch_size):
        triplet = buffer.sample_triplet()

        if buffer.mode == 'deduction':
            # Format: (program, input) → output
            x = format_deduction_task(triplet.program, triplet.input)
            y_star = triplet.output

        elif buffer.mode == 'abduction':
            # Format: (program, output) → input
            x = format_abduction_task(triplet.program, triplet.output)
            y_star = triplet.input

        elif buffer.mode == 'induction':
            # Format: {(input, output)} → program
            x = format_induction_task(triplet.io_pairs)
            y_star = triplet.program

        prepared_tasks.append((x, y_star))

    return prepared_tasks
```

#### **Task Relative REINFORCE++ (TRR++)**
```python
def task_relative_reinforce_plus_plus(rewards, task_types, roles):
    """
    Implémentation TRR++ avec 6 baselines séparées
    Réduction de variance optimale pour multi-tâches
    """
    # 6 baselines : 3 task_types × 2 roles
    baselines = {}

    for task_type in ['ded', 'abd', 'ind']:
        for role in ['propose', 'solve']:
            # Calcul baseline spécialisée
            mask = [(t == task_type and r == role)
                   for t, r in zip(task_types, roles)]

            if any(mask):
                baseline_rewards = [rewards[i] for i, m in enumerate(mask) if m]
                baselines[f"{task_type}_{role}"] = np.mean(baseline_rewards)
            else:
                baselines[f"{task_type}_{role}"] = 0.0

    # Calcul avantages normalisés
    advantages = []
    for reward, task_type, role in zip(rewards, task_types, roles):
        baseline = baselines[f"{task_type}_{role}"]
        advantage = reward - baseline
        advantages.append(advantage)

    return advantages
```

---

*Nouvelles découvertes des sections 3.2 & 3.3 - Compléments essentiels à la documentation AZR existante*
