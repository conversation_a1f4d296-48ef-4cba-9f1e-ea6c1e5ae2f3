🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Exploring the predictable. In Advances in evolutionary computing: theory and applications, pp. 579–612. Springer,
🔗 2003.
🔗 <PERSON><PERSON><PERSON><PERSON><PERSON>, J. POWERPLAY: training an increasingly general problem solver by continually searching for the simplest still unsolvable
🔗 problem. CoRR, abs/1112.5309, 2011. URL http://arxiv.org/abs/1112.5309.
🔗 <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>: Pushing the limits
🔗 of mathematical reasoning in open language models. CoRR, abs/2402.03300, 2024. doi: 10.48550/ARXIV.2402.03300. URL
🔗 https://doi.org/10.48550/arXiv.2402.03300.
🔗 <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, C. Hybridflow: A flexible and efficient
🔗 RLHF framework. In Proceedings of the Twentieth European Conference on Computer Systems, EuroSys 2025, Rotterdam,
🔗 The Netherlands, 30 March 2025 - 3 April 2025, pp. 1279–1297. ACM, 2025. doi: 10.1145/3689031.3696075. URL https:
🔗 //doi.org/10.1145/3689031.3696075.
🔗 Silver, D. and Sutton, R. S. The era of experience. https://storage.googleapis.com/deepmind-media/Era-of-Experience%
🔗 20/The%20Era%20of%20Experience%20Paper.pdf, 2025.
🔗 Silver, D., Huang, A., Maddison, C. J., Guez, A., Sifre, L., van den Driessche, G., Schrittwieser, J., Antonoglou, I., Panneershelvam,
🔗 V., Lanctot, M., Dieleman, S., Grewe, D., Nham, J., Kalchbrenner, N., Sutskever, I., Lillicrap, T. P., Leach, M., Kavukcuoglu, K.,
🔗 Graepel, T., and Hassabis, D. Mastering the game of go with deep neural networks and tree search. Nat., 529(7587):484–489, 2016.
🔗 doi: 10.1038/NATURE16961. URL https://doi.org/10.1038/nature16961.
🔗 Silver, D., Hubert, T., Schrittwieser, J., Antonoglou, I., Lai, M., Guez, A., Lanctot, M., Sifre, L., Kumaran, D., Graepel, T., Lillicrap,
🔗 T. P., Simonyan, K., and Hassabis, D. Mastering chess and shogi by self-play with a general reinforcement learning algorithm. CoRR,
🔗 abs/1712.01815, 2017. URL http://arxiv.org/abs/1712.01815.
🔗 Stuart, T. Understanding computation - from simple machines to impossible programs. O’Reilly, 2015. ISBN 978-1-449-32927-3. URL
🔗 http://www.oreilly.de/catalog/9781449329273/index.html.
🔗 Sukhbaatar, S., Lin, Z., Kostrikov, I., Synnaeve, G., Szlam, A., and Fergus, R. Intrinsic motivation and automatic curricula via
🔗 asymmetric self-play. In 6th International Conference on Learning Representations, ICLR 2018, Vancouver, BC, Canada, April 30 -
🔗 May 3, 2018, Conference Track Proceedings. OpenReview.net, 2018. URL https://openreview.net/forum?id=SkT5Yg-RZ.
🔗 Suteu, M. and Guo, Y. Regularizing deep multi-task networks using orthogonal gradients. CoRR, abs/1912.06844, 2019. URL
🔗 http://arxiv.org/abs/1912.06844.
🔗 Sutskever, I., Vinyals, O., and Le, Q. V. Neurips 2024 test of time award session: Sequence to sequence learning with neural networks.
🔗 Conference session, December 2024. URL https://neurips.cc/virtual/2024/test-of-time/105032.
🔗 Sutton, R. S. Verification, the key to ai. http://incompleteideas.net/IncIdeas/KeytoAI.html, 2001.
🔗 Team, K., Du, A., Gao, B., Xing, B., Jiang, C., Chen, C., Li, C., Xiao, C., Du, C., Liao, C., Tang, C., Wang, C., Zhang, D., Yuan, E., Lu,
🔗 E., Tang, F., Sung, F., Wei, G., Lai, G., Guo, H., Zhu, H., Ding, H., Hu, H., Yang, H., Zhang, H., Yao, H., Zhao, H., Lu, H., Li, H., Yu,
🔗 H., Gao, H., Zheng, H., Yuan, H., Chen, J., Guo, J., Su, J., Wang, J., Zhao, J., Zhang, J., Liu, J., Yan, J., Wu, J., Shi, L., Ye, L., Yu, L.,
🔗 Dong, M., Zhang, N., Ma, N., Pan, Q., Gong, Q., Liu, S., Ma, S., Wei, S., Cao, S., Huang, S., Jiang, T., Gao, W., Xiong, W., He, W.,
🔗 Huang, W., Wu, W., He, W., Wei, X., Jia, X., Wu, X., Xu, X., Zu, X., Zhou, X., Pan, X., Charles, Y., Li, Y., Hu, Y., Liu, Y., Chen,
🔗 Y., Wang, Y., Liu, Y., Qin, Y., Liu, Y., Yang, Y., Bao, Y., Du, Y., Wu, Y., Wang, Y., Zhou, Z., Wang, Z., Li, Z., Zhu, Z., Zhang,
🔗 Z., Wang, Z., Yang, Z., Huang, Z., Huang, Z., Xu, Z., and Yang, Z. Kimi k1.5: Scaling reinforcement learning with llms. CoRR,
🔗 abs/2501.12599, 2025. doi: 10.48550/ARXIV.2501.12599. URL https://doi.org/10.48550/arXiv.2501.12599.
🔗 Villalobos, P., Ho, A., Sevilla, J., Besiroglu, T., Heim, L., and Hobbhahn, M. Position: Will we run out of data? limits of LLM scaling
🔗 based on human-generated data. In Forty-first International Conference on Machine Learning, ICML 2024, Vienna, Austria, July
🔗 21-27, 2024. OpenReview.net, 2024. URL https://openreview.net/forum?id=ViZcgDQjyG.
🔗 Wang, H., Yue, Y., Lu, R., Shi, J., Zhao, A., Wang, S., Song, S., and Huang, G. Model surgery: Modulating LLM‘s behavior via
🔗 simple parameter editing. In Proceedings of the 2025 Conference of the Nations of the Americas Chapter of the Association for
🔗 Computational Linguistics, pp. 6337–6357, 2025a.
🔗 Wang, R., Lehman, J., Clune, J., and Stanley, K. O. Paired open-ended trailblazer (POET): endlessly generating increasingly complex
🔗 and diverse learning environments and their solutions. CoRR, abs/1901.01753, 2019. URL http://arxiv.org/abs/1901.01753.
🔗 Wang, S., Yang, Q., Gao, J., Lin, M. G., Chen, H., Wu, L., Jia, N., Song, S., and Huang, G. Train once, get a family: State-adaptive
🔗 balances for offline-to-online reinforcement learning. In Thirty-seventh Conference on Neural Information Processing Systems, 2023.
🔗 URL https://openreview.net/forum?id=vtoY8qJjTR.
🔗 17