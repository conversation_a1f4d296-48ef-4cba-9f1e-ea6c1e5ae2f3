🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 D. Alternative Approaches Considered
🔗 In this section, we share many of the approaches we tried that did not prove to be particularly helpful for Absolute Zero Reasoner.
🔗 However, we believe it is especially valuable to share these findings with the community, as they are crucial for guiding future research.
🔗 Below, we outline each of the additional methods we explored during the development of our project.
🔗 D.1. Error Deduction Task
🔗 Since programming languages often have error messages, and these messages contain a lot of information about how someone might
🔗 expect a program to run, we also came up with another task domain: allowing the learner to propose a program that will produce an
🔗 error, and requiring the solver to deduce what kind of error is raised when executing this code. We experimented with this additional
🔗 task alongside the induction (f), deduction (o), and abduction (i) tasks. Unfortunately, we did not observe noticeable changes in
🔗 downstream performance with this additional task and since it requires more computational resources than our AZR setup, we decided
🔗 not to incorporate it into our final version. However, we believe further thorough investigation of this is well deserved.
🔗 D.2. Composite Functions as Curriculum Learning
🔗 One valuable property we can leverage from programming languages is the ability to compose functions—that is, to define a function as
🔗 a composite of other functions, i.e., f(g(x)). In our setting, when generating a program, we can not only require the output to be a valid
🔗 program but also constrain the LLM to utilize a predefined set of programs within its main function. For example, if the target program
🔗 to be generated is f(·), we can sample a set of previously generated programs {g_0, . . . , gc} from D, and force a valid program to be

📐 FORMULE MATHÉMATIQUE:
    f(g_0, · · · , gc, i).

🔗 Since all programs are generated by the LLM itself, this setup allows the model to bootstrap from its earlier generations, automatically
🔗 increasing the complexity of the generated programs. We interpret this mechanism as a form of curriculum learning: earlier programs
🔗 in the AZR self-play loop tend to be simpler, and as the loop progresses, they become increasingly complex. By composing newer
🔗 programs from progressively more difficult earlier ones, the resulting programs naturally inherit this growing difficulty, which in turn
🔗 challenges the solver step.
🔗 For implementation, in generating tasks for abduction and deduction, we begin by sampling a binary decision from a binomial distribution
🔗 with p = 0.5. This determines whether the generated program should be a simple program or a composite one. If the sample is 0, we
🔗 prompt the LLM to generate a standard program along with a corresponding input. If the sample is 1, we prompt the LLM to generate a
🔗 composite program. To construct the composite, we first sample an integer c ∼U(1, 3), then uniformly select c programs from the
🔗 dataset D that are not themselves composite programs. Finally, we prompt the LLM to generate a valid program that incorporates

📐 FORMULE MATHÉMATIQUE:
    {g_0, . . . , gc} as subcomponents, ensuring it composes these selected programs meaningfully. We additionally filter programs that did

🔗 not utilize all the c programs.
🔗 However, we did not observe a significant difference when using this more complex curriculum compared to our simpler and more
🔗 effective approach. One failure mode we encountered was that the model often defaulted to simply returning “g(x)”, effectively learning

📐 FORMULE MATHÉMATIQUE:
    f(g(x)) = g(x), which failed to introduce any additional difficulty. This trivial behavior undermined the intended challenge, leading us

🔗 to deprioritize further exploration in this direction. While it may be possible to design a stricter reward mechanism—such as enforcing

📐 FORMULE MATHÉMATIQUE:
    f(g(x)) ̸= g(x) by executing the code via a Python interpreter and penalizing such shortcuts—we leave this to future work.

🔗 D.3. Toying with the Initial p(z)
🔗 We investigated a setting where the initial seed buffer (see Section 3.3.1 on how we generated these), i.e. p(z) in Equation (3), is not
🔗 self-generated by the base model, but instead sourced from the LeetCode Dataset. We only modified this component and ran AZR
🔗 using the same procedure as before, continuing to add new valid programs to the initialized buffer. We observed an increase in initial
🔗 performance on coding benchmarks; however, the performance plateaued at roughly the same level after additional training steps,
🔗 compared to our official AZR setup. Interestingly, math performance was lower than in the official AZR setup, pointing towards that
🔗 on-policy data may be more beneficial to the learner to bootstrap from for mathematical reasoning. We believe that exploring different
🔗 strategies for initializing and updating p(z) is an important and exciting direction for future research. We briefly explored different
🔗 strategies for sampling reference code, ultimately settling on uniform sampling for its simplicity, though we also experimented with
🔗 recency-based sampling and observed potential collapse.
🔗 D.4. Extra Rewards
🔗 Complexity Rewards.
🔗 Code complexity is well studied in software science and could potentially be a good proxy for measuring
🔗 how hard it is to infer the properties of a piece of code for our reasoning learner. Therefore, for the problem proposer, we can add various
🔗 measures of complexity—such as Cyclomatic Complexity (Ebert et al., 2016), maintainability, etc.—to the reward function to incentivize
🔗 the proposer to produce more complex programs. For illustration purposes, we tried using the Maintainability measure and the Halstead
🔗 49