🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 2025) used self-play to train on human-curated tasks to increase the critic capabilities and SPAG (<PERSON> et al., 2024) trained using
🔗 self-play in specific game of Adversarial Taboo. Concurrent works—Genius, EMPO, and TTRL (<PERSON> et al., 2025; <PERSON> et al., 2025b;
🔗 <PERSON> et al., 2025)—leverage human-curated language queries without labels to train reinforcement learning agents, but still rely on a
🔗 fixed human defined learning task distribution. Finally, <PERSON><PERSON> (<PERSON><PERSON> et al., 2024) extends self-play to formal mathematics, where a
🔗 pair of conjecture- and theorem-proving agents are jointly trained using reinforcement learning. Our work builds upon the self-play
🔗 paradigm, but it is the first to use it to elicit long CoT for improved reasoning, and the first to frame the problem space as a Python
🔗 input/output/function abduction/deduction/induction tasks, grounding it in an operationalizable environment to facilitate RLVR.
🔗 Weak-to-Strong Supervision.
🔗 The concept of weak-to-strong supervision has been studied in prior work, where a teacher—despite
🔗 being weaker than the learner—still provides useful guidance (<PERSON> et al., 2024; <PERSON><PERSON> et al., 2015; <PERSON><PERSON>, 2018; 2019; <PERSON><PERSON><PERSON> &
🔗 Garrabrant, 2019; Leike & Sutskever, 2023; <PERSON><PERSON><PERSON> et al., 2019). We consider a similar setting in which the learner may possess
🔗 superhuman capabilities. However, rather than relying on supervision from a weaker teacher, we propose an alternative approach:
🔗 guiding the learner’s improvement through verifiable rewards, which potentially offer a more reliable and scalable learning signal.
🔗 Furthermore, in our proposed method, the learning task and goal distribution is not predefined by any external supervisor—they are
🔗 entirely self-generated by the learner, enabling it to maximize its learning potential through autonomous self-practice.
🔗 6. Conclusion and Discussion
🔗 Conclusion.
🔗 In this work, we proposed the Absolute Zero paradigm, a novel setting that addresses the data limitations of existing
🔗 RLVR frameworks. In this paradigm, reasoning agents are tasked with generating their own learning task distributions and improving
🔗 their reasoning abilities with environmental guidance. We then presented our own instantiation, the Absolute Zero Reasoner (AZR),
🔗 which is trained by having them propose and solve code-related reasoning tasks grounded by code executor.
🔗 We evaluated our trained models on out-of-distribution benchmarks in both the code generation and mathematical reasoning domains.
🔗 Remarkably, even though our models were not directly trained on these tasks and lacked human expert-curated datasets, our reasoning
🔗 agents achieved exceptional performance, surpassing the state-of-the-art in combined general reasoning scores and in coding. This
🔗 demonstrates the potential of the absolute zero paradigm to drive superior reasoning capabilities without the need for extensive
🔗 domain-specific training data. Furthermore, we showed that AZR scales efficiently, offering strong performance across varying model
🔗 sizes, and can enhance the capabilities of other model classes as well. To foster further exploration and advancement of this emerging
🔗 paradigm, we are releasing the code, models, and logs as open-source, encouraging the research community to build upon our findings.
🔗 Discussion.
🔗 We believe there remains much to explore, such as altering the environment from which the reasoner receives verifiable
🔗 feedback, including sources like the world wide web, formal math languages (Sutton, 2001; Ren et al., 2025), world simulators, or even
🔗 the real world. Furthermore, AZ’s generality could possibly be extend to domains such as embodied AI (Zitkovich et al., 2023; Yue
🔗 et al., 2024). Additionally, more complex agentic tasks or scientific experiments, present exciting opportunities to further advance the
🔗 absolute zero setting to different application domains (Wu et al., 2024; 2023). Beyond that, future directions could include exploring
🔗 multimodal reasoning models, modifying the distribution p(z) to incorporate privileged information, defining or even let the model
🔗 dynamically learn how to define f (Equation (3)), or designing exploration/diversity rewards for both the propose and solve roles.
🔗 While underappreciated in current reasoning literature, the exploration component of RL has long been recognized as a critical driver for
🔗 emergent behavior in traditional RL (Yue et al., 2025; Silver et al., 2016; Ladosz et al., 2022). Years of research have examined various
🔗 forms of exploration, even in related subfields using LLMs such as red teaming (Zhao et al., 2025a), yet its role in LLM reasoning
🔗 models remains underexplored. Taking this a step further, our framework investigates an even more meta-level exploration problem:
🔗 exploration within the learning task space—where the agent learns not just how to solve tasks, but what tasks to learn from and how to
🔗 find them. Rather than being confined to a fixed problem set, AI reasoner agents may benefit from dynamically defining and refining
🔗 their own learning tasks. This shift opens a powerful new frontier—where agents explore not only solution spaces but also expand the
🔗 boundaries of problem spaces. We believe this is a promising and important direction for future research.
🔗 One limitation of our work is that we did not address how to safely manage a system composed of such self-improving components.
🔗 To our surprise, we observed several instances of safety-concerning CoT from the Llama-3.1-8B model, which we term the “uh-oh
🔗 moment”. These findings suggest that the proposed absolute zero paradigm, while reducing the need for human intervention for curating
🔗 tasks, still necessitates oversight due to lingering safety concerns and is a critical direction for future research (Wang et al., 2024; 2025a).
🔗 As a final note, we explored reasoning models that possess experience—models that not only solve given tasks, but also define and
🔗 evolve their own learning task distributions with the help of an environment. Our results with AZR show that this shift enables strong
🔗 performance across diverse reasoning tasks, even with significantly fewer privileged resources, such as curated human data. We believe
🔗 this could finally free reasoning models from the constraints of human-curated data (Morris, 2025) and marks the beginning of a new
🔗 chapter for reasoning models: “welcome to the era of experience” (Silver & Sutton, 2025; Zhao et al., 2024).
🔗 13