<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="UTF-8">
    <title>Absolute Zero: References</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.mathpix.com/fonts/cmu.css"/>
    <style>
  html,body {
    width: 100%;
    height: 100%;
  }
  *, *::before,*::after {
    box-sizing: border-box;
  }
  @-ms-viewport {
    width: device-width;
  }
  body {
    margin: 0;
    color: #1E2029;
    font-size: 14px;
    line-height: normal;
  }
  hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
  }
  h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: 0.5em;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }
  p {
    margin-top: 0;
    margin-bottom: 1em;
  }
  ol, ul, dl {
    margin-top: 0;
    margin-bottom: 1em;
  }
  ol ol, ul ul, ol ul, ul ol {
    margin-bottom: 0;
  }
  dt {
    font-weight: 500;
  }
  dd {
    margin-bottom: 0.5em;
    margin-left: 0;
  }
  blockquote {
    margin: 0 0 1em;
  }
  dfn {
    font-style: italic;
  }
  b, strong {
    font-weight: bolder;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  a {
    color: #0B93ff;
    text-decoration: none;
    background-color: transparent;
    outline: none;
    cursor: pointer;
    transition: color 0.3s;
  }
  a:hover {
    color: #33aaff;
  }
  a:active {
    color: #0070d9;
  }
  a:active, a:hover {
    text-decoration: none;
    outline: 0;
  }
  a[disabled] {
    color: rgba(0, 0, 0, 0.25);
    cursor: not-allowed;
    pointer-events: none;
  }
  pre, code, kbd, samp {
    font-size: 1em;
  }
  pre {
    margin-top: 0;
    margin-bottom: 1em;
    overflow: auto;
  }
  figure {
    margin: 0 0 1em;
  }
  img {
    vertical-align: middle;
    border-style: none;
  }
  svg:not(:root) {
    overflow: hidden;
  }
  table {
    border-collapse: collapse;
  }
  caption {
    padding-top: 0.75em;
    padding-bottom: 0.3em;
    color: rgba(0, 0, 0, 0.45);
    text-align: left;
    caption-side: bottom;
  }
  th {
    text-align: inherit;
  }

mjx-container[jax="SVG"] {
  direction: ltr;
}

mjx-container[jax="SVG"] > svg {
  overflow: visible;
  min-height: 1px;
  min-width: 1px;
}

mjx-container[jax="SVG"] > svg a {
  fill: blue;
  stroke: blue;
}

mjx-assistive-mml {
  position: absolute !important;
  top: 0px;
  left: 0px;
  clip: rect(1px, 1px, 1px, 1px);
  padding: 1px 0px 0px 0px !important;
  border: 0px !important;
  display: block !important;
  width: auto !important;
  overflow: hidden !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

mjx-assistive-mml[display="block"] {
  width: 100% !important;
}

mjx-container[jax="SVG"][display="true"] {
  display: block;
  text-align: center;
  margin: 1em 0;
}

mjx-container[jax="SVG"][display="true"][width="full"] {
  display: flex;
}

mjx-container[jax="SVG"][justify="left"] {
  text-align: left;
}

mjx-container[jax="SVG"][justify="right"] {
  text-align: right;
}

g[data-mml-node="merror"] > g {
  fill: red;
  stroke: red;
}

g[data-mml-node="merror"] > rect[data-background] {
  fill: yellow;
  stroke: none;
}

g[data-mml-node="mtable"] > line[data-line], svg[data-table] > g > line[data-line] {
  stroke-width: 70px;
  fill: none;
}

g[data-mml-node="mtable"] > rect[data-frame], svg[data-table] > g > rect[data-frame] {
  stroke-width: 70px;
  fill: none;
}

g[data-mml-node="mtable"] > .mjx-dashed, svg[data-table] > g > .mjx-dashed {
  stroke-dasharray: 140;
}

g[data-mml-node="mtable"] > .mjx-dotted, svg[data-table] > g > .mjx-dotted {
  stroke-linecap: round;
  stroke-dasharray: 0,140;
}

g[data-mml-node="mtable"] > g > svg {
  overflow: visible;
}

[jax="SVG"] mjx-tool {
  display: inline-block;
  position: relative;
  width: 0;
  height: 0;
}

[jax="SVG"] mjx-tool > mjx-tip {
  position: absolute;
  top: 0;
  left: 0;
}

mjx-tool > mjx-tip {
  display: inline-block;
  padding: .2em;
  border: 1px solid #888;
  font-size: 70%;
  background-color: #F8F8F8;
  color: black;
  box-shadow: 2px 2px 5px #AAAAAA;
}

g[data-mml-node="maction"][data-toggle] {
  cursor: pointer;
}

mjx-status {
  display: block;
  position: fixed;
  left: 1em;
  bottom: 1em;
  min-width: 25%;
  padding: .2em .4em;
  border: 1px solid #888;
  font-size: 90%;
  background-color: #F8F8F8;
  color: black;
}

foreignObject[data-mjx-xml] {
  font-family: initial;
  line-height: normal;
  overflow: visible;
}

mjx-container[jax="SVG"] path[data-c], mjx-container[jax="SVG"] use[data-c] {
  stroke-width: 3;
}

    #setText > div {
        justify-content: inherit;
        margin-top: 0;
        margin-bottom: 1em;
        
        
    }
    
    
    
    #setText div:last-child {
        margin-bottom: 0 !important;
    }

    #setText > br, #preview-content br {
        line-height: 1.2;
    }

    #preview-content > div {
        margin-top: 0;
        margin-bottom: 1em;
        
    }    
    
    .proof > div, .theorem > div {
        margin-top: 1rem;
    }

    #preview-content table {
      margin-bottom: 1em;
    }

    #setText table {
      margin-bottom: 1em;
    }
    
    #preview-content .sub-table table, #setText .sub-table table {
      margin-bottom: 0;
    }

    mjx-container {
      text-indent: 0;
      overflow-y: hidden;
      overflow-x: auto;
      padding-top: 1px;
      padding-bottom: 1px;
      
      
    }
    
    
    
    .math-inline mjx-container {
        display: inline-block !important;
        page-break-inside: avoid;
        max-width: 100%;
        padding: 0;
        line-height: 0;
    }
    .math-inline[data-overflow="visible"] mjx-container {
      overflow: visible;
    }
    .math-inline mjx-container mjx-assistive-mml {
      max-width: 100%;
    }
    .math-block {
        align-items: center;
        page-break-after: auto;
        page-break-inside: avoid;
        margin: 0;
        display: block; /* mjx-container has block */
    }
    
    .math-inline {
      display: inline-flex; /* mjx-container has inline-block. To prevent displacement during use overflow-x: auto;*/
      max-width: 100%;
    }
    
    .math-block[data-width="full"] {
      overflow-x: auto;
      display: flex; /* mjx-container has flex */
    }
    
    svg .math-inline {
      display: initial;
      max-width: initial;
    }
    
    svg .math-inline mjx-container {
      max-width: initial;
    }
    
    svg mjx-container {
      overflow: visible;
      padding: 0;
    }
    
    svg math-block[data-width="full"] {
      overflow: visible;
    }
    
    .math-block,.math-inline {
      --mmd-highlight-color: rgba(0, 147, 255, 0.25);
      --mmd-highlight-text-color: #1e2029;
    }

    .math-block[data-highlight-color] mjx-container[jax="SVG"] > svg {
      background-color: var(--mmd-highlight-color);
    }    
    
    .math-block[data-highlight-text-color] mjx-container[jax="SVG"] > svg {
      color: var(--mmd-highlight-text-color);
    }    
    .math-inline[data-highlight-color] mjx-container[jax="SVG"] {
      background-color: var(--mmd-highlight-color);
    }    
    
    .math-inline[data-highlight-text-color] mjx-container[jax="SVG"] {
      color: var(--mmd-highlight-text-color);
    }
    
    .math-block p {
        flex-shrink: 1;
    }
    .math-block mjx-container {
        margin: 0 !important;
    }
    .math-error {
        background-color: yellow;
        color: red;
    }

    #preview-content img, #setText img {
        max-width: 100%;
    }
    
    #preview-content blockquote,  #setText blockquote {
        page-break-inside: avoid;
        color: #666;
        margin: 0 0 1em 0;
        padding-left: 3em;
        border-left: .5em solid #eee;
    }

    #preview-content pre, #setText pre {
        border: none;
        padding: 0;
        overflow: auto;
        font-size: 85%;
        line-height: 1.45;
        border-radius: 6px;
        box-sizing: border-box;
        background: #f8f8fa;
    }
    #preview-content pre code, #setText pre code{
        padding: 1rem;
        display: block;
        overflow-x: auto;
        line-height: 24px;
    }
    .empty {
        text-align: center;
        font-size: 18px;
        padding: 50px 0 !important;
    }

    #setText table, #preview-content table {
        display: table; 
        overflow: auto;
        max-width: 100%;
        border-collapse: collapse;
        page-break-inside: avoid;
    }
      
    #setText table th, #preview-content table th {
        text-align: center;
        font-weight: bold;
    }
    
    #setText table td, #preview-content table td,
    #setText table th, #preview-content table th {
        border: 1px solid #dfe2e5;
        padding: 6px 13px;
    }
      
    #setText table tr, #preview-content table tr {
        background-color: #fff;
        border-top: 1px solid #c6cbd1;
    }
    
    #setText table tr:nth-child(2n), #preview-content table tr:nth-child(2n) {
        background-color: #f6f8fa;
    }

    
    #setText .main-title, #setText .author, #preview-content .main-title, #preview-content .author  {
        text-align: center;
        margin: 0 auto;
    }
    
    #preview-content .main-title, #setText .main-title {
        line-height: 1.2;
        margin-bottom: 1em;
    }

    #preview-content .author, #setText .author  {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }

    #preview-content .author p, #setText .author p {
        min-width: 30%;
        max-width: 50%;
        padding: 0 7px;
    }

    #preview-content .author > p > span, #setText .author > p > span {
        display: block;
        text-align: center;
    }

    #preview-content .section-title, #setText .section-title {
        margin-top: 1.5em;
    }

    #preview-content .abstract, #setText .abstract {
        text-align: justify;
        margin-bottom: 1em;
    }

    #preview-content .abstract p, #setText .abstract p {
        margin-bottom: 0;
    }

  #preview {
    font-family: 'CMU Serif', 'Georgia', Helvetica, Arial, sans-serif;
    font-size: 17px;
    visibility: visible;
    word-break: break-word;
    padding: 2.5em;
    max-width: 800px;
    margin: auto;
    box-sizing: content-box;
  }

  #preview h1, #preview h2, #preview h3, #preview h4, #preview h5, #preview strong {
    font-family: 'CMU Serif Bold', 'Georgia', Helvetica, Arial, sans-serif;
  }

  #preview  i, #preview  em {
    font-family: 'CMU Serif Italic', 'Georgia', Helvetica, Arial, sans-serif;
  }
</style>
</head>
<body>
  <div id="preview" class="preview scrollEditor">
    <div id="container-ruller" />
    <div id="preview-content">
<h2 type="section" data-unnumbered="true" class="section-title preview-paragraph-363 preview-line 363" id="references" data_line_start="363" data_line_end="363" data_line="363,364" count_line="1">
References</h2>
<div class="preview-paragraph-365 preview-line 365" data_line_start="365" data_line_end="365" data_line="365,366" count_line="1">Aryabumi, V., Su, Y., Ma, R., Morisot, A., Zhang, I., Locatelli, A., Fadaee, M., Üstün, A., and Hooker, S. To code, or not to code? exploring impact of code in pre-training. CoRR, abs/2408.10914, 2024. doi: 10.48550/ARXIV.2408.10914. URL <a href="https://doi.org/10.48550/arXiv.2408.10914" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2408.10914</a>.</div>
<div class="preview-paragraph-367 preview-line 367" data_line_start="367" data_line_end="367" data_line="367,368" count_line="1">Burns, C., Izmailov, P., Kirchner, J. H., Baker, B., Gao, L., Aschenbrenner, L., Chen, Y., Ecoffet, A., Joglekar, M., Leike, J., Sutskever, I., and Wu, J. Weak-to-strong generalization: Eliciting strong capabilities with weak supervision. In Forty-first International Conference on Machine Learning, ICML 2024, Vienna, Austria, July 21-27, 2024. <a href="http://OpenReview.net" target="_blank" rel="noopener" style="display: inline-block">OpenReview.net</a>, 2024. URL <a href="https://openreview.net/forum?id=ghNRg2mEgN" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=ghNRg2mEgN</a>.</div>
<div class="preview-paragraph-369 preview-line 369 370" data_line_start="369" data_line_end="370" data_line="369,371" count_line="2">Canal, M. Radon: Python tool for code metrics. <a href="https://github" target="_blank" rel="noopener" style="display: inline-block">https://github</a>. com/rubik/radon, 2023. Accessed: 2025-04-06.<br>
Chen, J., Zhang, B., Ma, R., Wang, P., Liang, X., Tu, Z., Li, X., and Wong, K.-Y. K. Spc: Evolving self-play critic via adversarial games for llm reasoning, 2025. URL <a href="https://arxiv.org/abs/2504" target="_blank" rel="noopener" style="display: inline-block">https://arxiv.org/abs/2504</a>. 19162.</div>
<div class="preview-paragraph-372 preview-line 372" data_line_start="372" data_line_end="372" data_line="372,373" count_line="1">Chen, Z., Deng, Y., Yuan, H., Ji, K., and Gu, Q. Self-play fine-tuning converts weak language models to strong language models. In Forty-first International Conference on Machine Learning, ICML 2024, Vienna, Austria, July 21-27, 2024. <a href="http://OpenReview.net" target="_blank" rel="noopener" style="display: inline-block">OpenReview.net</a>, 2024. URL <a href="https://openreview.net/forum?id=04cHTxW9BS" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=04cHTxW9BS</a>.</div>
<div class="preview-paragraph-374 preview-line 374" data_line_start="374" data_line_end="374" data_line="374,375" count_line="1">Cheng, P., Hu, T., Xu, H., Zhang, Z., Dai, Y., Han, L., Du, N., and Li, X. Self-playing adversarial language game enhances LLM reasoning. In Globersons, A., Mackey, L., Belgrave, D., Fan, A., Paquet, U., Tomczak, J. M., and Zhang, C. (eds.), Advances in Neural Information Processing Systems 38: Annual Conference on Neural Information Processing Systems 2024, NeurIPS 2024, Vancouver, BC, Canada, December 10-15, 2024, 2024. URL <a href="http://papers.nips.cc/paper_files/paper/2024/hash/" target="_blank" rel="noopener" style="word-break: break-all">http://papers.nips.cc/paper_files/paper/2024/hash/</a> e4be7e9867ef163563f4a5e90cec478f-Abstract-Conference.html.</div>
<div class="preview-paragraph-376 preview-line 376" data_line_start="376" data_line_end="376" data_line="376,377" count_line="1">Christiano, P. Approval-directed bootstrapping. <a href="https://www.alignmentforum.org/posts/6x7oExXi32ot6HjJv/" target="_blank" rel="noopener" style="word-break: break-all">https://www.alignmentforum.org/posts/6x7oExXi32ot6HjJv/</a> approval-directed-bootstrapping, 2018. AI Alignment Forum.</div>
<div class="preview-paragraph-378 preview-line 378" data_line_start="378" data_line_end="378" data_line="378,379" count_line="1">Christiano, P. Capability amplification. <a href="https://www.alignmentforum.org/posts/t3AJW5jP3sk36aGoC/" target="_blank" rel="noopener" style="word-break: break-all">https://www.alignmentforum.org/posts/t3AJW5jP3sk36aGoC/</a> capability-amplification-1, 2019. AI Alignment Forum.</div>
<div class="preview-paragraph-380 preview-line 380" data_line_start="380" data_line_end="380" data_line="380,381" count_line="1">Cui, G., Yuan, L., Wang, Z., Wang, H., Li, W., He, B., Fan, Y., Yu, T., Xu, Q., Chen, W., Yuan, J., Chen, H., Zhang, K., Lv, X., Wang, S., Yao, Y., Han, X., Peng, H., Cheng, Y., Liu, Z., Sun, M., Zhou, B., and Ding, N. Process reinforcement through implicit rewards. CoRR, abs/2502.01456, 2025. doi: 10.48550/ARXIV.2502.01456. URL <a href="https://doi.org/10.48550/arXiv.2502.01456" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2502.01456</a>.</div>
<div class="preview-paragraph-382 preview-line 382" data_line_start="382" data_line_end="382" data_line="382,383" count_line="1">DeepSeek-AI, Guo, D., Yang, D., Zhang, H., Song, J., Zhang, R., Xu, R., Zhu, Q., Ma, S., Wang, P., Bi, X., Zhang, X., Yu, X., Wu, Y., Wu, Z. F., Gou, Z., Shao, Z., Li, Z., Gao, Z., Liu, A., Xue, B., Wang, B., Wu, B., Feng, B., Lu, C., Zhao, C., Deng, C., Zhang, C., Ruan, C., Dai, D., Chen, D., Ji, D., Li, E., Lin, F., Dai, F., Luo, F., Hao, G., Chen, G., Li, G., Zhang, H., Bao, H., Xu, H., Wang, H., Ding, H., Xin, H., Gao, H., Qu, H., Li, H., Guo, J., Li, J., Wang, J., Chen, J., Yuan, J., Qiu, J., Li, J., Cai, J. L., Ni, J., Liang, J., Chen, J., Dong, K., Hu, K., Gao, K., Guan, K., Huang, K., Yu, K., Wang, L., Zhang, L., Zhao, L., Wang, L., Zhang, L., Xu, L., Xia, L., Zhang, M., Zhang, M., Tang, M., Li, M., Wang, M., Li, M., Tian, N., Huang, P., Zhang, P., Wang, Q., Chen, Q., Du, Q., Ge, R., Zhang, R., Pan, R., Wang, R., Chen, R. J., Jin, R. L., Chen, R., Lu, S., Zhou, S., Chen, S., Ye, S., Wang, S., Yu, S., Zhou, S., Pan, S., and Li, S. S. Deepseek-r1: Incentivizing reasoning capability in llms via reinforcement learning. CoRR, abs/2501.12948, 2025. doi: 10.48550/ARXIV.2501.12948. URL <a href="https://doi.org/10.48550/arXiv.2501.12948" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2501.12948</a>.</div>
<div class="preview-paragraph-384 preview-line 384 385" data_line_start="384" data_line_end="385" data_line="384,386" count_line="2">Demski, A. and Garrabrant, S. Embedded agency. CoRR, abs/1902.09469, 2019. URL <a href="http://arxiv" target="_blank" rel="noopener" style="display: inline-block">http://arxiv</a>. org/abs/1902. 09469.<br>
Dennis, M., Jaques, N., Vinitsky, E., Bayen, A. M., Russell, S., Critch, A., and Levine, S. Emergent complexity and zeroshot transfer via unsupervised environment design. In Larochelle, H., Ranzato, M., Hadsell, R., Balcan, M., and Lin, H. (eds.), Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems 2020, NeurIPS 2020, December 6-12, 2020, virtual, 2020. URL <a href="https://proceedings.neurips.cc/paper/2020/hash/" target="_blank" rel="noopener" style="word-break: break-all">https://proceedings.neurips.cc/paper/2020/hash/</a> 985e9a46e10005356bbaf194249f6856-Abstract.html.</div>
<div class="preview-paragraph-387 preview-line 387 388" data_line_start="387" data_line_end="388" data_line="387,389" count_line="2">Dubey, A., Jauhri, A., Pandey, A., Kadian, A., Al-Dahle, A., Letman, A., Mathur, A., Schelten, A., Yang, A., Fan, A., Goyal, A., Hartshorn, A., Yang, A., Mitra, A., Sravankumar, A., Korenev, A., Hinsvark, A., Rao, A., Zhang, A., Rodriguez, A., Gregerson, A., Spataru, A., Rozière, B., Biron, B., Tang, B., Chern, B., Caucheteux, C., Nayak, C., Bi, C., Marra, C., McConnell, C., Keller, C., Touret, C., Wu, C., Wong, C., Ferrer, C. C., Nikolaidis, C., Allonsius, D., Song, D., Pintz, D., Livshits, D., Esiobu, D., Choudhary, D., Mahajan, D., Garcia-Olano, D., Perino, D., Hupkes, D., Lakomkin, E., AlBadawy, E., Lobanova, E., Dinan, E., Smith, E. M., Radenovic, F., Zhang, F., Synnaeve, G., Lee, G., Anderson, G. L., Nail, G., Mialon, G., Pang, G., Cucurell, G., Nguyen, H., Korevaar, H., Xu, H., Touvron, H., Zarov, I., Ibarra, I. A., Kloumann, I. M., Misra, I., Evtimov, I., Copet, J., Lee, J., Geffert, J., Vranes, J., Park, J., Mahadeokar, J., Shah, J., van der Linde, J., Billock, J., Hong, J., Lee, J., Fu, J., Chi, J., Huang, J., Liu, J., Wang, J., Yu, J., Bitton, J., Spisak, J., Park, J., Rocca, J., Johnstun, J., Saxe, J., Jia, J., Alwala, K. V., Upasani, K., Plawiak, K., Li, K., Heafield,<br>
K., Stone, K., and et al. The llama 3 herd of models. CoRR, abs/2407.21783, 2024. doi: 10.48550/ARXIV.2407.21783. URL <a href="https://doi.org/10.48550/arXiv.2407.21783" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2407.21783</a>.</div>
<div class="preview-paragraph-390 preview-line 390 391" data_line_start="390" data_line_end="391" data_line="390,392" count_line="2">Ebert, C., Cain, J., Antoniol, G., Counsell, S., and Laplante, P. Cyclomatic complexity. IEEE software, 33(6):27-29, 2016.<br>
Florensa, C., Held, D., Geng, X., and Abbeel, P. Automatic goal generation for reinforcement learning agents. In Dy, J. G. and Krause, A. (eds.), Proceedings of the 35th International Conference on Machine Learning, ICML 2018, Stockholmsmässan, Stockholm, Sweden, July 10-15, 2018, volume 80 of Proceedings of Machine Learning Research, pp. 1514-1523. PMLR, 2018. URL <a href="http://proceedings.mlr.press/v80/florensa18a.html" target="_blank" rel="noopener" style="word-break: break-all">http://proceedings.mlr.press/v80/florensa18a.html</a>.</div>
<div class="preview-paragraph-393 preview-line 393" data_line_start="393" data_line_end="393" data_line="393,394" count_line="1">Goodfellow, I. J., Pouget-Abadie, J., Mirza, M., Xu, B., Warde-Farley, D., Ozair, S., Courville, A. C., and Bengio, Y. Generative adversarial networks. Commun. ACM, 63(11):139-144, 2020. doi: 10.1145/3422622. URL <a href="https://doi.org/10.1145/3422622" target="_blank" rel="noopener" style="display: inline-block">https://doi.org/10.1145/3422622</a>.</div>
<div class="preview-paragraph-395 preview-line 395" data_line_start="395" data_line_end="395" data_line="395,396" count_line="1">Gu, A., Rozière, B., Leather, H. J., Solar-Lezama, A., Synnaeve, G., and Wang, S. Cruxeval: A benchmark for code reasoning, understanding and execution. In Forty-first International Conference on Machine Learning, ICML 2024, Vienna, Austria, July 21-27, 2024. <a href="http://OpenReview.net" target="_blank" rel="noopener" style="display: inline-block">OpenReview.net</a>, 2024. URL <a href="https://openreview.net/forum?id=Ffpg52swvg" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=Ffpg52swvg</a>.</div>
<div class="preview-paragraph-397 preview-line 397 398" data_line_start="397" data_line_end="398" data_line="397,399" count_line="2">Halstead, M. H. Elements of Software Science (Operating and programming systems series). Elsevier Science Inc., 1977.<br>
He, C., Luo, R., Bai, Y., Hu, S., Thai, Z. L., Shen, J., Hu, J., Han, X., Huang, Y., Zhang, Y., Liu, J., Qi, L., Liu, Z., and Sun, M. Olympiadbench: A challenging benchmark for promoting AGI with olympiad-level bilingual multimodal scientific problems. In Ku, L., Martins, A., and Srikumar, V. (eds.), Proceedings of the 62nd Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), ACL 2024, Bangkok, Thailand, August 11-16, 2024, pp. 3828-3850. Association for Computational Linguistics, 2024. doi: 10.18653/V1/2024.ACL-LONG.211. URL <a href="https://doi.org/10.18653/v1/2024.acl-long" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.18653/v1/2024.acl-long</a>. 211.</div>
<div class="preview-paragraph-400 preview-line 400" data_line_start="400" data_line_end="400" data_line="400,401" count_line="1">Hendrycks, D., Burns, C., Kadavath, S., Arora, A., Basart, S., Tang, E., Song, D., and Steinhardt, J. Measuring mathematical problem solving with the MATH dataset. In Vanschoren, J. and Yeung, S. (eds.), Proceedings of the Neural Information Processing Systems Track on Datasets and Benchmarks 1, NeurIPS Datasets and Benchmarks 2021, December 2021, virtual, 2021. URL <a href="https://datasets-benchmarks-proceedings.neurips.cc/paper/2021/hash/" target="_blank" rel="noopener" style="word-break: break-all">https://datasets-benchmarks-proceedings.neurips.cc/paper/2021/hash/</a> be83ab3ecd0db773eb2dc1b0a17836a1-Abstract-round2.html.</div>
<div class="preview-paragraph-402 preview-line 402" data_line_start="402" data_line_end="402" data_line="402,403" count_line="1">Hinton, G. E., Vinyals, O., and Dean, J. Distilling the knowledge in a neural network. CoRR, abs/1503.02531, 2015. URL <a href="http://arxiv.org/abs/1503.02531" target="_blank" rel="noopener" style="display: inline-block">http://arxiv.org/abs/1503.02531</a>.</div>
<div class="preview-paragraph-404 preview-line 404" data_line_start="404" data_line_end="404" data_line="404,405" count_line="1">Hu, J. REINFORCE++: A simple and efficient approach for aligning large language models. CoRR, abs/2501.03262, 2025. doi: 10.48550/ARXIV.2501.03262. URL <a href="https://doi.org/10.48550/arXiv.2501.03262" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2501.03262</a>.</div>
<div class="preview-paragraph-406 preview-line 406" data_line_start="406" data_line_end="406" data_line="406,407" count_line="1">Hu, J., Zhang, Y., Han, Q., Jiang, D., Zhang, X., and Shum, H. Open-reasoner-zero: An open source approach to scaling up reinforcement learning on the base model. CoRR, abs/2503.24290, 2025. doi: 10.48550/ARXIV.2503.24290. URL https: <a href="//doi.org/10.48550/arXiv.2503.24290" target="_blank" rel="noopener" style="display: inline-block">//doi.org/10.48550/arXiv.2503.24290</a>.</div>
<div class="preview-paragraph-408 preview-line 408" data_line_start="408" data_line_end="408" data_line="408,409" count_line="1">Hubinger, E., van Merwijk, C., Mikulik, V., Skalse, J., and Garrabrant, S. Risks from learned optimization in advanced machine learning systems. CoRR, abs/1906.01820, 2019. URL <a href="http://arxiv.org/abs/1906.01820" target="_blank" rel="noopener" style="display: inline-block">http://arxiv.org/abs/1906.01820</a>.</div>
<div class="preview-paragraph-410 preview-line 410" data_line_start="410" data_line_end="410" data_line="410,411" count_line="1">Hughes, E., Dennis, M. D., Parker-Holder, J., Behbahani, F. M. P., Mavalankar, A., Shi, Y., Schaul, T., and Rocktäschel, T. Position: Open-endedness is essential for artificial superhuman intelligence. In Forty-first International Conference on Machine Learning, ICML 2024, Vienna, Austria, July 21-27, 2024. <a href="http://OpenReview.net" target="_blank" rel="noopener" style="display: inline-block">OpenReview.net</a>, 2024. URL <a href="https://openreview" target="_blank" rel="noopener" style="display: inline-block">https://openreview</a> . net/forum?id=Bc4vZ2CX7E.</div>
<div class="preview-paragraph-412 preview-line 412" data_line_start="412" data_line_end="412" data_line="412,413" count_line="1">Hui, B., Yang, J., Cui, Z., Yang, J., Liu, D., Zhang, L., Liu, T., Zhang, J., Yu, B., Dang, K., Yang, A., Men, R., Huang, F., Ren, X., Ren, X., Zhou, J., and Lin, J. Qwen2.5-coder technical report. CoRR, abs/2409.12186, 2024. doi: 10.48550/ARXIV.2409.12186. URL <a href="https://doi.org/10.48550/arXiv.2409.12186" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2409.12186</a>.</div>
<div class="preview-paragraph-414 preview-line 414" data_line_start="414" data_line_end="414" data_line="414,415" count_line="1">Jaech, A., Kalai, A., Lerer, A., Richardson, A., El-Kishky, A., Low, A., Helyar, A., Madry, A., Beutel, A., Carney, A., et al. Openai o1 system card. arXiv preprint arXiv:2412.16720, 2024.</div>
<div class="preview-paragraph-416 preview-line 416" data_line_start="416" data_line_end="416" data_line="416,417" count_line="1">Jain, N., Han, K., Gu, A., Li, W., Yan, F., Zhang, T., Wang, S., Solar-Lezama, A., Sen, K., and Stoica, I. Livecodebench: Holistic and contamination free evaluation of large language models for code. CoRR, abs/2403.07974, 2024. doi: 10.48550/ARXIV.2403.07974. URL <a href="https://doi.org/10.48550/arXiv.2403.07974" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2403.07974</a>.</div>
<div class="preview-paragraph-418 preview-line 418" data_line_start="418" data_line_end="418" data_line="418,419" count_line="1">Kirchner, J. H., Chen, Y., Edwards, H., Leike, J., McAleese, N., and Burda, Y. Prover-verifier games improve legibility of LLM outputs. CoRR, abs/2407.13692, 2024. doi: 10.48550/ARXIV.2407.13692. URL <a href="https://doi.org/10.48550/arXiv.2407.13692" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2407.13692</a>.</div>
<div class="preview-paragraph-420 preview-line 420" data_line_start="420" data_line_end="420" data_line="420,421" count_line="1">Ladosz, P., Weng, L., Kim, M., and Oh, H. Exploration in deep reinforcement learning: A survey. Inf. Fusion, 85:1-22, 2022. doi: 10.1016/J.INFFUS.2022.03.003. URL <a href="https://doi.org/10.1016/j.inffus.2022.03.003" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.1016/j.inffus.2022.03.003</a>.</div>
<div class="preview-paragraph-422 preview-line 422" data_line_start="422" data_line_end="422" data_line="422,423" count_line="1">Lambert, N., Morrison, J., Pyatkin, V., Huang, S., Ivison, H., Brahman, F., Miranda, L. J. V., Liu, A., Dziri, N., Lyu, S., Gu, Y., Malik, S., Graf, V., Hwang, J. D., Yang, J., Bras, R. L., Tafjord, O., Wilhelm, C., Soldaini, L., Smith, N. A., Wang, Y., Dasigi, P., and Hajishirzi, H. Tülu 3: Pushing frontiers in open language model post-training. CoRR, abs/2411.15124, 2024. doi: 10.48550/ARXIV.2411.15124. URL <a href="https://doi.org/10.48550/arXiv.2411.15124" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2411.15124</a>.</div>
<div class="preview-paragraph-424 preview-line 424" data_line_start="424" data_line_end="424" data_line="424,425" count_line="1">Laskin, M., Yarats, D., Liu, H., Lee, K., Zhan, A., Lu, K., Cang, C., Pinto, L., and Abbeel, P. URLB: unsupervised reinforcement learning benchmark. In Vanschoren, J. and Yeung, S. (eds.), Proceedings of the Neural Information Processing Systems Track on Datasets and Benchmarks 1, NeurIPS Datasets and Benchmarks 2021, December 2021, virtual, 2021. URL <a href="https://datasets-benchmarks-proceedings.neurips.cc/paper/2021/hash/" target="_blank" rel="noopener" style="word-break: break-all">https://datasets-benchmarks-proceedings.neurips.cc/paper/2021/hash/</a> 091d584fced301b442654dd8c23b3fc9-Abstract-round2.html.</div>
<div class="preview-paragraph-426 preview-line 426" data_line_start="426" data_line_end="426" data_line="426,427" count_line="1">Leike, J. and Sutskever, I. Introducing superalignment. <a href="https://openai.com/index/introducing-superalignment/" target="_blank" rel="noopener" style="word-break: break-all">https://openai.com/index/introducing-superalignment/</a>, 2023. OpenAI Blog.</div>
<div class="preview-paragraph-428 preview-line 428" data_line_start="428" data_line_end="428" data_line="428,429" count_line="1">Li, J., Guo, D., Yang, D., Xu, R., Wu, Y., and He, J. Codei/o: Condensing reasoning patterns via code input-output prediction. CoRR, abs/2502.07316, 2025. doi: 10.48550/ARXIV.2502.07316. URL <a href="https://doi.org/10.48550/arXiv.2502.07316" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2502.07316</a>.</div>
<div class="preview-paragraph-430 preview-line 430" data_line_start="430" data_line_end="430" data_line="430,431" count_line="1">Li, R., Fu, J., Zhang, B., Huang, T., Sun, Z., Lyu, C., Liu, G., Jin, Z., and Li, G. TACO: topics in algorithmic code generation dataset. CoRR, abs/2312.14852, 2023. doi: 10.48550/ARXIV.2312.14852. URL <a href="https://doi.org/10.48550/arXiv.2312.14852" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2312.14852</a>.</div>
<div class="preview-paragraph-432 preview-line 432 433" data_line_start="432" data_line_end="433" data_line="432,434" count_line="2">Liu, J. and Zhang, L. Code-r1: Reproducing r1 for code with reliable rewards. GitHub, 2025.<br>
Liu, J., Xia, C. S., Wang, Y., and Zhang, L. Is your code generated by chatGPT really correct? rigorous evaluation of large language models for code generation. In Thirty-seventh Conference on Neural Information Processing Systems, 2023. URL <a href="https://openreview.net/forum?id=1qvx610Cu7" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=1qvx610Cu7</a>.</div>
<div class="preview-paragraph-435 preview-line 435" data_line_start="435" data_line_end="435" data_line="435,436" count_line="1">Liu, Z., Chen, C., Li, W., Qi, P., Pang, T., Du, C., Lee, W. S., and Lin, M. Understanding r1-zero-like training: A critical perspective. CoRR, abs/2503.20783, 2025. doi: 10.48550/ARXIV.2503.20783. URL <a href="https://doi.org/10.48550/arXiv.2503.20783" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2503.20783</a>.</div>
<div class="preview-paragraph-437 preview-line 437" data_line_start="437" data_line_end="437" data_line="437,438" count_line="1">Lopez, R. H. Q. Complexipy: An extremely fast python library to calculate the cognitive complexity of python files, written in rust, 2025. URL <a href="https://github.com/rohaquinlop/complexipy" target="_blank" rel="noopener" style="word-break: break-all">https://github.com/rohaquinlop/complexipy</a>. Accessed: 2025-04-06.</div>
<div class="preview-paragraph-439 preview-line 439" data_line_start="439" data_line_end="439" data_line="439,440" count_line="1">Loshchilov, I. and Hutter, F. Decoupled weight decay regularization. In 7th International Conference on Learning Representations, ICLR 2019, New Orleans, LA, USA, May 6-9, 2019. <a href="http://OpenReview.net" target="_blank" rel="noopener" style="display: inline-block">OpenReview.net</a>, 2019. URL <a href="https://openreview.net/forum?id=Bkg6RiCqY7" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=Bkg6RiCqY7</a>.</div>
<div class="preview-paragraph-441 preview-line 441" data_line_start="441" data_line_end="441" data_line="441,442" count_line="1">Morris, J. There are no new ideas in ai. . . only new datasets. <a href="https://blog" target="_blank" rel="noopener" style="display: inline-block">https://blog</a>. <a href="http://jxmo.io/p/there-are-no-new-ideas-in-ai-only" target="_blank" rel="noopener" style="word-break: break-all">jxmo.io/p/there-are-no-new-ideas-in-ai-only</a>, 2025.</div>
<div class="preview-paragraph-443 preview-line 443 444" data_line_start="443" data_line_end="444" data_line="443,445" count_line="2">OpenAI. Openai o3-mini, January 2025a. URL <a href="https://openai" target="_blank" rel="noopener" style="display: inline-block">https://openai</a> . com/index/openai-o3-mini/. Accessed: 2025-04-17.<br>
OpenAI. Introducing openai o3 and o4-mini, April 2025b. URL <a href="https://openai.com/index/introducing-o3-and-o4-mini/" target="_blank" rel="noopener" style="word-break: break-all">https://openai.com/index/introducing-o3-and-o4-mini/</a>. Accessed: 2025-04-17.</div>
<div class="preview-paragraph-446 preview-line 446" data_line_start="446" data_line_end="446" data_line="446,447" count_line="1">OpenAI, Plappert, M., Sampedro, R., Xu, T., Akkaya, I., Kosaraju, V., Welinder, P., D’Sa, R., Petron, A., de Oliveira Pinto, H. P., Paino, A., Noh, H., Weng, L., Yuan, Q., Chu, C., and Zaremba, W. Asymmetric self-play for automatic goal discovery in robotic manipulation. CoRR, abs/2101.04882, 2021. URL <a href="https://arxiv.org/abs/2101.04882" target="_blank" rel="noopener" style="display: inline-block">https://arxiv.org/abs/2101.04882</a>.</div>
<div class="preview-paragraph-448 preview-line 448" data_line_start="448" data_line_end="448" data_line="448,449" count_line="1">Ouyang, L., Wu, J., Jiang, X., Almeida, D., Wainwright, C., Mishkin, P., Zhang, C., Agarwal, S., Slama, K., Ray, A., et al. Training language models to follow instructions with human feedback. Advances in neural information processing systems, 35:27730-27744, 2022.</div>
<div class="preview-paragraph-450 preview-line 450" data_line_start="450" data_line_end="450" data_line="450,451" count_line="1">Poesia, G., Broman, D., Haber, N., and Goodman, N. D. Learning formal mathematics from intrinsic motivation. In Globersons, A., Mackey, L., Belgrave, D., Fan, A., Paquet, U., Tomczak, J. M., and Zhang, C. (eds.), Advances in Neural Information Processing Systems 38: Annual Conference on Neural Information Processing Systems 2024, NeurIPS 2024, Vancouver, BC, Canada, December 10-15, 2024, 2024. URL <a href="http://papers.nips.cc/paper_files/paper/2024/hash/" target="_blank" rel="noopener" style="word-break: break-all">http://papers.nips.cc/paper_files/paper/2024/hash/</a> 4b8001fc75f0532827472ea5a16af9ca-Abstract-Conference.html.</div>
<div class="preview-paragraph-452 preview-line 452" data_line_start="452" data_line_end="452" data_line="452,453" count_line="1">Radford, A., Wu, J., Child, R., Luan, D., Amodei, D., Sutskever, I., et al. Language models are unsupervised multitask learners. OpenAI blog, 1(8):9, 2019.</div>
<div class="preview-paragraph-454 preview-line 454" data_line_start="454" data_line_end="454" data_line="454,455" count_line="1">Ren, Z. Z., Shao, Z., Song, J., Xin, H., Wang, H., Zhao, W., Zhang, L., Fu, Z., Zhu, Q., Yang, D., Wu, Z. F., Gou, Z., Ma, S., Tang, H., Liu, Y., Gao, W., Guo, D., and Ruan, C. Deepseek-prover-v2: Advancing formal mathematical reasoning via reinforcement learning for subgoal decomposition, 2025. URL <a href="https://arxiv.org/abs/2504.21801" target="_blank" rel="noopener" style="display: inline-block">https://arxiv.org/abs/2504.21801</a>.</div>
<div class="preview-paragraph-456 preview-line 456" data_line_start="456" data_line_end="456" data_line="456,457" count_line="1">Schaul, T. Boundless socratic learning with language games. arXiv preprint arXiv:2411.16905, 2024.</div>
<div class="preview-paragraph-458 preview-line 458" data_line_start="458" data_line_end="458" data_line="458,459" count_line="1">Schmidhuber, J. Exploring the predictable. In Advances in evolutionary computing: theory and applications, pp. 579-612. Springer, 2003.</div>
<div class="preview-paragraph-460 preview-line 460" data_line_start="460" data_line_end="460" data_line="460,461" count_line="1">Schmidhuber, J. POWERPLAY: training an increasingly general problem solver by continually searching for the simplest still unsolvable problem. CoRR, abs/1112.5309, 2011. URL <a href="http://arxiv.org/abs/1112.5309" target="_blank" rel="noopener" style="display: inline-block">http://arxiv.org/abs/1112.5309</a>.</div>
<div class="preview-paragraph-462 preview-line 462" data_line_start="462" data_line_end="462" data_line="462,463" count_line="1">Shao, Z., Wang, P., Zhu, Q., Xu, R., Song, J., Zhang, M., Li, Y. K., Wu, Y., and Guo, D. Deepseekmath: Pushing the limits of mathematical reasoning in open language models. CoRR, abs/2402.03300, 2024. doi: 10.48550/ARXIV.2402.03300. URL <a href="https://doi.org/10.48550/arXiv.2402.03300" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2402.03300</a>.</div>
<div class="preview-paragraph-464 preview-line 464" data_line_start="464" data_line_end="464" data_line="464,465" count_line="1">Sheng, G., Zhang, C., Ye, Z., Wu, X., Zhang, W., Zhang, R., Peng, Y., Lin, H., and Wu, C. Hybridflow: A flexible and efficient RLHF framework. In Proceedings of the Twentieth European Conference on Computer Systems, EuroSys 2025, Rotterdam, The Netherlands, 30 March 2025-3 April 2025, pp. 1279-1297. ACM, 2025. doi: 10.1145/3689031.3696075. URL https: <a href="//doi.org/10.1145/3689031.3696075" target="_blank" rel="noopener" style="display: inline-block">//doi.org/10.1145/3689031.3696075</a>.</div>
<div class="preview-paragraph-466 preview-line 466" data_line_start="466" data_line_end="466" data_line="466,467" count_line="1">Silver, D. and Sutton, R. S. The era of experience. <a href="https://storage.googleapis.com/deepmind-media/Era-of-Experience%25" target="_blank" rel="noopener" style="word-break: break-all">https://storage.googleapis.com/deepmind-media/Era-of-Experience%</a> 20/The%20Era%20of%20Experience%20Paper.pdf, 2025.</div>
<div class="preview-paragraph-468 preview-line 468" data_line_start="468" data_line_end="468" data_line="468,469" count_line="1">Silver, D., Huang, A., Maddison, C. J., Guez, A., Sifre, L., van den Driessche, G., Schrittwieser, J., Antonoglou, I., Panneershelvam, V., Lanctot, M., Dieleman, S., Grewe, D., Nham, J., Kalchbrenner, N., Sutskever, I., Lillicrap, T. P., Leach, M., Kavukcuoglu, K., Graepel, T., and Hassabis, D. Mastering the game of go with deep neural networks and tree search. Nat., 529(7587):484-489, 2016. doi: 10.1038/NATURE16961. URL <a href="https://doi.org/10.1038/nature16961" target="_blank" rel="noopener" style="display: inline-block">https://doi.org/10.1038/nature16961</a>.</div>
<div class="preview-paragraph-470 preview-line 470" data_line_start="470" data_line_end="470" data_line="470,471" count_line="1">Silver, D., Hubert, T., Schrittwieser, J., Antonoglou, I., Lai, M., Guez, A., Lanctot, M., Sifre, L., Kumaran, D., Graepel, T., Lillicrap, T. P., Simonyan, K., and Hassabis, D. Mastering chess and shogi by self-play with a general reinforcement learning algorithm. CoRR, abs/1712.01815, 2017. URL <a href="http://arxiv.org/abs/1712.01815" target="_blank" rel="noopener" style="display: inline-block">http://arxiv.org/abs/1712.01815</a>.</div>
<div class="preview-paragraph-472 preview-line 472" data_line_start="472" data_line_end="472" data_line="472,473" count_line="1">Stuart, T. Understanding computation - from simple machines to impossible programs. O'Reilly, 2015. ISBN 978-1-449-32927-3. URL <a href="http://www.oreilly.de/catalog/9781449329273/index.html" target="_blank" rel="noopener" style="word-break: break-all">http://www.oreilly.de/catalog/9781449329273/index.html</a>.</div>
<div class="preview-paragraph-474 preview-line 474" data_line_start="474" data_line_end="474" data_line="474,475" count_line="1">Sukhbaatar, S., Lin, Z., Kostrikov, I., Synnaeve, G., Szlam, A., and Fergus, R. Intrinsic motivation and automatic curricula via asymmetric self-play. In 6th International Conference on Learning Representations, ICLR 2018, Vancouver, BC, Canada, April 30 May 3, 2018, Conference Track Proceedings. <a href="http://OpenReview.net" target="_blank" rel="noopener" style="display: inline-block">OpenReview.net</a>, 2018. URL <a href="https://openreview.net/forum?id=SkT5Yg-RZ" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=SkT5Yg-RZ</a>.</div>
<div class="preview-paragraph-476 preview-line 476" data_line_start="476" data_line_end="476" data_line="476,477" count_line="1">Suteu, M. and Guo, Y. Regularizing deep multi-task networks using orthogonal gradients. CoRR, abs/1912.06844, 2019. URL <a href="http://arxiv.org/abs/1912.06844" target="_blank" rel="noopener" style="display: inline-block">http://arxiv.org/abs/1912.06844</a>.</div>
<div class="preview-paragraph-478 preview-line 478" data_line_start="478" data_line_end="478" data_line="478,479" count_line="1">Sutskever, I., Vinyals, O., and Le, Q. V. Neurips 2024 test of time award session: Sequence to sequence learning with neural networks. Conference session, December 2024. URL <a href="https://neurips.cc/virtual/2024/test-of-time/105032" target="_blank" rel="noopener" style="word-break: break-all">https://neurips.cc/virtual/2024/test-of-time/105032</a>.</div>
<div class="preview-paragraph-480 preview-line 480 481" data_line_start="480" data_line_end="481" data_line="480,482" count_line="2">Sutton, R. S. Verification, the key to ai. <a href="http://incompleteideas.net/IncIdeas/KeytoAI.html" target="_blank" rel="noopener" style="word-break: break-all">http://incompleteideas.net/IncIdeas/KeytoAI.html</a>, 2001.<br>
Team, K., Du, A., Gao, B., Xing, B., Jiang, C., Chen, C., Li, C., Xiao, C., Du, C., Liao, C., Tang, C., Wang, C., Zhang, D., Yuan, E., Lu, E., Tang, F., Sung, F., Wei, G., Lai, G., Guo, H., Zhu, H., Ding, H., Hu, H., Yang, H., Zhang, H., Yao, H., Zhao, H., Lu, H., Li, H., Yu, H., Gao, H., Zheng, H., Yuan, H., Chen, J., Guo, J., Su, J., Wang, J., Zhao, J., Zhang, J., Liu, J., Yan, J., Wu, J., Shi, L., Ye, L., Yu, L., Dong, M., Zhang, N., Ma, N., Pan, Q., Gong, Q., Liu, S., Ma, S., Wei, S., Cao, S., Huang, S., Jiang, T., Gao, W., Xiong, W., He, W., Huang, W., Wu, W., He, W., Wei, X., Jia, X., Wu, X., Xu, X., Zu, X., Zhou, X., Pan, X., Charles, Y., Li, Y., Hu, Y., Liu, Y., Chen, Y., Wang, Y., Liu, Y., Qin, Y., Liu, Y., Yang, Y., Bao, Y., Du, Y., Wu, Y., Wang, Y., Zhou, Z., Wang, Z., Li, Z., Zhu, Z., Zhang, Z., Wang, Z., Yang, Z., Huang, Z., Huang, Z., Xu, Z., and Yang, Z. Kimi k1.5: Scaling reinforcement learning with llms. CoRR, abs/2501.12599, 2025. doi: 10.48550/ARXIV.2501.12599. URL <a href="https://doi.org/10.48550/arXiv.2501.12599" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2501.12599</a>.</div>
<div class="preview-paragraph-483 preview-line 483" data_line_start="483" data_line_end="483" data_line="483,484" count_line="1">Villalobos, P., Ho, A., Sevilla, J., Besiroglu, T., Heim, L., and Hobbhahn, M. Position: Will we run out of data? limits of LLM scaling based on human-generated data. In Forty-first International Conference on Machine Learning, ICML 2024, Vienna, Austria, July 21-27, 2024. <a href="http://OpenReview.net" target="_blank" rel="noopener" style="display: inline-block">OpenReview.net</a>, 2024. URL <a href="https://openreview.net/forum?id=ViZcgDQjyG" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=ViZcgDQjyG</a>.</div>
<div class="preview-paragraph-485 preview-line 485" data_line_start="485" data_line_end="485" data_line="485,486" count_line="1">Wang, H., Yue, Y., Lu, R., Shi, J., Zhao, A., Wang, S., Song, S., and Huang, G. Model surgery: Modulating LLM's behavior via simple parameter editing. In Proceedings of the 2025 Conference of the Nations of the Americas Chapter of the Association for Computational Linguistics, pp. 6337-6357, 2025a.</div>
<div class="preview-paragraph-487 preview-line 487" data_line_start="487" data_line_end="487" data_line="487,488" count_line="1">Wang, R., Lehman, J., Clune, J., and Stanley, K. O. Paired open-ended trailblazer (POET): endlessly generating increasingly complex and diverse learning environments and their solutions. CoRR, abs/1901.01753, 2019. URL <a href="http://arxiv.org/abs/1901" target="_blank" rel="noopener" style="display: inline-block">http://arxiv.org/abs/1901</a>. 01753.</div>
<div class="preview-paragraph-489 preview-line 489" data_line_start="489" data_line_end="489" data_line="489,490" count_line="1">Wang, S., Yang, Q., Gao, J., Lin, M. G., Chen, H., Wu, L., Jia, N., Song, S., and Huang, G. Train once, get a family: State-adaptive balances for offline-to-online reinforcement learning. In Thirty-seventh Conference on Neural Information Processing Systems, 2023. URL <a href="https://openreview.net/forum?id=vtoY8qJjTR" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=vtoY8qJjTR</a>.</div>
<div class="preview-paragraph-491 preview-line 491" data_line_start="491" data_line_end="491" data_line="491,492" count_line="1">Wang, S., Liu, C., Zheng, Z., Qi, S., Chen, S., Yang, Q., Zhao, A., Wang, C., Song, S., and Huang, G. Boosting LLM agents with recursive contemplation for effective deception handling. In Ku, L.-W., Martins, A., and Srikumar, V. (eds.), Findings of the Association for Computational Linguistics: ACL 2024, pp. 9909-9953, Bangkok, Thailand, August 2024. Association for Computational Linguistics. doi: 10.18653/v1/2024.findings-acl.591. URL <a href="https://aclanthology.org/2024.findings-acl.591/" target="_blank" rel="noopener" style="word-break: break-all">https://aclanthology.org/2024.findings-acl.591/</a>.</div>
<div class="preview-paragraph-493 preview-line 493" data_line_start="493" data_line_end="493" data_line="493,494" count_line="1">Wang, Y., Yang, Q., Zeng, Z., Ren, L., Liu, L., Peng, B., Cheng, H., He, X., Wang, K., Gao, J., Chen, W., Wang, S., Du, S. S., and Shen, Y. Reinforcement learning for reasoning in large language models with one training example, 2025b. URL <a href="https://arxiv.org/abs/2504.20571" target="_blank" rel="noopener" style="display: inline-block">https://arxiv.org/abs/2504.20571</a>.</div>
<div class="preview-paragraph-495 preview-line 495" data_line_start="495" data_line_end="495" data_line="495,496" count_line="1">Wu, Q., Bansal, G., Zhang, J., Wu, Y., Zhang, S., Zhu, E., Li, B., Jiang, L., Zhang, X., and Wang, C. Autogen: Enabling next-gen LLM applications via multi-agent conversation framework. CoRR, abs/2308.08155, 2023. doi: 10.48550/ARXIV.2308.08155. URL <a href="https://doi.org/10.48550/arXiv.2308.08155" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2308.08155</a>.</div>
<div class="preview-paragraph-497 preview-line 497" data_line_start="497" data_line_end="497" data_line="497,498" count_line="1">Wu, Y., Yue, T., Zhang, S., Wang, C., and Wu, Q. Stateflow: Enhancing LLM task-solving through state-driven workflows. CoRR, abs/2403.11322, 2024. doi: 10.48550/ARXIV.2403.11322. URL <a href="https://doi.org/10.48550/arXiv.2403.11322" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2403.11322</a>.</div>
<div class="preview-paragraph-499 preview-line 499" data_line_start="499" data_line_end="499" data_line="499,500" count_line="1">Xie, T., Gao, Z., Ren, Q., Luo, H., Hong, Y., Dai, B., Zhou, J., Qiu, K., Wu, Z., and Luo, C. Logic-rl: Unleashing LLM reasoning with rule-based reinforcement learning. CoRR, abs/2502.14768, 2025. doi: 10.48550/ARXIV.2502.14768. URL <a href="https://doi.org/10.48550/arXiv.2502.14768" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2502.14768</a>.</div>
<div class="preview-paragraph-501 preview-line 501" data_line_start="501" data_line_end="501" data_line="501,502" count_line="1">Xu, F., Yan, H., Ma, C., Zhao, H., Sun, Q., Cheng, K., He, J., Liu, J., and Wu, Z. Genius: A generalizable and purely unsupervised self-training framework for advanced reasoning, 2025. URL <a href="https://arxiv" target="_blank" rel="noopener" style="display: inline-block">https://arxiv</a>. org/abs/2504. 08672.</div>
<div class="preview-paragraph-503 preview-line 503" data_line_start="503" data_line_end="503" data_line="503,504" count_line="1">Yang, A., Yang, B., Zhang, B., Hui, B., Zheng, B., Yu, B., Li, C., Liu, D., Huang, F., Wei, H., Lin, H., Yang, J., Tu, J., Zhang, J., Yang, J., Yang, J., Zhou, J., Lin, J., Dang, K., Lu, K., Bao, K., Yang, K., Yu, L., Li, M., Xue, M., Zhang, P., Zhu, Q., Men, R., Lin, R., Li, T., Xia, T., Ren, X., Ren, X., Fan, Y., Su, Y., Zhang, Y., Wan, Y., Liu, Y., Cui, Z., Zhang, Z., and Qiu, Z. Qwen2.5 technical report. CoRR, abs/2412.15115, 2024a. doi: 10.48550/ARXIV.2412.15115. URL <a href="https://doi.org/10.48550/arXiv.2412.15115" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2412.15115</a>.</div>
<div class="preview-paragraph-505 preview-line 505" data_line_start="505" data_line_end="505" data_line="505,506" count_line="1">Yang, A., Zhang, B., Hui, B., Gao, B., Yu, B., Li, C., Liu, D., Tu, J., Zhou, J., Lin, J., Lu, K., Xue, M., Lin, R., Liu, T., Ren, X., and Zhang, Z. Qwen2.5-math technical report: Toward mathematical expert model via self-improvement. CoRR, abs/2409.12122, 2024b. doi: 10.48550/ARXIV.2409.12122. URL <a href="https://doi.org/10.48550/arXiv.2409.12122" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2409.12122</a>.</div>
<div class="preview-paragraph-507 preview-line 507" data_line_start="507" data_line_end="507" data_line="507,508" count_line="1">Yao, S., Zhao, J., Yu, D., Du, N., Shafran, I., Narasimhan, K. R., and Cao, Y. React: Synergizing reasoning and acting in language models. In The Eleventh International Conference on Learning Representations, ICLR 2023, Kigali, Rwanda, May 1-5, 2023. <a href="http://OpenReview.net" target="_blank" rel="noopener" style="display: inline-block">OpenReview.net</a>, 2023. URL <a href="https://openreview.net/forum?id=WE_vluYUL-X" target="_blank" rel="noopener" style="word-break: break-all">https://openreview.net/forum?id=WE_vluYUL-X</a>.</div>
<div class="preview-paragraph-509 preview-line 509" data_line_start="509" data_line_end="509" data_line="509,510" count_line="1">Ye, Z., Agarwal, R., Liu, T., Joshi, R., Velury, S., Le, Q. V., Tan, Q., and Liu, Y. Evolving alignment via asymmetric self-play. CoRR, abs/2411.00062, 2024. doi: 10.48550/ARXIV.2411.00062. URL <a href="https://doi.org/10.48550/arXiv.2411.00062" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2411.00062</a>.</div>
<div class="preview-paragraph-511 preview-line 511" data_line_start="511" data_line_end="511" data_line="511,512" count_line="1">Yu, Q., Zhang, Z., Zhu, R., Yuan, Y., Zuo, X., Yue, Y., Fan, T., Liu, G., Liu, L., Liu, X., Lin, H., Lin, Z., Ma, B., Sheng, G., Tong, Y., Zhang, C., Zhang, M., Zhang, W., Zhu, H., Zhu, J., Chen, J., Chen, J., Wang, C., Yu, H., Dai, W., Song, Y., Wei, X., Zhou, H., Liu, J., Ma, W., Zhang, Y., Yan, L., Qiao, M., Wu, Y., and Wang, M. DAPO: an open-source LLM reinforcement learning system at scale. CoRR, abs/2503.14476, 2025. doi: 10.48550/ARXIV.2503.14476. URL <a href="https://doi.org/10.48550/arXiv.2503.14476" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2503.14476</a>.</div>
<div class="preview-paragraph-513 preview-line 513" data_line_start="513" data_line_end="513" data_line="513,514" count_line="1">Yuan, W., Pang, R. Y., Cho, K., Li, X., Sukhbaatar, S., Xu, J., and Weston, J. Self-rewarding language models. URL <a href="https://arxiv" target="_blank" rel="noopener" style="display: inline-block">https://arxiv</a>. org/abs/2401.10020, 2024.</div>
<div class="preview-paragraph-515 preview-line 515" data_line_start="515" data_line_end="515" data_line="515,516" count_line="1">Yuan, Y., Yu, Q., Zuo, X., Zhu, R., Xu, W., Chen, J., Wang, C., Fan, T., Du, Z., Wei, X., et al. Vapo: Efficient and reliable reinforcement learning for advanced reasoning tasks. arXiv preprint arXiv:2504.05118, 2025.</div>
<div class="preview-paragraph-517 preview-line 517" data_line_start="517" data_line_end="517" data_line="517,518" count_line="1">Yue, Y., Lu, R., Kang, B., Song, S., and Huang, G. Understanding, predicting and better resolving q-value divergence in offline-rl. Advances in Neural Information Processing Systems, 36:60247-60277, 2023.</div>
<div class="preview-paragraph-519 preview-line 519" data_line_start="519" data_line_end="519" data_line="519,520" count_line="1">Yue, Y., Wang, Y., Kang, B., Han, Y., Wang, S., Song, S., Feng, J., and Huang, G. Deer-vla: Dynamic inference of multimodal large language models for efficient robot execution. In Globersons, A., Mackey, L., Belgrave, D., Fan, A., Paquet, U., Tomczak, J. M., and Zhang, C. (eds.), Advances in Neural Information Processing Systems 38: Annual Conference on Neural Information Processing Systems 2024, NeurIPS 2024, Vancouver, BC, Canada, December 10-15, 2024, 2024. URL <a href="http://papers.nips" target="_blank" rel="noopener" style="display: inline-block">http://papers.nips</a>. cc/paper_ files/paper/2024/hash/67b0e7c7c2a5780aeefe3b79caac106e-Abstract-Conference.html.</div>
<div class="preview-paragraph-521 preview-line 521" data_line_start="521" data_line_end="521" data_line="521,522" count_line="1">Yue, Y., Chen, Z., Lu, R., Zhao, A., Wang, Z., Yue, Y., Song, S., and Huang, G. Does reinforcement learning really incentivize reasoning capacity in llms beyond the base model?, 2025. URL <a href="https://arxiv" target="_blank" rel="noopener" style="display: inline-block">https://arxiv</a>. org/abs/2504. 13837.</div>
<div class="preview-paragraph-523 preview-line 523" data_line_start="523" data_line_end="523" data_line="523,524" count_line="1">Zelikman, E., Wu, Y., Mu, J., and Goodman, N. Star: Bootstrapping reasoning with reasoning. Advances in Neural Information Processing Systems, 35:15476-15488, 2022.</div>
<div class="preview-paragraph-525 preview-line 525 526" data_line_start="525" data_line_end="526" data_line="525,527" count_line="2">Absolute Zero: Reinforced Self-play Reasoning with Zero Data<br>
Zeng, H., Jiang, D., Wang, H., Nie, P., Chen, X., and Chen, W. ACECODER: acing coder RL via automated test-case synthesis. CoRR, abs/2502.01718, 2025a. doi: 10.48550/ARXIV.2502.01718. URL <a href="https://doi.org/10.48550/arXiv.2502.01718" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.48550/arXiv.2502.01718</a>.</div>
<div class="preview-paragraph-528 preview-line 528" data_line_start="528" data_line_end="528" data_line="528,529" count_line="1">Zeng, W., Huang, Y., Liu, Q., Liu, W., He, K., Ma, Z., and He, J. Simplerl-zoo: Investigating and taming zero reinforcement learning for open base models in the wild. CoRR, abs/2503.18892, 2025b. doi: 10.48550/ARXIV.2503.18892. URL https: <a href="//doi.org/10.48550/arXiv.2503.18892" target="_blank" rel="noopener" style="display: inline-block">//doi.org/10.48550/arXiv.2503.18892</a>.</div>
<div class="preview-paragraph-530 preview-line 530" data_line_start="530" data_line_end="530" data_line="530,531" count_line="1">Zhang, C., Deng, Y., Lin, X., Wang, B., Ng, D., Ye, H., Li, X., Xiao, Y., Mo, Z., Zhang, Q., et al. 100 days after deepseek-r1: A survey on replication studies and more directions for reasoning language models. arXiv preprint arXiv:2505.00551, 2025a.</div>
<div class="preview-paragraph-532 preview-line 532" data_line_start="532" data_line_end="532" data_line="532,533" count_line="1">Zhang, Q., Wu, H., Zhang, C., Zhao, P., and Bian, Y. Right question is already half the answer: Fully unsupervised llm reasoning incentivization, 2025b. URL <a href="https://arxiv.org/abs/2504" target="_blank" rel="noopener" style="display: inline-block">https://arxiv.org/abs/2504</a>. 05812.</div>
<div class="preview-paragraph-534 preview-line 534" data_line_start="534" data_line_end="534" data_line="534,535" count_line="1">Zhang, Y. and Yang, Q. A survey on multi-task learning. IEEE transactions on knowledge and data engineering, 34(12):5586-5609, 2021.</div>
<div class="preview-paragraph-536 preview-line 536" data_line_start="536" data_line_end="536" data_line="536,537" count_line="1">Zhao, A., Lin, M. G., Li, Y., Liu, Y., and Huang, G. A mixture of surprises for unsupervised reinforcement learning. In Koyejo, S., Mohamed, S., Agarwal, A., Belgrave, D., Cho, K., and Oh, A. (eds.), Advances in Neural Information Processing Systems 35: Annual Conference on Neural Information Processing Systems 2022, NeurIPS 2022, New Orleans, LA, USA, November 28 - December 9, 2022, 2022. URL <a href="http://papers.nips.cc/paper_files/paper/2022/hash/" target="_blank" rel="noopener" style="word-break: break-all">http://papers.nips.cc/paper_files/paper/2022/hash/</a> a7667ee5d545a43d2f0fda98863c260e-Abstract-Conference.html.</div>
<div class="preview-paragraph-538 preview-line 538" data_line_start="538" data_line_end="538" data_line="538,539" count_line="1">Zhao, A., Huang, D., Xu, Q., Lin, M., Liu, Y., and Huang, G. Expel: LLM agents are experiential learners. In Wooldridge, M. J., Dy, J. G., and Natarajan, S. (eds.), Thirty-Eighth AAAI Conference on Artificial Intelligence, AAAI 2024, Thirty-Sixth Conference on Innovative Applications of Artificial Intelligence, IAAI 2024, Fourteenth Symposium on Educational Advances in Artificial Intelligence, EAAI 2014, February 20-27, 2024, Vancouver, Canada, pp. 19632-19642. AAAI Press, 2024. doi: 10.1609/AAAI.V38I17.29936. URL <a href="https://doi.org/10.1609/aaai.v38i17.29936" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.1609/aaai.v38i17.29936</a>.</div>
<div class="preview-paragraph-540 preview-line 540" data_line_start="540" data_line_end="540" data_line="540,541" count_line="1">Zhao, A., Xu, Q., Lin, M., Wang, S., Liu, Y., Zheng, Z., and Huang, G. Diver-ct: Diversity-enhanced red teaming large language model assistants with relaxing constraints. In Walsh, T., Shah, J., and Kolter, Z. (eds.), AAAI-25, Sponsored by the Association for the Advancement of Artificial Intelligence, February 25 - March 4, 2025, Philadelphia, PA, USA, pp. 26021-26030. AAAI Press, 2025a. doi: 10.1609/AAAI.V39I24.34797. URL <a href="https://doi.org/10.1609/aaai.v39i24.34797" target="_blank" rel="noopener" style="word-break: break-all">https://doi.org/10.1609/aaai.v39i24.34797</a>.</div>
<div class="preview-paragraph-542 preview-line 542" data_line_start="542" data_line_end="542" data_line="542,543" count_line="1">Zhao, A., Zhu, E., Lu, R., Lin, M., Liu, Y., and Huang, G. Self-referencing agents for unsupervised reinforcement learning. Neural Networks, 188:107448, 2025b. doi: 10.1016/J.NEUNET.2025.107448. URL <a href="https://doi.org/10.1016/j.neunet" target="_blank" rel="noopener" style="display: inline-block">https://doi.org/10.1016/j.neunet</a>. 2025. 107448.</div>
<div class="preview-paragraph-544 preview-line 544" data_line_start="544" data_line_end="544" data_line="544,545" count_line="1">Zitkovich, B., Yu, T., Xu, S., Xu, P., Xiao, T., Xia, F., Wu, J., Wohlhart, P., Welker, S., Wahid, A., Vuong, Q., Vanhoucke, V., Tran, H. T., Soricut, R., Singh, A., Singh, J., Sermanet, P., Sanketi, P. R., Salazar, G., Ryoo, M. S., Reymann, K., Rao, K., Pertsch, K., Mordatch, I., Michalewski, H., Lu, Y., Levine, S., Lee, L., Lee, T. E., Leal, I., Kuang, Y., Kalashnikov, D., Julian, R., Joshi, N. J., Irpan, A., Ichter, B., Hsu, J., Herzog, A., Hausman, K., Gopalakrishnan, K., Fu, C., Florence, P., Finn, C., Dubey, K. A., Driess, D., Ding, T., Choromanski, K. M., Chen, X., Chebotar, Y., Carbajal, J., Brown, N., Brohan, A., Arenas, M. G., and Han, K. RT-2: vision-language-action models transfer web knowledge to robotic control. In Tan, J., Toussaint, M., and Darvish, K. (eds.), Conference on Robot Learning, CoRL 2023, 6-9 November 2023, Atlanta, GA, USA, volume 229 of Proceedings of Machine Learning Research, pp. 2165-2183. PMLR, 2023. URL <a href="https://proceedings.mlr.press/v229/zitkovich23a.html" target="_blank" rel="noopener" style="word-break: break-all">https://proceedings.mlr.press/v229/zitkovich23a.html</a>.</div>
<div class="preview-paragraph-546 preview-line 546 547 548 549 550 551 552 553 554 555 556 557 558 559 560 561 562" data_line_start="546" data_line_end="562" data_line="546,563" count_line="17">Zuo, Y., Zhang, K., Qu, S., Sheng, L., Zhu, X., Qi, B., Sun, Y., Cui, G., Ding, N., and Zhou, B. Ttrl: Test-time reinforcement learning, 2025. URL <a href="https://arxiv.org/abs/2504.16084" target="_blank" rel="noopener" style="display: inline-block">https://arxiv.org/abs/2504.16084</a>.<br>
Appendix<br>
Appendix Contents<br>
A Reinforcement Learning with Verifiable Rewards. ..... 21<br>
B Implementation Details ..... 21<br>
C More Results ..... 22<br>
C. 1 Out-of-Distribution Performance Breakdown ..... 22<br>
C. 2 In-Distribution Results ..... 22<br>
C. 3 Interplay Between Propose and Solve Roles ..... 22<br>
C. 4 Complexity and Diversity Metrics of AZR Proposed Tasks ..... 32<br>
C. 5 Generated Code Complexity Dynamics Between Abd/Ded and Ind. ..... 32<br>
D Alternative Approaches Considered ..... 49<br>
D. 1 Error Deduction Task ..... 49<br>
D. 2 Composite Functions as Curriculum Learning ..... 49<br>
D. 3 Toying with the Initial <span class="math-inline "><mathml style="display: none"><math xmlns="http://www.w3.org/1998/Math/MathML">
  <mi>p</mi>
  <mo stretchy="false">(</mo>
  <mi>z</mi>
  <mo stretchy="false">)</mo>
</math></mathml><mathmlword style="display: none"><math xmlns="http://www.w3.org/1998/Math/MathML">
  <mi>p</mi>
  <mo stretchy="false">(</mo>
  <mi>z</mi>
  <mo stretchy="false">)</mo>
</math></mathmlword><asciimath style="display: none;">p(z)</asciimath><latex style="display: none">p(z)</latex><mjx-container class="MathJax" jax="SVG" style="position: relative;"><svg style="vertical-align: -0.566ex;" xmlns="http://www.w3.org/2000/svg" width="3.95ex" height="2.262ex" role="img" focusable="false" viewBox="0 -750 1746 1000" aria-hidden="true"><g stroke="currentColor" fill="currentColor" stroke-width="0" transform="scale(1,-1)"><g data-mml-node="math"><g data-mml-node="mi"><path data-c="1D45D" d="M23 287Q24 290 25 295T30 317T40 348T55 381T75 411T101 433T134 442Q209 442 230 378L240 387Q302 442 358 442Q423 442 460 395T497 281Q497 173 421 82T249 -10Q227 -10 210 -4Q199 1 187 11T168 28L161 36Q160 35 139 -51T118 -138Q118 -144 126 -145T163 -148H188Q194 -155 194 -157T191 -175Q188 -187 185 -190T172 -194Q170 -194 161 -194T127 -193T65 -192Q-5 -192 -24 -194H-32Q-39 -187 -39 -183Q-37 -156 -26 -148H-6Q28 -147 33 -136Q36 -130 94 103T155 350Q156 355 156 364Q156 405 131 405Q109 405 94 377T71 316T59 280Q57 278 43 278H29Q23 284 23 287ZM178 102Q200 26 252 26Q282 26 310 49T356 107Q374 141 392 215T411 325V331Q411 405 350 405Q339 405 328 402T306 393T286 380T269 365T254 350T243 336T235 326L232 322Q232 321 229 308T218 264T204 212Q178 106 178 102Z"></path></g><g data-mml-node="mo" transform="translate(503,0)"><path data-c="28" d="M94 250Q94 319 104 381T127 488T164 576T202 643T244 695T277 729T302 750H315H319Q333 750 333 741Q333 738 316 720T275 667T226 581T184 443T167 250T184 58T225 -81T274 -167T316 -220T333 -241Q333 -250 318 -250H315H302L274 -226Q180 -141 137 -14T94 250Z"></path></g><g data-mml-node="mi" transform="translate(892,0)"><path data-c="1D467" d="M347 338Q337 338 294 349T231 360Q211 360 197 356T174 346T162 335T155 324L153 320Q150 317 138 317Q117 317 117 325Q117 330 120 339Q133 378 163 406T229 440Q241 442 246 442Q271 442 291 425T329 392T367 375Q389 375 411 408T434 441Q435 442 449 442H462Q468 436 468 434Q468 430 463 420T449 399T432 377T418 358L411 349Q368 298 275 214T160 106L148 94L163 93Q185 93 227 82T290 71Q328 71 360 90T402 140Q406 149 409 151T424 153Q443 153 443 143Q443 138 442 134Q425 72 376 31T278 -11Q252 -11 232 6T193 40T155 57Q111 57 76 -3Q70 -11 59 -11H54H41Q35 -5 35 -2Q35 13 93 84Q132 129 225 214T340 322Q352 338 347 338Z"></path></g><g data-mml-node="mo" transform="translate(1357,0)"><path data-c="29" d="M60 749L64 750Q69 750 74 750H86L114 726Q208 641 251 514T294 250Q294 182 284 119T261 12T224 -76T186 -143T145 -194T113 -227T90 -246Q87 -249 86 -250H74Q66 -250 63 -250T58 -247T55 -238Q56 -237 66 -225Q221 -64 221 250T66 725Q56 737 55 738Q55 746 60 749Z"></path></g></g></g></svg><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>p</mi><mo stretchy="false">(</mo><mi>z</mi><mo stretchy="false">)</mo></math></mjx-assistive-mml></mjx-container></span> ..... 49<br>
D. 4 Extra Rewards ..... 49<br>
D. 5 Environment Transition ..... 50</div>
    </div>
  </div>
</body>
</html>