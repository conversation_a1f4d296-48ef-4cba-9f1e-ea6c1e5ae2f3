# ============================================================================
# 🔬 VALIDATION ENVIRONNEMENTALE BCT (Référence Plan : Lignes 1782-1807)
# ============================================================================

class BaccaratEnvironment:
    """
    Environnement de validation pour BCT-AZR
    Équivalent du Code Executor d'AZR

    Référence Plan : Lignes 1782-1807 (Validation Environnementale BCT)
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.BaccaratEnvironment")
        self.validation_history = []

        self.logger.info("BaccaratEnvironment initialisé - Équivalent Code Executor AZR")

    def validate_prediction(self, prediction: str, actual_result: str) -> bool:
        """
        Validation objective des prédictions S/O
        Feedback déterministe comme dans AZR

        Référence Plan : Lignes 1792-1797 (validate_prediction)

        Args:
            prediction: Prédiction du système ('S' ou 'O')
            actual_result: Résultat réel ('S' ou 'O')

        Returns:
            bool: True si prédiction correcte
        """
        is_correct = prediction == actual_result

        # Enregistrer pour historique
        validation_record = {
            'timestamp': datetime.now(),
            'prediction': prediction,
            'actual': actual_result,
            'correct': is_correct,
            'validation_type': 'so_prediction'
        }

        self.validation_history.append(validation_record)

        self.logger.debug(f"Validation S/O: {prediction} vs {actual_result} "
                         f"({'OK' if is_correct else 'KO'})")

        return is_correct

    def validate_pattern_analysis(self, analysis: Dict, history: List) -> float:
        """
        Validation de la qualité d'analyse des patterns
        Grounding réel dans l'historique Baccarat

        Référence Plan : Lignes 1799-1807 (validate_pattern_analysis)

        Args:
            analysis: Analyse des patterns produite par le système
            history: Historique Baccarat réel

        Returns:
            float: Score de qualité [0, 1]
        """
        if not analysis or not history:
            return 0.0

        quality_score = 0.0
        validation_components = []

        # 1. Vérifier cohérence des corrélations détectées (ligne 1804)
        correlation_coherence = self._verify_correlation_coherence(analysis, history)
        validation_components.append(correlation_coherence)

        # 2. Mesurer significativité statistique (ligne 1805)
        statistical_significance = self._measure_statistical_significance(analysis, history)
        validation_components.append(statistical_significance)

        # 3. Valider patterns philosophiques (Pair/Impair)
        philosophical_validity = self._validate_philosophical_patterns(analysis, history)
        validation_components.append(philosophical_validity)

        # 4. Vérifier exploitation TIE
        tie_exploitation_quality = self._validate_tie_exploitation(analysis, history)
        validation_components.append(tie_exploitation_quality)

        # Score de qualité global [0,1] (ligne 1806)
        quality_score = sum(validation_components) / len(validation_components)

        # Enregistrer validation
        validation_record = {
            'timestamp': datetime.now(),
            'quality_score': quality_score,
            'components': {
                'correlation_coherence': correlation_coherence,
                'statistical_significance': statistical_significance,
                'philosophical_validity': philosophical_validity,
                'tie_exploitation_quality': tie_exploitation_quality
            },
            'validation_type': 'pattern_analysis',
            'history_length': len(history)
        }

        self.validation_history.append(validation_record)

        self.logger.info(f"Validation Pattern Analysis: {quality_score:.3f} "
                        f"(historique: {len(history)} éléments)")

        return quality_score

    def get_validation_statistics(self) -> Dict[str, Any]:
        """
        Retourne les statistiques de validation

        Returns:
            Dict: Statistiques complètes de validation
        """
        if not self.validation_history:
            return {
                'total_validations': 0,
                'prediction_accuracy': 0.0,
                'average_pattern_quality': 0.0
            }

        # Séparer par type de validation
        prediction_validations = [v for v in self.validation_history if v['validation_type'] == 'so_prediction']
        pattern_validations = [v for v in self.validation_history if v['validation_type'] == 'pattern_analysis']

        # Calculer statistiques prédictions
        prediction_accuracy = 0.0
        if prediction_validations:
            correct_predictions = sum(1 for v in prediction_validations if v['correct'])
            prediction_accuracy = correct_predictions / len(prediction_validations)

        # Calculer qualité moyenne patterns
        average_pattern_quality = 0.0
        if pattern_validations:
            total_quality = sum(v['quality_score'] for v in pattern_validations)
            average_pattern_quality = total_quality / len(pattern_validations)

        return {
            'total_validations': len(self.validation_history),
            'prediction_validations': len(prediction_validations),
            'pattern_validations': len(pattern_validations),
            'prediction_accuracy': prediction_accuracy,
            'average_pattern_quality': average_pattern_quality,
            'last_validation': self.validation_history[-1]['timestamp'].isoformat() if self.validation_history else None,
            'environment_active': True
        }

    # ========================================================================
    # 🔧 MÉTHODES UTILITAIRES VALIDATION ENVIRONNEMENTALE
    # ========================================================================

    def _verify_correlation_coherence(self, analysis: Dict, history: List) -> float:
        """Vérifie la cohérence des corrélations détectées"""
        if not analysis.get('7_dimensional'):
            return 0.0

        # Vérifier cohérence des corrélations 7D
        correlations = analysis['7_dimensional']
        coherence_score = 0.0

        # Vérifier que les corrélations sont dans des plages réalistes
        for corr_name, corr_value in correlations.items():
            if isinstance(corr_value, (int, float)):
                if 0.0 <= abs(corr_value) <= 1.0:  # Corrélation valide
                    coherence_score += 0.2

        return min(1.0, coherence_score)

    def _measure_statistical_significance(self, analysis: Dict, history: List) -> float:
        """Mesure la significativité statistique"""
        if len(history) < 10:
            return 0.0  # Pas assez de données

        # Mesurer significativité basée sur taille échantillon
        sample_size_score = min(1.0, len(history) / 50.0)  # Normaliser sur 50 éléments

        # Vérifier variabilité des patterns
        variability_score = 0.5  # Valeur par défaut
        if analysis.get('subsequences'):
            variability_score = 0.8  # Bonus si sous-séquences analysées

        return (sample_size_score + variability_score) / 2

    def _validate_philosophical_patterns(self, analysis: Dict, history: List) -> float:
        """Valide les patterns philosophiques Pair/Impair"""
        if not analysis.get('philosophy'):
            return 0.0

        philosophy = analysis['philosophy']
        validity_score = 0.0

        # Vérifier hiérarchie de priorité : impair_5 > pair_6 > pair_4
        if philosophy.get('priority_hierarchy'):
            hierarchy = philosophy['priority_hierarchy']
            if (hierarchy.get('impair_5_weight', 0) > hierarchy.get('pair_6_weight', 0) >
                hierarchy.get('pair_4_weight', 0)):
                validity_score += 0.5

        # Vérifier pouvoir de transformation impair_5
        if philosophy.get('impair_5_transformations'):
            validity_score += 0.3

        # Vérifier stabilité des PAIR
        if philosophy.get('pair_continuity_power'):
            validity_score += 0.2

        return validity_score

    def _validate_tie_exploitation(self, analysis: Dict, history: List) -> float:
        """Valide l'exploitation des TIE"""
        if not analysis.get('tie_exploitation'):
            return 0.0

        tie_analysis = analysis['tie_exploitation']
        exploitation_quality = 0.0

        # Vérifier enrichissement INDEX 1&2
        if tie_analysis.get('tie_index1_enrichment'):
            exploitation_quality += 0.3
        if tie_analysis.get('tie_index2_enrichment'):
            exploitation_quality += 0.3

        # Vérifier prédiction post-TIE
        if tie_analysis.get('post_tie_prediction'):
            exploitation_quality += 0.2

        # Vérifier avantage compétitif
        if tie_analysis.get('competitive_advantage'):
            advantage = tie_analysis['competitive_advantage']
            if advantage.get('advantage_score', 0) > 0.2:
                exploitation_quality += 0.2

        return exploitation_quality