#!/usr/bin/env python3
"""
Test de classification des 50 équations AZR par rollout
"""

from bct import AZRConfig, AZRMathEngine

def test_equation_classification():
    """Test la classification des équations par rollout"""
    
    # Créer une instance pour tester
    config = AZRConfig()
    math_engine = AZRMathEngine(config)
    
    # Tester la classification
    validation = math_engine.validate_equation_classification()
    counts = math_engine.get_rollout_equation_count()
    
    print('=== VALIDATION CLASSIFICATION DES 50 ÉQUATIONS AZR ===')
    print(f'Total classées: {validation["total_classified"]}/50')
    print(f'Complétude: {validation["is_complete"]}')
    print(f'Équilibre: {validation["is_balanced"]}')
    print(f'Validation réussie: {validation["validation_passed"]}')
    print()
    
    print('=== RÉPARTITION PAR ROLLOUT ===')
    for rollout, count in counts.items():
        print(f'{rollout}: {count} équations')
    print()
    
    if validation['missing_equations']:
        print(f'ÉQUATIONS MANQUANTES: {validation["missing_equations"]}')
    if validation['duplicate_equations']:
        print(f'ÉQUATIONS DUPLIQUÉES: {validation["duplicate_equations"]}')
    
    print('=== ÉQUATIONS PAR ROLLOUT ===')
    equations_by_rollout = math_engine.get_equations_by_rollout()
    for rollout_name, equations in equations_by_rollout.items():
        print(f'{rollout_name}: {len(equations)} équations')
        for eq in equations:
            print(f'  - {eq}')
        print()
    
    return validation["validation_passed"]

if __name__ == "__main__":
    success = test_equation_classification()
    if success:
        print("✅ CLASSIFICATION RÉUSSIE - TOUTES LES 50 ÉQUATIONS SONT CORRECTEMENT CLASSÉES !")
    else:
        print("❌ PROBLÈME DE CLASSIFICATION DÉTECTÉ")
