🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Model
🔗 Input:
🔗 Here
🔗 is
🔗 the
🔗 function
🔗 f
🔗 and
🔗 the
🔗 output:
🔗 [
🔗 [ 5 , 3 , . , . , 7 , . , . , . , . ],
🔗 [”6”,”.”,”.”,”1”,”9”,”5”,”.”,”.”,”.”],
🔗 [”.”,”9”,”8”,”.”,”.”,”.”,”.”,”6”,”.”],
🔗 [”8”,”.”,”.”,”.”,”6”,”.”,”.”,”.”,”3”],
🔗 [”4”,”.”,”.”,”8”,”.”,”3”,”.”,”.”,”1”],
🔗 [”7”,”.”,”.”,”.”,”2”,”.”,”.”,”.”,”6”],
🔗 [”.”,”6”,”.”,”.”,”.”,”.”,”2”,”8”,”.”],
🔗 [”.”,”.”,”.”,”4”,”1”,”9”,”.”,”.”,”5”], [”.”,”.”,”.”,”.”,”8”,”.”,”.”,”7”,”9”] ], please predict the input.
🔗 Model Output:
🔗 To determine the input that produced the given output, we need to analyze the function ‘f‘ and understand
🔗 what it does. Let’s break down the function step by step:
🔗 1. **Input Validation in ‘g_0‘**: - The function ‘g_0‘ checks if the given board is valid. - A board is valid
🔗 if each row, each column, and each 3x3 subgrid contains the digits 1 through 9 exactly once. - If the board
🔗 is not valid, ‘g_0‘ returns ‘False‘.
🔗 2. **Input Validation in ‘f‘**: - The function ‘f‘ first checks if there are any ”.” in the board. If there are, it
🔗 raises a ‘ValueError‘. - This means the input board to ‘f‘ must be a complete board with no ”.”.
🔗 3. **Masking the Board**: - If the board is valid (i.e., ‘g_0(board)‘ returns ‘True‘), the function ‘f‘ proceeds
🔗 to mask the board. - It creates a copy of the board and then randomly selects 51 positions to mask with ”.”.
🔗 - The masked board is then returned.
🔗 Given
🔗 the
🔗 output:
🔗 “‘output
🔗 [[”5”,”3”,”.”,”.”,”7”,”.”,”.”,”.”,”.”],
🔗 [”6”,”.”,”.”,”1”,”9”,”5”,”.”,”.”,”.”],
🔗 [”.”,”9”,”8”,”.”,”.”,”.”,”.”,”6”,”.”],
🔗 [”8”,”.”,”.”,”.”,”6”,”.”,”.”,”.”,”3”],
🔗 [”4”,”.”,”.”,”8”,”.”,”3”,”.”,”.”,”1”],
🔗 [”7”,”.”,”.”,”.”,”2”,”.”,”.”,”.”,”6”],
🔗 [”.”,”6”,”.”,”.”,”.”,”.”,”2”,”8”,”.”],
🔗 [”.”,”.”,”.”,”4”,”1”,”9”,”.”,”.”,”5”],
🔗 [”.”,”.”,”.”,”.”,”8”,”.”,”.”,”7”,”9”]] “‘ We can see that the board has 51 positions masked with ”.”. Therefore,
🔗 the input board must have been a complete board, and ‘f‘ masked 51 random positions.
🔗 Let’s construct a complete board that, when masked, can produce the given output. We need to find a board
🔗 such that after masking 51 positions, we get the output shown.
🔗 Here is a complete board that matches the structure of the output: “‘python [ [”5”, ”3”, ”4”, ”6”, ”7”, ”8”,
🔗 ”9”, ”1”, ”2”], [”6”, ”7”, ”2”, ”1”, ”9”, ”5”, ”3”, ”4”, ”8”], [”1”, ”9”, ”8”, ”3”, ”4”, ”2”, ”5”, ”6”, ”7”], [”8”,
🔗 ”5”, ”9”, ”7”, ”6”, ”1”, ”4”, ”2”, ”3”], [”4”, ”2”, ”6”, ”8”, ”5”, ”3”, ”7”, ”9”, ”1”], [”7”, ”1”, ”3”, ”9”, ”2”,
🔗 ”4”, ”8”, ”5”, ”6”], [”9”, ”6”, ”1”, ”5”, ”3”, ”7”, ”2”, ”8”, ”4”], [”2”, ”8”, ”7”, ”4”, ”1”, ”9”, ”6”, ”3”, ”5”],
🔗 [”3”, ”4”, ”5”, ”2”, ”8”, ”6”, ”1”, ”7”, ”9”] ] “‘
🔗 Let’s verify that this board, when passed to the function ‘f‘, produces the given output.
🔗 1. The board is complete with no ”.”. 2. The board is valid because each row, each column, and each 3x3
🔗 subgrid contains the digits 1 through 9 exactly once. 3. The function ‘f‘ will mask 51 random positions.
🔗 Given the random nature of the masking, we need to ensure that the positions masked match the positions
🔗 of ”.” in the output. Since the positions are random, we can assume that the function ‘f‘ with this complete
🔗 board will produce the given output.
🔗 Therefore, the input that produced the output is: “‘input [ [”5”, ”3”, ”4”, ”6”, ”7”, ”8”, ”9”, ”1”, ”2”], [”6”,
🔗 ”7”, ”2”, ”1”, ”9”, ”5”, ”3”, ”4”, ”8”], [”1”, ”9”, ”8”, ”3”, ”4”, ”2”, ”5”, ”6”, ”7”], [”8”, ”5”, ”9”, ”7”, ”6”,
🔗 ”1”, ”4”, ”2”, ”3”], [”4”, ”2”, ”6”, ”8”, ”5”, ”3”, ”7”, ”9”, ”1”], [”7”, ”1”, ”3”, ”9”, ”2”, ”4”, ”8”, ”5”, ”6”],
🔗 [”9”, ”6”, ”1”, ”5”, ”3”, ”7”, ”2”, ”8”, ”4”], [”2”, ”8”, ”7”, ”4”, ”1”, ”9”, ”6”, ”3”, ”5”], [”3”, ”4”, ”5”, ”2”,
🔗 ”8”, ”6”, ”1”, ”7”, ”9”] ] “‘
🔗 Figure 40. Vibe Check 1: Sudoku.
🔗 46
🔗 Figure 40. Vibe Check 1: Sudoku Solver. We cast Sudoku solving as an abduction task: our program starts from a fully solved and
🔗 validated Sudoku board and simulates the masking of 51 random cells. The masked board is then presented as output, and the model
🔗 is tasked with inferring the original input — effectively solving the puzzle backwards. An example solution is shown above, where
🔗 AZR-Coder-14b verifies its initial guess before correctly answering. Generation parameters: temperature=0.6.
🔗 46