# FONCTIONNEMENT TECHNIQUE DÉTAILLÉ D'AZR

## 📋 INFORMATIONS TECHNIQUES

**Date :** 12 juin 2025  
**Sources :** text_pages/page_007.txt + page_008.txt + equations_synthesis.txt  
**Objectif :** Comprendre le fonctionnement interne complet d'AZR  
**Niveau :** Documentation technique approfondie  

---

## 🔧 ALGORITHME COMPLET D'AZR

### Algorithm 1: Self-Play Training of Absolute Zero Reasoner (AZR)

#### **Prérequis**
- **Pretrained base LLM** : π_θ (modèle de base pré-entraîné)
- **Batch size** : B (taille de batch)
- **#references** : K (nombre de références)
- **Iterations** : T (nombre d'itérations)

#### **Phase d'initialisation**
```
1: D_ded, D_abd, D_ind ← InitSeeding(π_θ)  ▷ voir §3.3.1
```

#### **Boucle d'entraînement principal - ALGORITHME 1 COMPLET**
```
2: for t ← 1 to T do
3:   for b ← 1 to B do                     ▷ PROPOSE PHASE
4:     p ∼ P_ind ∪ P_ded                   ▷ sample a program for induction task proposal
5:     {i^n}_{n=1}, m_x ← π_θ^propose(ind, p)  ▷ generate N inputs and a description
6:     {(i^n, o^n)} ← VALIDATEBYEXECUTING(p, {i^n}, SYNTAX) then  ▷ validate I/Os, see §3.3.3
7:       P_ind ← P_ind ∪ {(p, {(i^n, o^n)}, m_x)}  ▷ update induction buffer
8:   for α ∈ {ded, abd} do
9:     (p_k, i_k, o_k)^K_{k=1} ← P_α       ▷ sample K reference examples
10:    (p_x, i_x) ← π_θ^propose(α, {(p_k, i_k, o_k)})  ▷ propose new task
11:    if o_x ← VALIDATEBYEXECUTING(p_x, i_x, SYNTAX,SAFETY,DETERMINISM) then  ▷ see §3.3.3
12:      P_α ← P_α ∪ {(p_x, i_x, o_x)}     ▷ if valid, update deduction or abduction buffers
13:  for all α ∈ {ded, abd, ind} do        ▷ SOLVE PHASE
14:    (x, y*) ← SAMPLEPREPAREDTASKS(P_α, B, t)  ▷ x, y* prepared based on α, see §3.3.4&3.4
15:    y_x ∼ π_θ^solve(x)                  ▷ solve
16: Reward: Use proposed task triplets and solved answers to get r_propose & r_solve  ▷ see §3.1
17: RL update: use Task Relative REINFORCE++ to update π_θ  ▷ see §3.3.5
15:      y_π ∼ π^{solve}_θ(·|x)           ▷ résoudre tâche
16:  Reward: Use proposed task triplets and solved answers to get r^{propose} & r^{solve}
17:  RL update: use Task Relative REINFORCE++ to update π_θ
```

---

## 🗂️ GESTION DES BUFFERS ET TÂCHES

### 3.3.2. Task Proposal Inputs and Buffer Management

#### **Trois utilisations des buffers**

1. **Pour les proposers abduction/déduction**
   > *"We uniformly sample K past triplets from the buffer, present them as in-context examples to the proposer and let it generate a new task. The design is to show it past examples, and prompt it to generate a different one to promote diversity"* (Page 7)

2. **Pour le proposer induction**
   > *"We sample one triplet from the union of abduction and deduction buffers D_abd ∪ D_ded, and present the program p from that triplet to the induction proposer to generate a set of N matching inputs {i_n} and a natural language message m"* (Page 7)

3. **Pour la stabilité d'entraînement**
   > *"To maintain stable training, if a batch of solver problems contains fewer than B valid proposed tasks (proposer not adhering to formatting), we fill the remainder by uniformly sampling from the corresponding task buffer of previously validated triplets"* (Page 7)

#### **Croissance des buffers**
- **Abduction/Déduction** : "The buffer grows for abduction and deduction tasks whenever π propose a valid triplet (p, i, o), regardless if it gets any task reward"
- **Induction** : "Similarly, for induction tasks, all valid triplets (p, {i_n, o_n}), m are added to the buffer"

---

## ✅ VALIDATION ET VÉRIFICATION DES TÂCHES

### 3.3.3. Constructing Valid Tasks

#### **Validation des propositions de tâches**

**Pour déduction et abduction :**
> *"For deduction and abduction tasks, each proposal consists of a program and an input (p, i). To validate the task, we use the task validation procedure on the input to obtain the correct output o, resulting in a complete triplet (p, i, o)"* (Page 7)

**Pour induction :**
> *"For induction tasks, given a program p the policy proposes a set of inputs {i_n} and message m. We also use the task validation procedure on each of the input i_n"* (Page 7)

#### **Programmes déterministes - Équation (7)**

```
∀p ∈ P_deterministic, ∀i ∈ I, lim_{j→∞} p(i)^{(1)} = p(i)^{(2)} = ... = p(i)^{(j)}
```

**Explication technique :**
> *"Where (j) indexes repeated independent executions of the program. That is, for all inputs i, the output of p(i) remains identical with any independent execution of the program"* (Page 8)

**Implémentation pratique :**
> *"To implement the filtering of invalid probabilistic programs, and following the definition of a deterministic program highlighted in Equation (7), we approximate this procedure by independently running the program j finite times and checking that all the outputs are equal. For computational budget reasons, we fixed j = 2 for all experiments"* (Page 8)

**Justification :**
> *"Since the output of probabilistic programs can vary on every individual run, it is non-trivial to use verifiable functions to evaluate the correctness of an answer. Therefore, to keep the verifier simple, we restrict the valid programs generated by the learner to the class of deterministic programs"* (Page 8)

---

## 🔍 VÉRIFICATION DES RÉPONSES

### 3.3.4. Answer Verification

#### **Vérification par type de tâche**

**Abduction :**
```
p(i_π) = p(i⋆)
```
> *"For abduction task, we receive i_π from the solver policy, then we equivalence match using p(i_π) = p(i⋆), where ⋆ refers to the"* (Page 8)

**Déduction :**
```
o_π = o⋆
```
> *"For deduction task, we receive o_π from the solver policy, then we equivalence match using o_π = o⋆"* (Page 8)

**Induction :**
```
all({p_π(i⋆_n) = o⋆_n}_N)
```
> *"For induction task, we receive p_π from the solver policy, then we equivalence match using all({p_π(i⋆_n) = o⋆_n}_N). This part might be convoluted to explain in language, therefore we recommend the reader to see how we did abduction, deduction and induction verification in code in Figures 10 to 12, respectively"* (Page 8)

---

## ⚡ TASK-RELATIVE REINFORCE++ (TRR++)

### 3.3.5. Innovation algorithmique

#### **Contexte multitâche**
> *"Since AZR trains the combination of roles and task types, it operates in a multitask reinforcement learning setup"* (Page 8)

#### **🔢 ÉQUATION (8) : AVANTAGE NORMALISÉ TRR++**
```
A^task = (r - μ_task,role) / σ_task,role,    task ∈ {ded,abd,ind}, role ∈ {propose,solve}
```

**Où :**
- **r** : Récompense obtenue
- **μ_task,role** : Moyenne des récompenses pour cette combinaison (task, role)
- **σ_task,role** : Écart-type des récompenses pour cette combinaison (task, role)
- **A^task** : Avantage normalisé utilisé pour la mise à jour des gradients

#### **📊 6 BASELINES SÉPARÉES**
```
Baselines maintenues :
1. μ_ded,propose & σ_ded,propose
2. μ_ded,solve & σ_ded,solve
3. μ_abd,propose & σ_abd,propose
4. μ_abd,solve & σ_abd,solve
5. μ_ind,propose & σ_ind,propose
6. μ_ind,solve & σ_ind,solve
```

**Innovation :** Réduction de variance optimale en séparant les statistiques par type de tâche ET par rôle, permettant une normalisation précise pour chaque contexte d'apprentissage.

#### **Amélioration par rapport à REINFORCE++**
> *"Instead of computing a single global baseline as in REINFORCE++, we compute separate baselines for each of the six task-role configurations. This can be viewed as an interpolation between per-question baselines, as in GRPO, and a global baseline, allowing for more structured variance reduction tailored to each task setup"* (Page 8)

#### **Six configurations task-role**
1. **Déduction-Propose**
2. **Déduction-Solve**
3. **Abduction-Propose**
4. **Abduction-Solve**
5. **Induction-Propose**
6. **Induction-Solve**

#### **Avantage normalisé**
```
A^{norm}_{task,role} = (r - μ_{task,role}) / σ_{task,role}
```
> *"The normalized advantage A^{norm} is computed as"* (Page 8)

---

## 🧮 IMPLÉMENTATION TECHNIQUE COMPLÈTE

```python
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import subprocess
import tempfile
import os

class AZRTechnicalImplementation:
    """
    Implémentation technique complète d'AZR basée sur l'algorithme détaillé
    """
    
    def __init__(self, batch_size: int = 64, k_references: int = 5, 
                 iterations: int = 1000, j_deterministic: int = 2):
        self.B = batch_size
        self.K = k_references
        self.T = iterations
        self.j = j_deterministic  # Vérifications déterministes
        
        # Buffers pour les trois types de tâches
        self.D_ded = []  # Buffer déduction
        self.D_abd = []  # Buffer abduction
        self.D_ind = []  # Buffer induction
        
        # Statistiques pour TRR++
        self.task_role_stats = defaultdict(lambda: {'rewards': [], 'mean': 0.0, 'std': 1.0})
    
    def init_seeding(self, model: nn.Module) -> None:
        """
        Initialisation des buffers avec des triplets seed
        """
        # Triplet identité minimal pour bootstrap
        identity_triplet = {
            'program': 'def f(x): return x',
            'input': 'Hello World',
            'output': 'Hello World'
        }
        
        # Initialiser tous les buffers avec le triplet seed
        self.D_ded.append(identity_triplet)
        self.D_abd.append(identity_triplet)
        self.D_ind.append(identity_triplet)
    
    def self_play_training(self, model: nn.Module) -> None:
        """
        Algorithme 1: Self-Play Training of Absolute Zero Reasoner (AZR)
        """
        # 1: Initialisation
        self.init_seeding(model)
        
        # 2: Boucle d'entraînement principal
        for t in range(1, self.T + 1):
            
            # PROPOSE PHASE
            proposed_tasks = self.propose_phase(model)
            
            # SOLVE PHASE
            solved_tasks = self.solve_phase(model, proposed_tasks)
            
            # REWARD CALCULATION
            rewards = self.calculate_rewards(proposed_tasks, solved_tasks)
            
            # RL UPDATE avec TRR++
            self.trr_plus_plus_update(model, rewards)
    
    def propose_phase(self, model: nn.Module) -> Dict[str, List]:
        """
        Phase de proposition de tâches pour les trois types
        """
        proposed_tasks = {'ded': [], 'abd': [], 'ind': []}
        
        # Proposition pour induction
        for b in range(self.B):
            # 4: Échantillonner programme des buffers abd ∪ ded
            p = self.sample_program_from_union()
            
            # 5: Générer N entrées et description
            inputs, message = self.propose_induction_task(model, p)
            
            # 6-7: Validation et mise à jour buffer
            if self.validate_induction_task(p, inputs, message):
                task = {'program': p, 'inputs': inputs, 'message': message}
                proposed_tasks['ind'].append(task)
                self.D_ind.append(task)
        
        # Proposition pour déduction et abduction
        for task_type in ['ded', 'abd']:
            for b in range(self.B):
                # 10: Proposer tâche avec K références
                k_refs = self.sample_k_references(task_type)
                program, input_val, output_val = self.propose_task(model, task_type, k_refs)
                
                # 11-12: Validation et mise à jour buffer
                if self.validate_task(program, input_val, output_val):
                    task = {'program': program, 'input': input_val, 'output': output_val}
                    proposed_tasks[task_type].append(task)
                    getattr(self, f'D_{task_type}').append(task)
        
        return proposed_tasks
    
    def solve_phase(self, model: nn.Module, proposed_tasks: Dict) -> Dict:
        """
        Phase de résolution des tâches proposées
        """
        solved_tasks = {}
        
        for task_type in ['ded', 'abd', 'ind']:
            solved_tasks[task_type] = []
            
            for task in proposed_tasks[task_type]:
                # 15: Résoudre tâche
                solution = self.solve_task(model, task_type, task)
                solved_tasks[task_type].append({
                    'task': task,
                    'solution': solution
                })
        
        return solved_tasks
    
    def validate_deterministic_program(self, program: str, input_val: str) -> bool:
        """
        Validation déterministe selon l'équation (7)
        ∀p ∈ P_deterministic, ∀i ∈ I, lim_{j→∞} p(i)^{(1)} = p(i)^{(2)} = ... = p(i)^{(j)}
        """
        outputs = []
        
        for execution in range(self.j):  # j = 2 pour budget computationnel
            try:
                output = self.execute_program_safely(program, input_val)
                outputs.append(output)
            except Exception:
                return False  # Programme invalide
        
        # Vérifier que toutes les sorties sont identiques
        return all(output == outputs[0] for output in outputs)
    
    def verify_answer(self, task_type: str, task: Dict, solution: str) -> bool:
        """
        Vérification des réponses selon le type de tâche (Section 3.3.4)
        """
        if task_type == 'abd':  # Abduction: p(i_π) = p(i⋆)
            try:
                pred_output = self.execute_program_safely(task['program'], solution)
                true_output = self.execute_program_safely(task['program'], task['input'])
                return pred_output == true_output
            except:
                return False
        
        elif task_type == 'ded':  # Déduction: o_π = o⋆
            return solution == task['output']
        
        elif task_type == 'ind':  # Induction: all({p_π(i⋆_n) = o⋆_n}_N)
            try:
                for input_val, expected_output in zip(task['inputs'], task['outputs']):
                    actual_output = self.execute_program_safely(solution, input_val)
                    if actual_output != expected_output:
                        return False
                return True
            except:
                return False
        
        return False
    
    def trr_plus_plus_update(self, model: nn.Module, rewards: Dict) -> None:
        """
        Task-Relative REINFORCE++ (TRR++) - Section 3.3.5
        Calcul de baselines séparées pour chaque configuration task-role
        """
        # Mise à jour des statistiques pour chaque (task, role)
        for task_type in ['ded', 'abd', 'ind']:
            for role in ['propose', 'solve']:
                key = f"{task_type}_{role}"
                
                if key in rewards:
                    # Ajouter nouvelles récompenses
                    self.task_role_stats[key]['rewards'].extend(rewards[key])
                    
                    # Recalculer moyenne et écart-type
                    recent_rewards = self.task_role_stats[key]['rewards'][-100:]  # Fenêtre glissante
                    self.task_role_stats[key]['mean'] = np.mean(recent_rewards)
                    self.task_role_stats[key]['std'] = np.std(recent_rewards) + 1e-8
        
        # Calcul des avantages normalisés
        normalized_advantages = {}
        for task_type in ['ded', 'abd', 'ind']:
            for role in ['propose', 'solve']:
                key = f"{task_type}_{role}"
                if key in rewards:
                    stats = self.task_role_stats[key]
                    advantages = [(r - stats['mean']) / stats['std'] for r in rewards[key]]
                    normalized_advantages[key] = advantages
        
        # Mise à jour du modèle avec avantages normalisés
        self.update_model_parameters(model, normalized_advantages)
    
    def execute_program_safely(self, program: str, input_val: str) -> str:
        """
        Exécution sécurisée d'un programme Python
        """
        try:
            # Créer un fichier temporaire pour l'exécution
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(program)
                f.write(f"\nprint(f({repr(input_val)}))")
                temp_file = f.name
            
            # Exécuter le programme
            result = subprocess.run(['python', temp_file], 
                                  capture_output=True, text=True, timeout=5)
            
            # Nettoyer
            os.unlink(temp_file)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                raise RuntimeError(f"Execution error: {result.stderr}")
        
        except Exception as e:
            raise RuntimeError(f"Program execution failed: {e}")
    
    # Méthodes auxiliaires
    def sample_program_from_union(self) -> str:
        """Échantillonner programme de D_abd ∪ D_ded"""
        union_buffer = self.D_abd + self.D_ded
        return np.random.choice(union_buffer)['program']
    
    def sample_k_references(self, task_type: str) -> List[Dict]:
        """Échantillonner K références du buffer correspondant"""
        buffer = getattr(self, f'D_{task_type}')
        k = min(self.K, len(buffer))
        return np.random.choice(buffer, k, replace=False).tolist()
    
    def propose_task(self, model: nn.Module, task_type: str, k_refs: List) -> Tuple:
        """Proposer une nouvelle tâche"""
        # Implémentation simplifiée - à adapter selon le modèle
        return "def f(x): return x*2", "5", "10"
    
    def propose_induction_task(self, model: nn.Module, program: str) -> Tuple:
        """Proposer tâche d'induction"""
        # Implémentation simplifiée
        return ["1", "2", "3"], "Double the input"
    
    def solve_task(self, model: nn.Module, task_type: str, task: Dict) -> str:
        """Résoudre une tâche"""
        # Implémentation simplifiée - à adapter selon le modèle
        return "solution"
    
    def validate_task(self, program: str, input_val: str, output_val: str) -> bool:
        """Valider une tâche"""
        return self.validate_deterministic_program(program, input_val)
    
    def validate_induction_task(self, program: str, inputs: List, message: str) -> bool:
        """Valider une tâche d'induction"""
        return True  # Implémentation simplifiée
    
    def calculate_rewards(self, proposed_tasks: Dict, solved_tasks: Dict) -> Dict:
        """Calculer les récompenses"""
        return {}  # Implémentation simplifiée
    
    def update_model_parameters(self, model: nn.Module, advantages: Dict) -> None:
        """Mettre à jour les paramètres du modèle"""
        pass  # Implémentation simplifiée

# Exemple d'utilisation
azr_tech = AZRTechnicalImplementation(batch_size=64, k_references=5)
print("=== IMPLÉMENTATION TECHNIQUE AZR ===")
print("Algorithme complet avec gestion des buffers, validation déterministe et TRR++")
```

---

## ✅ POINTS CLÉS TECHNIQUES

### Innovations algorithmiques
1. **Buffers multiples** : Gestion séparée pour déduction, abduction, induction
2. **Validation déterministe** : j=2 exécutions pour vérifier la reproductibilité
3. **TRR++** : 6 baselines séparées pour réduction de variance optimale
4. **Bootstrap minimal** : Initialisation avec triplet identité simple

### Robustesse du système
- **Gestion des échecs** : Remplissage par échantillonnage des buffers
- **Diversité** : Promotion explicite de tâches différentes
- **Stabilité** : Validation rigoureuse à chaque étape
- **Scalabilité** : Architecture modulaire et extensible

## 📚 **SECTIONS 3.2 & 3.3 : DÉTAILS COMPLÉMENTAIRES AZR**

### **🧠 SECTION 3.2 : MODES DE RAISONNEMENT DÉTAILLÉS**

#### **🔍 DEDUCTION : Prédiction logique étape par étape**
```
Triplet AZR: (p, i, o) où p ∈ P (programme), i ∈ I (input), o ∈ O (output)

PROPOSER (Deduction):
- AZR conditionné sur type tâche α = deduction
- Génère K exemples de référence depuis buffer P_deduction
- Environment exécute p(i) pour calculer o
- Triplet (p, i, o) ajouté au buffer si pas d'erreur de sortie

SOLVER (Deduction):
- Modèle prédit output o étant donné programme p et input i
- Vérification avec égalité type-aware en Python
- Gère variations (ordre des sets, fractions, etc.)
```

#### **🔮 ABDUCTION : Inférence plausible depuis output**
```
PROPOSER (Abduction):
- Politique π^propose génère (p, i) conditionné sur o
- Type tâche α = abduction changé en input du modèle
- Génère paire (p, i) conditionnée sur o + exemples de référence

SOLVER (Abduction):
- Modèle reçoit (p, o) et prédit i_*
- Solution vérifiée en checkant si p(i_*) = o
- Note: Programmes peuvent ne pas être bijectifs
```

#### **🌱 INDUCTION : Synthèse depuis exemples input-output**
```
PROPOSER (Induction):
- AZR échantillonne programme valide depuis P_induction ∪ P_deduction
- Génère N nouveaux inputs + message m
- Utilise environment pour calculer outputs correspondants
- Représentation: (p, {(i^n, o^n)}, m) stockée dans buffer P_induction

SOLVER (Induction):
- Modèle reçoit première moitié des paires input-output + message m
- Doit synthétiser programme p_* qui mappe inputs cachés vers outputs
- Encourage généralisation et décourage overfitting via held-out examples
```

### **🔧 SECTION 3.3 : ALGORITHME D'APPRENTISSAGE AZR**

#### **📊 FIGURE 5 : SEED AZR ZERO TRIPLET**
```python
# Programme triplet d'identité - Le plus simple fourni à AZR pour bootstrap
def f(x):
    return x

Input: "Hello World"
Output: "Hello World"
```

#### **🚀 BUFFER INITIALIZATION (Section 3.3.1) - DÉTAILS COMPLETS**
```
Processus d'Initialisation Détaillé :
1. Génération seed set de triplets valides avec base language model
2. Chaque prompt échantillonne jusqu'à K triplets depuis current set
3. Filtrage, exécution et validation des triplets
4. Stockage dans buffers appropriés selon type de tâche

Formules d'Initialisation Spécialisées :
- P^0_deduction = P^0_abduction = P_seed (où P_seed = B × S, S = 4)
- P^0_induction = B × S programmes échantillonnés depuis P_seed
- Génère matching input sets et messages, collecte valid samples jusqu'à |P^0_induction| = B × S

Variables Clés :
- B = batch size (taille du batch)
- S = facteur fixé à 4 dans tous les expériments
- K = nombre d'exemples de référence par prompt
- N = nombre d'inputs générés pour induction
```

## 📚 **SECTIONS 3.3.2 & 3.3.3 : DÉTAILS ALGORITHME COMPLETS**

### **🔄 SECTION 3.3.2 : TASK PROPOSAL INPUTS ET BUFFER MANAGEMENT**

#### **Utilisation des Buffers en 3 Modes**

##### **1. ABDUCTION & DEDUCTION TASKS**
```
- Échantillonnage de K exemples depuis buffer correspondant
- Présentation au proposer pour générer nouvelle tâche
- Design : Montrer exemples passés, prompt pour générer différente tâche (diversité)
```

##### **2. INDUCTION TASKS**
```
- Échantillonnage triplet depuis P_ind ∪ P_ded
- Présentation programme p au proposer
- Génération de N inputs correspondants {i^n} + message naturel m_x
- Maintien entraînement stable si tâche contient plus de 10 inputs
- Remplissage par échantillonnage uniforme depuis buffer si nécessaire
```

##### **3. CROISSANCE DES BUFFERS**
```
- Buffer grandit quand proposer génère triplet valide (p, i, o)
- Ajout au buffer correspondant selon type de tâche
- Pour induction : tous triplets valides {p, i^n, o^n} ajoutés au buffer
```

### **🔧 SECTION 3.3.3 : CONSTRUCTION DE TÂCHES VALIDES**

#### **✅ VALIDATION DES TÂCHES PROPOSÉES**

##### **🔍 POUR DEDUCTION ET ABDUCTION**
```
Processus de validation :
1. Construire programme p avec input i
2. Calculer tâche via procédure de validation
3. Utiliser output pour obtenir triplet complet (p, i, o)
```

##### **🌱 POUR INDUCTION**
```
Processus de validation :
1. Programme p donné par politique
2. Échantillonnage inputs depuis ensemble pour obtenir output correspondant o^n
3. Formation ensemble paires input-output {i^n, o^n}
4. Pas de contraintes imposées sur m_x
5. Tâche considérée valide si tous inputs donnent outputs valides + exigences format satisfaites
```

#### **🛡️ PROCÉDURE DE VALIDATION DÉTAILLÉE**

##### **1. Program Integrity**
```python
# Utilisation Python pour exécuter programme p avec input i
# Si erreurs levées et quelque chose retourné :
#   - Collecter output de cette paire (p, i)
#   - Déterminer que programme a au moins syntaxe valide
```

##### **2. Program Safety**
```python
# Validation si programme utilise certains packages sensibles
# Packages pouvant causer dommages à environnement Python :
#   - os, sys, shutil
# Liste fournie dans Figure 8
# Inclus dans instructions lors du prompting du modèle
```

##### **3. Check for Determinism**
```python
# Considération uniquement programmes déterministes
# p ∈ P_deterministic ⊂ P
# P = espace de tous programmes valides
# I = espace de tous inputs valides
```

### **⚡ FONCTIONS CLÉS DÉTAILLÉES**

#### **VALIDATEBYEXECUTING(p, i, FLAGS)**
```python
def validate_by_executing(program, inputs, *flags):
    """
    Validation complète avec flags multiples

    Args:
        program: Code Python à valider
        inputs: Entrées à tester
        flags: SYNTAX, SAFETY, DETERMINISM

    Returns:
        outputs si valide, None sinon
    """
    if 'SYNTAX' in flags:
        if not check_syntax(program):
            return None

    if 'SAFETY' in flags:
        if not check_safety(program):
            return None

    if 'DETERMINISM' in flags:
        if not check_determinism(program, inputs):
            return None

    return execute_program(program, inputs)
```

#### **SAMPLEPREPAREDTASKS(P_α, B, t)**
```python
def sample_prepared_tasks(buffer, batch_size, iteration):
    """
    Échantillonnage de tâches préparées selon le mode

    Args:
        buffer: Buffer spécialisé (P_ded, P_abd, P_ind)
        batch_size: Taille du batch
        iteration: Itération courante

    Returns:
        (x, y*): Problème formaté et solution de référence
    """
    tasks = []
    for _ in range(batch_size):
        triplet = buffer.sample()
        x, y_star = format_task_for_mode(triplet, buffer.mode)
        tasks.append((x, y_star))
    return tasks
```

#### **Task Relative REINFORCE++ (TRR++)**
```python
def task_relative_reinforce_plus_plus(rewards, task_types, roles):
    """
    Implémentation TRR++ avec 6 baselines séparées

    Args:
        rewards: Récompenses obtenues
        task_types: Types de tâches (ded, abd, ind)
        roles: Rôles (propose, solve)

    Returns:
        Avantages normalisés pour mise à jour
    """
    baselines = {}
    for task_type in ['ded', 'abd', 'ind']:
        for role in ['propose', 'solve']:
            key = f"{task_type}_{role}"
            baselines[key] = calculate_baseline(rewards, task_types, roles, task_type, role)

    advantages = []
    for i, (reward, task_type, role) in enumerate(zip(rewards, task_types, roles)):
        baseline = baselines[f"{task_type}_{role}"]
        advantage = reward - baseline
        advantages.append(advantage)

    return advantages
```

## 🧪 **SECTION 4.1 : EXPERIMENT SETUP - CONFIGURATION COMPLÈTE**

### **📋 TRAINING DETAILS**

#### **🔧 Hyperparamètres d'Entraînement**
```
Configuration AZR Standard :
- Batch size B = 64 (Couples task-role)
- Optimiseur : AdamW
- Learning rate : [voir Table 5 pour détails]
- Iterations T : Variable selon modèle
- References K = 3 (exemples de référence)
```

#### **🎯 Modèles Testés**
```
Modèles de base :
- Qwen2.5-7B base
- Qwen2.5-7B-Coder
- Qwen2.5-14B
- Llama-3.1-8B

Résultats :
- Absolute Zero Reasoner-base-7B
- Absolute Zero Reasoner-Coder-7B
```

### **📊 EVALUATION PROTOCOL**

#### **🔍 Division des Datasets**
```
In-Distribution (ID) vs Out-of-Distribution (OOD) :
- ID : Données similaires à l'entraînement
- OOD : Généralisation sur nouvelles catégories
```

#### **🏆 Benchmarks Utilisés**

##### **📝 Coding Tasks**
```
- EvalPlus (Liu et al., 2023) : HumanEval+ et MBPP+
- LiveCodeBench (Jain et al., 2024) : Génération v1.5, Mai 23-Fév 24
```

##### **🧮 Mathematical Reasoning**
```
Benchmarks standards :
- AIME-24, AIME-25 : Compétitions mathématiques
- OlympiadBench (He et al., 2024) : Problèmes olympiques
- Minerva, Math500 (Hendrycks et al., 2021) : Raisonnement mathématique
- AMC-23 : American Mathematics Competitions
```

##### **🔬 ID Benchmarks**
```
- CruxEval (Input) : Compréhension de code
- CruxEval (Output) : Génération de sortie
- LiveCodeBench-Execution : Exécution temps réel
```

#### **⚙️ Procédure d'Évaluation**
```python
def evaluation_protocol():
    """
    Protocole d'évaluation AZR complet
    """
    # 1. Greedy decoding pour tous les baselines et résultats AZR
    decoding_method = "greedy"

    # 2. Division datasets
    datasets = {
        'id': ['CruxEval-I', 'CruxEval-O', 'LiveCodeBench-Execution'],
        'ood': ['HumanEval+', 'MBPP+', 'AIME-24', 'AIME-25', 'OlympiadBench']
    }

    # 3. Évaluation reproductible
    reproducibility = "greedy_decoding_ensures_reproducibility"

    return {
        'method': decoding_method,
        'datasets': datasets,
        'reproducibility': reproducibility
    }
```

### **🔬 TRAINING DETAILS COMPLETS**

#### **📈 Buffer Initialization**
```python
def initialize_buffers():
    """
    Initialisation des buffers selon Section 3.3.1
    """
    # Triplet identité pour tous les buffers
    identity_triplet = {
        'program': 'def f(x): return x',
        'input': 'Hello World',
        'output': 'Hello World'
    }

    P_ded = [identity_triplet]
    P_abd = [identity_triplet]
    P_ind = [identity_triplet]

    return P_ded, P_abd, P_ind
```

#### **⚡ Optimisation AdamW**
```python
def setup_optimizer(model_parameters):
    """
    Configuration optimiseur AdamW pour AZR
    """
    optimizer = torch.optim.AdamW(
        model_parameters,
        lr=learning_rate,  # Voir Table 5
        betas=(0.9, 0.999),
        eps=1e-8,
        weight_decay=0.01
    )

    return optimizer
```

#### **🎯 Batch Construction**
```python
def construct_training_batch(B=64):
    """
    Construction batch d'entraînement AZR
    """
    batch = []

    # Répartition équilibrée des tâches
    tasks_per_type = B // 6  # 6 combinaisons (task, role)

    for task_type in ['ded', 'abd', 'ind']:
        for role in ['propose', 'solve']:
            for _ in range(tasks_per_type):
                task_instance = sample_task(task_type, role)
                batch.append(task_instance)

    return batch
```

## 🎯 **FIGURE 7 : EXEMPLE CONCRET DE TÂCHE D'ABDUCTION**

### **💻 Code de Tâche Proposée par le Modèle**
```python
def f(numbers: list[int], target: int) -> int:
    accumulated_values = []
    current_sum = 0
    for number in numbers:
        current_sum += number
        accumulated_values.append(current_sum)

    result = 0
    for i in range(1, len(accumulated_values)):
        if accumulated_values[i] == target:
            result = accumulated_values[i]
    return result
```

**Input donné :** `[1, 2, 3, 4, 5], 5`
**Output attendu :** `1`
**Type :** Abduction (trouver l'input qui produit cet output)

### **🧠 Processus de Raisonnement Multi-Étapes**

#### **Étape 1 : Analyse du Code**
> *"Let's analyze this code snippet... To find one possible input that produces this output, we need to find a scenario where..."*

#### **Étape 2 : Décomposition Logique**
```python
def reasoning_process():
    """
    Processus de raisonnement observé dans Figure 7
    """
    steps = [
        "1. Analyse du code et compréhension de la logique",
        "2. Calcul des sommes cumulatives step-by-step",
        "3. Test d'hypothèses multiples avec validation",
        "4. Auto-correction quand résultat incorrect",
        "5. Exploration de scénarios plus complexes",
        "6. Validation finale par exécution"
    ]
    return steps
```

#### **🔄 Pattern Cognitif Émergent**
- **State-tracking behavior** : Suivi d'état sophistiqué
- **Trial-and-error reasoning** : Raisonnement par essai-erreur
- **Self-correction** : Auto-correction lors d'incohérences
- **Multi-hypothesis testing** : Test de plusieurs hypothèses

## 🧪 **RESEARCH QUESTIONS 6-7 : ÉTUDES D'ABLATION**

### **RQ6 : Importance des Types de Tâches**

#### **🔬 Protocole d'Ablation**
```python
def task_type_ablation_study():
    """
    Étude d'ablation des types de tâches (RQ6)
    """
    variants = {
        'full_azr': ['deduction', 'abduction', 'induction'],
        'without_induction_abduction': ['deduction'],
        'without_induction': ['deduction', 'abduction']
    }

    results = {
        'without_induction_abduction': 'severe_performance_drop',
        'without_induction': 'significant_performance_drop'
    }

    return variants, results
```

**Découvertes Clés :**
- **Tous les types essentiels** : Suppression = dégradation sévère
- **Induction critique** : Rôle central dans l'apprentissage
- **Synergie nécessaire** : Complémentarité entre les 3 types

### **RQ7 : Composants du Proposer**

#### **🔧 Conditionnement Historique**
```python
def historic_conditioning_ablation():
    """
    Ablation du conditionnement sur triplets historiques (RQ7)
    """
    configurations = {
        'dynamic_conditioning': {
            'method': 'K_historical_triplets',
            'K': 3,
            'performance': 'baseline'
        },
        'fixed_prompt': {
            'method': 'fixed_prompt_only',
            'K': 0,
            'performance': {
                'math_drop': -5.0,  # Points absolus
                'code_drop': -1.0   # Points absolus
            }
        }
    }

    return configurations
```

**Impact Mesuré :**
- **Performance Math** : -5 points absolus sans conditionnement
- **Performance Code** : -1 point absolu sans conditionnement
- **Conclusion** : Conditionnement dynamique essentiel

## 📈 **PATTERNS COGNITIFS ÉMERGENTS**

### **🧠 Token Length Growth by Task Type**

#### **📊 Croissance Différentielle**
```python
def token_growth_patterns():
    """
    Patterns de croissance des tokens selon le type de tâche
    """
    patterns = {
        'abduction': {
            'growth_rate': 'high',
            'reason': 'trial-and-error exploration extensive',
            'pattern': 'exponential with complexity'
        },
        'deduction': {
            'growth_rate': 'moderate',
            'reason': 'step-by-step logical reasoning',
            'pattern': 'linear with reasoning steps'
        },
        'induction': {
            'growth_rate': 'variable',
            'reason': 'pattern complexity dependent',
            'pattern': 'varies with abstraction level'
        }
    }

    return patterns
```

**Observations Clés :**
- **Distinction cognitive claire** : Première observation de croissance différentielle
- **Abduction** : Plus grande augmentation (raisonnement exploratoire)
- **Déduction** : Croissance modérée (logique directe)
- **Induction** : Variable selon complexité du pattern

---

*Documentation technique complète du fonctionnement interne d'AZR*
