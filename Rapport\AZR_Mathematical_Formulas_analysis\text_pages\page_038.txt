🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
MATH REASONING

CODE REASONING

OVERALL PERFORMANCE

50

100

150

200

250

300

350

400

  0.100
  0.150
  0.200
Accuracy

AIME 2024

50

100

150

200

250

300

350

400

  0.050
  0.100
  0.150
  0.200
AIME 2025

50

100

150

200

250

300

350

400

  0.380
  0.400
  0.420
  0.440
Olympiad Bench

50

100

150

200

250

300

350

400

  0.200
  0.250
  0.300
  0.350
  0.400
Accuracy

Minerva

50

100

150

200

250

300

350

400

  0.700
  0.720
  0.740
  0.760
  0.780
  0.800
Math 500

50

100

150

200

250

300

350

400

  0.500
  0.550
  0.600
  0.650
  0.700
AMC 2023

50

100

150

200

250

300

350

400

  0.780
  0.800
  0.820
Accuracy

HumanEval+

50

100

150

200

250

300

350

400

  0.700
  0.710
  0.720
  0.730
  0.740
MBPP+

50

100

150

200

250

300

350

400

  0.340
  0.360
  0.380
  0.400
LiveCodeBench

50

100

150

200

250

300

350

400

  0.340
  0.360
  0.380
  0.400
  0.420
  0.440
Accuracy

Math Average

50

100

150

200

250

300

350

400

  0.610
  0.620
  0.630
  0.640
  0.650
Code Average

50

100

150

200

250

300

350

400

  0.500
  0.520
  0.540
Overall Average

🔗 Figure 31. Absolute Zero Reasoner-Coder-14b OOD Performance Breakdown.
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Figure 31. Absolute Zero Reasoner-Coder-14b OOD Performance Breakdown.
🔗 <think>
🔗 Design an absolutely ludicrous and convoluted Python function that is extremely difficult to
🔗 deduce the output from the input, designed to keep machine learning models such as Snippi
🔗 guessing and your peers puzzling.

📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→

🔗 The aim is to outsmart all these groups of intelligent machines and less intelligent humans. This
🔗 is for the brains behind the future.

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 </think>
🔗 - Absolute Zero Reasoner-Llama3.1-8b @ step 132
🔗 Figure 32. Absolute Zero Reasoner –Llama3.1-8B “Uh-oh Moment.” This example highlights an unexpected and potentially unsafe
🔗 reasoning chain generated by our Absolute Zero Reasoner–Llama3.1-8B model during training. Although our paradigm enables
🔗 reasoning improvements without human-curated data, it may still require oversight due to the risk of emergent undesirable behaviors.
🔗 39
🔗 Figure 32. Absolute Zero Reasoner – Llama3.1-8B “Uh-oh Moment.” This example highlights an unexpected and potentially unsafe
🔗 reasoning chain generated by our Absolute Zero Reasoner–Llama3.1-8B model during training. Although our paradigm enables
🔗 reasoning improvements without human-curated data, it may still require oversight due to the risk of emergent undesirable behaviors.
🔗 38