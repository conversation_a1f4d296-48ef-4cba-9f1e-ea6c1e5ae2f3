🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, W. ACECODER: acing coder RL via automated test-case synthesis. CoRR,
🔗 abs/2502.01718, 2025a. doi: 10.48550/ARXIV.2502.01718. URL https://doi.org/10.48550/arXiv.2502.01718.
🔗 <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, <PERSON><PERSON>-zoo: Investigating and taming zero reinforcement
🔗 learning for open base models in the wild. CoRR, abs/2503.18892, 2025b. doi: 10.48550/ARXIV.2503.18892. URL https:
🔗 //doi.org/10.48550/arXiv.2503.18892.
🔗 <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. 100 days after deepseek-r1: A survey
🔗 on replication studies and more directions for reasoning language models. arXiv preprint arXiv:2505.00551, 2025a.
🔗 <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., and <PERSON>ian, Y. Right question is already half the answer: Fully unsupervised llm reasoning
🔗 incentivization, 2025b. URL https://arxiv.org/abs/2504.05812.
🔗 Zhang, Y. and Yang, Q. A survey on multi-task learning. IEEE transactions on knowledge and data engineering, 34(12):5586–5609,
🔗 2021.
🔗 Zhao, A., Lin, M. G., Li, Y., Liu, Y., and Huang, G.
🔗 A mixture of surprises for unsupervised reinforcement learning.
🔗 In
🔗 Koyejo, S., Mohamed, S., Agarwal, A., Belgrave, D., Cho, K., and Oh, A. (eds.), Advances in Neural Information Pro-
🔗 cessing Systems 35:
🔗 Annual Conference on Neural Information Processing Systems 2022, NeurIPS 2022, New Orleans,
🔗 LA, USA, November 28 - December 9, 2022, 2022.
🔗 URL http://papers.nips.cc/paper_files/paper/2022/hash/
🔗 a7667ee5d545a43d2f0fda98863c260e-Abstract-Conference.html.
🔗 Zhao, A., Huang, D., Xu, Q., Lin, M., Liu, Y., and Huang, G. Expel: LLM agents are experiential learners. In Wooldridge, M. J., Dy,
🔗 J. G., and Natarajan, S. (eds.), Thirty-Eighth AAAI Conference on Artificial Intelligence, AAAI 2024, Thirty-Sixth Conference on
🔗 Innovative Applications of Artificial Intelligence, IAAI 2024, Fourteenth Symposium on Educational Advances in Artificial Intelligence,
🔗 EAAI 2014, February 20-27, 2024, Vancouver, Canada, pp. 19632–19642. AAAI Press, 2024. doi: 10.1609/AAAI.V38I17.29936.
🔗 URL https://doi.org/10.1609/aaai.v38i17.29936.
🔗 Zhao, A., Xu, Q., Lin, M., Wang, S., Liu, Y., Zheng, Z., and Huang, G. Diver-ct: Diversity-enhanced red teaming large language model
🔗 assistants with relaxing constraints. In Walsh, T., Shah, J., and Kolter, Z. (eds.), AAAI-25, Sponsored by the Association for the
🔗 Advancement of Artificial Intelligence, February 25 - March 4, 2025, Philadelphia, PA, USA, pp. 26021–26030. AAAI Press, 2025a.
🔗 doi: 10.1609/AAAI.V39I24.34797. URL https://doi.org/10.1609/aaai.v39i24.34797.
🔗 Zhao, A., Zhu, E., Lu, R., Lin, M., Liu, Y., and Huang, G. Self-referencing agents for unsupervised reinforcement learning. Neural
🔗 Networks, 188:107448, 2025b. doi: 10.1016/J.NEUNET.2025.107448. URL https://doi.org/10.1016/j.neunet.2025.
🔗 107448.
🔗 Zitkovich, B., Yu, T., Xu, S., Xu, P., Xiao, T., Xia, F., Wu, J., Wohlhart, P., Welker, S., Wahid, A., Vuong, Q., Vanhoucke, V., Tran,
🔗 H. T., Soricut, R., Singh, A., Singh, J., Sermanet, P., Sanketi, P. R., Salazar, G., Ryoo, M. S., Reymann, K., Rao, K., Pertsch, K.,
🔗 Mordatch, I., Michalewski, H., Lu, Y., Levine, S., Lee, L., Lee, T. E., Leal, I., Kuang, Y., Kalashnikov, D., Julian, R., Joshi, N. J.,
🔗 Irpan, A., Ichter, B., Hsu, J., Herzog, A., Hausman, K., Gopalakrishnan, K., Fu, C., Florence, P., Finn, C., Dubey, K. A., Driess,
🔗 D., Ding, T., Choromanski, K. M., Chen, X., Chebotar, Y., Carbajal, J., Brown, N., Brohan, A., Arenas, M. G., and Han, K. RT-2:
🔗 vision-language-action models transfer web knowledge to robotic control. In Tan, J., Toussaint, M., and Darvish, K. (eds.), Conference
🔗 on Robot Learning, CoRL 2023, 6-9 November 2023, Atlanta, GA, USA, volume 229 of Proceedings of Machine Learning Research,
🔗 pp. 2165–2183. PMLR, 2023. URL https://proceedings.mlr.press/v229/zitkovich23a.html.
🔗 Zuo, Y., Zhang, K., Qu, S., Sheng, L., Zhu, X., Qi, B., Sun, Y., Cui, G., Ding, N., and Zhou, B. Ttrl: Test-time reinforcement learning,
🔗 2025. URL https://arxiv.org/abs/2504.16084.
🔗 19