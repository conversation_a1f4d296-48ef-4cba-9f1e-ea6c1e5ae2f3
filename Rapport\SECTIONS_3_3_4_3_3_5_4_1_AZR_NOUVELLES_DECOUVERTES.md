# 🔬 SECTIONS 3.3.4, 3.3.5 & 4.1 AZR - NOUVELLES DÉCOUVERTES

## 📋 INFORMATIONS GÉNÉRALES

**Sections analysées :** 3.3.4 (Answer Verification), 3.3.5 (Task-Relative REINFORCE++), 4.1 (Experiment Setup)
**Date d'extraction :** 12 juin 2025
**Source :** Image fournie par l'utilisateur - Page 8 du paper AZR
**Statut :** Informations complémentaires critiques ajoutées

---

## 🔍 **SECTION 3.3.4 : ANSWER VERIFICATION - DÉTAILS COMPLETS**

### **🎯 Vérification par Type de Tâche**

#### **🔴 ABDUCTION TASK VERIFICATION**
```
Équation : p(i_π) = p(i⋆)
```

**Processus :**
1. **Réception** : i_π depuis solver policy
2. **Vérification** : Équivalence via exécution p(i_π) = p(i⋆)
3. **Raison** : p n'est pas nécessairement bijective
4. **⋆** : Fait référence aux informations gold privilégiées

**Implémentation :**
```python
def verify_abduction_answer(program, predicted_input, gold_input):
    """
    Vérification abduction : p(i_π) = p(i⋆)
    """
    try:
        predicted_output = execute_program(program, predicted_input)
        gold_output = execute_program(program, gold_input)
        return predicted_output == gold_output
    except:
        return False
```

#### **🟢 DEDUCTION TASK VERIFICATION**
```
Équation : o_π = o⋆
```

**Processus :**
1. **Réception** : o_π depuis solver policy
2. **Vérification** : Correspondance directe o_π = o⋆
3. **Simplicité** : Comparaison directe des outputs

**Implémentation :**
```python
def verify_deduction_answer(predicted_output, gold_output):
    """
    Vérification déduction : o_π = o⋆
    """
    return predicted_output == gold_output
```

#### **🔵 INDUCTION TASK VERIFICATION**
```
Équation : all({p_π(i⋆_n) = o⋆_n}_N)
```

**Processus :**
1. **Réception** : p_π depuis solver policy
2. **Vérification** : Tous les exemples I/O doivent être satisfaits
3. **Complexité** : Validation sur ensemble complet d'exemples
4. **Référence** : Voir Figures 10-12 pour implémentation code

**Implémentation :**
```python
def verify_induction_answer(predicted_program, io_examples):
    """
    Vérification induction : all({p_π(i⋆_n) = o⋆_n}_N)
    """
    for input_val, expected_output in io_examples:
        try:
            actual_output = execute_program(predicted_program, input_val)
            if actual_output != expected_output:
                return False
        except:
            return False
    return True
```

---

## ⚡ **SECTION 3.3.5 : TASK-RELATIVE REINFORCE++ (TRR++)**

### **🎯 Innovation Algorithmique Majeure**

#### **📊 ÉQUATION (8) : AVANTAGE NORMALISÉ**
```
A^task = (r - μ_task,role) / σ_task,role
```

**Où :**
- **r** : Récompense obtenue pour cette instance
- **μ_task,role** : Moyenne des récompenses pour (task, role)
- **σ_task,role** : Écart-type des récompenses pour (task, role)
- **task** ∈ {ded, abd, ind}
- **role** ∈ {propose, solve}

#### **🔧 6 BASELINES SÉPARÉES**

**Innovation clé :** Au lieu d'une baseline globale, TRR++ maintient **6 baselines distinctes** :

```
1. μ_ded,propose & σ_ded,propose    (Déduction - Proposition)
2. μ_ded,solve & σ_ded,solve        (Déduction - Résolution)
3. μ_abd,propose & σ_abd,propose    (Abduction - Proposition)
4. μ_abd,solve & σ_abd,solve        (Abduction - Résolution)
5. μ_ind,propose & σ_ind,propose    (Induction - Proposition)
6. μ_ind,solve & σ_ind,solve        (Induction - Résolution)
```

#### **🎯 Contexte Multitâche**
> *"Since AZR trains the combination of roles and task types, it operates in a multitask reinforcement learning setup"*

**Avantages TRR++ :**
- **Réduction de variance** optimale par contexte
- **Normalisation spécialisée** pour chaque (task, role)
- **Stabilité d'entraînement** améliorée
- **Convergence plus rapide** sur tâches complexes

#### **💻 Implémentation TRR++**
```python
class TaskRelativeREINFORCEPlusPlus:
    def __init__(self):
        # 6 baselines séparées
        self.baselines = {}
        for task in ['ded', 'abd', 'ind']:
            for role in ['propose', 'solve']:
                key = f"{task}_{role}"
                self.baselines[key] = {
                    'rewards': [],
                    'mean': 0.0,
                    'std': 1.0
                }
    
    def compute_advantage(self, reward, task_type, role):
        """
        Calcul avantage selon équation (8)
        """
        key = f"{task_type}_{role}"
        baseline = self.baselines[key]
        
        # Avantage normalisé
        advantage = (reward - baseline['mean']) / (baseline['std'] + 1e-8)
        
        # Mise à jour baseline
        baseline['rewards'].append(reward)
        baseline['mean'] = np.mean(baseline['rewards'][-100:])  # Fenêtre glissante
        baseline['std'] = np.std(baseline['rewards'][-100:])
        
        return advantage
```

---

## 🧪 **SECTION 4.1 : EXPERIMENT SETUP - CONFIGURATION COMPLÈTE**

### **📋 TRAINING DETAILS**

#### **🔧 Hyperparamètres Clés**
```
Configuration Standard AZR :
- Batch size B = 64
- Optimiseur : AdamW  
- Learning rate : [Table 5]
- References K = 3
- Iterations T : Variable
```

#### **🎯 Modèles de Base Testés**
```
Modèles évalués :
- Qwen2.5-7B base → Absolute Zero Reasoner-base-7B
- Qwen2.5-7B-Coder → Absolute Zero Reasoner-Coder-7B
- Qwen2.5-14B
- Llama-3.1-8B
```

### **📊 EVALUATION PROTOCOL**

#### **🔍 Division ID/OOD**
```
In-Distribution (ID) :
- Données similaires à l'entraînement
- Validation de l'apprentissage effectif

Out-of-Distribution (OOD) :
- Généralisation sur nouvelles catégories
- Test de robustesse du modèle
```

#### **🏆 Benchmarks Détaillés**

##### **💻 Coding Tasks**
```
- EvalPlus : HumanEval+ et MBPP+ benchmarks
- LiveCodeBench : Génération v1.5 (Mai 23 - Fév 24)
```

##### **🧮 Mathematical Reasoning**
```
- AIME-24, AIME-25 : American Invitational Mathematics Examination
- OlympiadBench : Problèmes de niveau olympique
- Minerva, Math500 : Raisonnement mathématique standard
- AMC-23 : American Mathematics Competitions
```

##### **🔬 ID Benchmarks Spécifiques**
```
- CruxEval (Input) : Compréhension de code
- CruxEval (Output) : Génération de sortie
- LiveCodeBench-Execution : Exécution temps réel
```

#### **⚙️ Protocole d'Évaluation**
```
Méthode : Greedy decoding
Raison : Assure la reproductibilité
Application : Tous baselines et résultats AZR
```

### **🔬 Configuration Technique Complète**

#### **📈 Initialisation des Buffers**
```python
def init_seeding():
    """
    Initialisation selon Section 3.3.1
    """
    identity_triplet = ('def f(x): return x', 'Hello World', 'Hello World')
    
    P_ded = [identity_triplet]
    P_abd = [identity_triplet]
    P_ind = [identity_triplet]
    
    return P_ded, P_abd, P_ind
```

#### **⚡ Configuration AdamW**
```python
optimizer = torch.optim.AdamW(
    model.parameters(),
    lr=learning_rate,  # Voir Table 5 du paper
    betas=(0.9, 0.999),
    eps=1e-8,
    weight_decay=0.01
)
```

#### **🎯 Construction des Batches**
```python
def construct_batch(B=64):
    """
    Batch équilibré pour 6 combinaisons (task, role)
    """
    batch_size_per_combination = B // 6
    
    batch = []
    for task in ['ded', 'abd', 'ind']:
        for role in ['propose', 'solve']:
            for _ in range(batch_size_per_combination):
                instance = sample_task_role(task, role)
                batch.append(instance)
    
    return batch
```

---

## 🎯 **INNOVATIONS TECHNIQUES RÉVÉLÉES**

### **🔬 Answer Verification Sophistiquée**
- **Vérification spécialisée** par type de tâche
- **Gestion de la non-bijectivité** pour abduction
- **Validation exhaustive** pour induction

### **⚡ Task-Relative REINFORCE++ (TRR++)**
- **6 baselines séparées** pour réduction variance optimale
- **Normalisation contextuelle** par (task, role)
- **Stabilité d'entraînement** multitâche améliorée

### **🧪 Protocole Expérimental Rigoureux**
- **Division ID/OOD** pour validation généralisation
- **Benchmarks diversifiés** coding + mathematical reasoning
- **Reproductibilité** via greedy decoding

### **🔧 Configuration Technique Optimisée**
- **Batch size B=64** équilibré sur 6 combinaisons
- **AdamW optimizer** avec hyperparamètres optimaux
- **Buffer initialization** avec triplet identité

---

*Nouvelles découvertes critiques des sections 3.3.4, 3.3.5 & 4.1 - Compléments essentiels*
