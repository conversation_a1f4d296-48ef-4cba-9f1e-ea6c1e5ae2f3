🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 References
🔗 <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON> To code, or not
🔗 to code? exploring impact of code in pre-training. CoRR, abs/2408.10914, 2024. doi: 10.48550/ARXIV.2408.10914. URL
🔗 https://doi.org/10.48550/arXiv.2408.10914.
🔗 <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,
🔗 <PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>. <PERSON>-to-strong generalization: Eliciting strong capabilities with weak supervision. In Forty-first
🔗 International Conference on Machine Learning, ICML 2024, Vienna, Austria, July 21-27, 2024. OpenReview.net, 2024. URL
🔗 https://openreview.net/forum?id=ghNRg2mEgN.
🔗 Canal, M. Radon: Python tool for code metrics. https://github.com/rubik/radon, 2023. Accessed: 2025-04-06.
🔗 <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>., <PERSON>, <PERSON>., <PERSON>, <PERSON>., <PERSON>, <PERSON>., <PERSON>, <PERSON>., and <PERSON>, K.-Y. K. Spc: Evolving self-play critic via advers<PERSON>l games
🔗 for llm reasoning, 2025. <PERSON>RL https://arxiv.org/abs/2504.19162.
🔗 Chen, Z., Deng, <PERSON>., <PERSON>, H., Ji, K., and Gu, Q. Self-play fine-tuning converts weak language models to strong language models. In
🔗 Forty-first International Conference on Machine Learning, ICML 2024, Vienna, Austria, July 21-27, 2024. OpenReview.net, 2024.
🔗 URL https://openreview.net/forum?id=O4cHTxW9BS.
🔗 Cheng, P., Hu, T., Xu, H., Zhang, Z., Dai, Y., Han, L., Du, N., and Li, X. Self-playing adversarial language game enhances LLM
🔗 reasoning. In Globersons, A., Mackey, L., Belgrave, D., Fan, A., Paquet, U., Tomczak, J. M., and Zhang, C. (eds.), Advances in
🔗 Neural Information Processing Systems 38: Annual Conference on Neural Information Processing Systems 2024, NeurIPS 2024,
🔗 Vancouver, BC, Canada, December 10 - 15, 2024, 2024. URL http://papers.nips.cc/paper_files/paper/2024/hash/
🔗 e4be7e9867ef163563f4a5e90cec478f-Abstract-Conference.html.
🔗 Christiano,
🔗 P.
🔗 Approval-directed
🔗 bootstrapping.
🔗 https://www.alignmentforum.org/posts/6x7oExXi32ot6HjJv/
🔗 approval-directed-bootstrapping, 2018. AI Alignment Forum.
🔗 Christiano,
🔗 P.
🔗 Capability
🔗 amplification.
🔗 https://www.alignmentforum.org/posts/t3AJW5jP3sk36aGoC/
🔗 capability-amplification-1, 2019. AI Alignment Forum.
🔗 Cui, G., Yuan, L., Wang, Z., Wang, H., Li, W., He, B., Fan, Y., Yu, T., Xu, Q., Chen, W., Yuan, J., Chen, H., Zhang, K., Lv, X., Wang, S.,
🔗 Yao, Y., Han, X., Peng, H., Cheng, Y., Liu, Z., Sun, M., Zhou, B., and Ding, N. Process reinforcement through implicit rewards.
🔗 CoRR, abs/2502.01456, 2025. doi: 10.48550/ARXIV.2502.01456. URL https://doi.org/10.48550/arXiv.2502.01456.
🔗 DeepSeek-AI, Guo, D., Yang, D., Zhang, H., Song, J., Zhang, R., Xu, R., Zhu, Q., Ma, S., Wang, P., Bi, X., Zhang, X., Yu, X., Wu, Y.,
🔗 Wu, Z. F., Gou, Z., Shao, Z., Li, Z., Gao, Z., Liu, A., Xue, B., Wang, B., Wu, B., Feng, B., Lu, C., Zhao, C., Deng, C., Zhang, C.,
🔗 Ruan, C., Dai, D., Chen, D., Ji, D., Li, E., Lin, F., Dai, F., Luo, F., Hao, G., Chen, G., Li, G., Zhang, H., Bao, H., Xu, H., Wang, H.,
🔗 Ding, H., Xin, H., Gao, H., Qu, H., Li, H., Guo, J., Li, J., Wang, J., Chen, J., Yuan, J., Qiu, J., Li, J., Cai, J. L., Ni, J., Liang, J., Chen,
🔗 J., Dong, K., Hu, K., Gao, K., Guan, K., Huang, K., Yu, K., Wang, L., Zhang, L., Zhao, L., Wang, L., Zhang, L., Xu, L., Xia, L.,
🔗 Zhang, M., Zhang, M., Tang, M., Li, M., Wang, M., Li, M., Tian, N., Huang, P., Zhang, P., Wang, Q., Chen, Q., Du, Q., Ge, R.,
🔗 Zhang, R., Pan, R., Wang, R., Chen, R. J., Jin, R. L., Chen, R., Lu, S., Zhou, S., Chen, S., Ye, S., Wang, S., Yu, S., Zhou, S., Pan, S.,
🔗 and Li, S. S. Deepseek-r1: Incentivizing reasoning capability in llms via reinforcement learning. CoRR, abs/2501.12948, 2025. doi:
🔗 10.48550/ARXIV.2501.12948. URL https://doi.org/10.48550/arXiv.2501.12948.
🔗 Demski, A. and Garrabrant, S. Embedded agency. CoRR, abs/1902.09469, 2019. URL http://arxiv.org/abs/1902.09469.
🔗 Dennis, M., Jaques, N., Vinitsky, E., Bayen, A. M., Russell, S., Critch, A., and Levine, S.
🔗 Emergent complexity and zero-
🔗 shot transfer via unsupervised environment design.
🔗 In Larochelle, H., Ranzato, M., Hadsell, R., Balcan, M., and Lin, H.
🔗 (eds.), Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems
🔗 2020, NeurIPS 2020, December 6-12, 2020, virtual, 2020. URL https://proceedings.neurips.cc/paper/2020/hash/
🔗 985e9a46e10005356bbaf194249f6856-Abstract.html.
🔗 Dubey, A., Jauhri, A., Pandey, A., Kadian, A., Al-Dahle, A., Letman, A., Mathur, A., Schelten, A., Yang, A., Fan, A., Goyal, A.,
🔗 Hartshorn, A., Yang, A., Mitra, A., Sravankumar, A., Korenev, A., Hinsvark, A., Rao, A., Zhang, A., Rodriguez, A., Gregerson, A.,
🔗 Spataru, A., Rozière, B., Biron, B., Tang, B., Chern, B., Caucheteux, C., Nayak, C., Bi, C., Marra, C., McConnell, C., Keller, C.,
🔗 Touret, C., Wu, C., Wong, C., Ferrer, C. C., Nikolaidis, C., Allonsius, D., Song, D., Pintz, D., Livshits, D., Esiobu, D., Choudhary,
🔗 D., Mahajan, D., Garcia-Olano, D., Perino, D., Hupkes, D., Lakomkin, E., AlBadawy, E., Lobanova, E., Dinan, E., Smith, E. M.,
🔗 Radenovic, F., Zhang, F., Synnaeve, G., Lee, G., Anderson, G. L., Nail, G., Mialon, G., Pang, G., Cucurell, G., Nguyen, H., Korevaar,
🔗 H., Xu, H., Touvron, H., Zarov, I., Ibarra, I. A., Kloumann, I. M., Misra, I., Evtimov, I., Copet, J., Lee, J., Geffert, J., Vranes, J.,
🔗 Park, J., Mahadeokar, J., Shah, J., van der Linde, J., Billock, J., Hong, J., Lee, J., Fu, J., Chi, J., Huang, J., Liu, J., Wang, J., Yu,
🔗 J., Bitton, J., Spisak, J., Park, J., Rocca, J., Johnstun, J., Saxe, J., Jia, J., Alwala, K. V., Upasani, K., Plawiak, K., Li, K., Heafield,
🔗 14