🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 complexity measure (<PERSON>, 1977) as intrinsic rewards. Concretely, we used the complexipy and Radon packages (<PERSON>, 2025;
🔗 Canal, 2023) to implement the respective metrics. These are then served as intrinsic rewards during the AZR self-play phase.
🔗 Diversity Rewards.
🔗 We also attempted using diversity rewards to . Inspired by DiveR-CT (<PERSON> et al., 2025a), we incorporate
🔗 code edit distance as an intrinsic reward. Specifically, we treat the reference programs shown in the prompt as anchors and compute the
🔗 average code edit distance between the generated program and these anchors. This serves as a measure of diversity in the generated
🔗 output. Additionally, we explored another diversity-based reward inspired by the notion of surprise (<PERSON> et al., 2022). In this approach,
🔗 we construct a probability distribution over previously encountered input/output pairs that the solver has answered. The reward is then
🔗 defined as 1 −p(input/output), where p denotes the empirical probability of a particular input or output. While both strategies were
🔗 evaluated in our experiments, we did not observe a significant difference in performance. However, we believe this aspect warrants
🔗 deeper investigation, as diversity rewards remain a promising avenue for strengthening AZR further.
🔗 Reward Aggregation.
🔗 We tested several ways on how to combine rewards for the proposer and discriminator. First, we separate

📐 FORMULE MATHÉMATIQUE:
    the reward into extrinsic reward rextrinsic and a set of intrinsic reward(s) I = {ri}, and tested the following strategies to combine them

🔗 into a single reward,

📐 FORMULE MATHÉMATIQUE:
    r = rextrinsic +


📐 FORMULE MATHÉMATIQUE:
    |I|


📐 FORMULE MATHÉMATIQUE:
    X


📐 FORMULE MATHÉMATIQUE:
    i


📐 FORMULE MATHÉMATIQUE:
    ri,

🔗 (11)

📐 FORMULE MATHÉMATIQUE:
    r = rextrinsic ·


📐 FORMULE MATHÉMATIQUE:
    |I|


📐 FORMULE MATHÉMATIQUE:
    X


📐 FORMULE MATHÉMATIQUE:
    i


📐 FORMULE MATHÉMATIQUE:
    ri,

🔗 (12)

📐 FORMULE MATHÉMATIQUE:
    r = rextrinsic ·


📐 FORMULE MATHÉMATIQUE:
    |I|


📐 FORMULE MATHÉMATIQUE:
    Y


📐 FORMULE MATHÉMATIQUE:
    i


📐 FORMULE MATHÉMATIQUE:
    ri,

🔗 (13)

📐 FORMULE MATHÉMATIQUE:
    r = rextrinsic +


📐 FORMULE MATHÉMATIQUE:
    |I|


📐 FORMULE MATHÉMATIQUE:
    Y


📐 FORMULE MATHÉMATIQUE:
    i


📐 FORMULE MATHÉMATIQUE:
    ri.

🔗 (14)
🔗 We found that the simple additive way of combining rewards, a.k.a Equation (11), produced the most stable runs, possibly due to less
🔗 variance.
🔗 D.5. Environment Transition
🔗 We investigated how the transition function in our coding environment for the proposer. Specifically, after generating a piece of code, we
🔗 can apply a transformation function on it before giving it making it an valid tuple in our dataset. We investigated two
🔗 Removing Comments and Docstrings
🔗 In early iterations of our experiments, we noticed that comments and docstrings
🔗 were sometimes used to explicitly outline what the function was doing, or even served as a partial “note-taking” interleaved “ReAct”
🔗 process (Yao et al., 2023) of generating code—that is, the model could interleave think and action at the same time, and to make the
🔗 generated code valid, it used comments to encase its thoughts (Appendix C.3), similarly observed in DeepSeek-Prover-V2: (Ren et al.,
🔗 2025). We then thought that to make the task harder for the solver, we should occlude this information from it. However, we observed
🔗 a significant performance drop after removing all comments and docstrings. One explanation for this phenomenon is that the only
🔗 “communication” channel between the proposer and the solver is restricted to the code itself, rather than some kind of “message” along
🔗 with the code. These messages can potentially provide hints to the solver, thus making some otherwise impossible tasks solvable. As a
🔗 result, the solver is able to learn from its experience and self-bootstrap out of certain unsolvable tasks.
🔗 Removing Global Variables.
🔗 We observed that some programs contain globally declared variables that may inadvertently leak
🔗 information about the correct answer—this issue is particularly prevalent in the input induction task generation and solving. Initially, we
🔗 were concerned that such leakage might lead to wasted computation on trivial or compromised examples. To address this, we developed
🔗 a systematic procedure to remove globally declared variables from the generated programs.
🔗 However, after applying this cleaning step, we observed a noticeable drop in performance on our self-play reasoning tasks. One possible
🔗 explanation is that the generation step is unaware of this post-processing modification; since the reward is assigned after the transition
🔗 function (which includes variable removal), the model may not learn effectively from this mismatch.
🔗 Moreover, we believe that even when answers are present, the solver still engages in nontrivial reasoning to reach a solution, potentially
🔗 benefiting from this exposure. This aligns with the idea of rationalization as proposed in STaR (Zelikman et al., 2022), where the model
🔗 pretends to not see the answer but still performs reasoning during learning. Therefore, in our final experiments, we choose not to remove
🔗 globally declared variables, allowing the self-play loop to naturally incorporate and adapt to such cases.
🔗 50