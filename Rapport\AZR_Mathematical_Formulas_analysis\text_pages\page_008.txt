🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data

📐 FORMULE MATHÉMATIQUE:
    ∀p ∈Pdeterministic, ∀i ∈I ,


📐 FORMULE MATHÉMATIQUE:
    

🔗 lim

📐 FORMULE MATHÉMATIQUE:
    j→∞p(i)(1) = p(i)(2) = · · · = p(i)(j)


📐 FORMULE MATHÉMATIQUE:
    


📐 FORMULE MATHÉMATIQUE:
    ,

🔗 (7)
🔗 where (j) indexes repeated independent executions of the program. That is, for all inputs i, the output of p(i) remains identical
🔗 with any independent execution of the program. A valid program/input/output triplet (p, i, o) is defined such that o = p(i), where
🔗 p ∈Pdeterministic.
🔗 Since the output of probabilistic programs can vary on every individual run, it is non-trivial to use verifiable functions to evaluate the
🔗 correctness of an answer. Therefore, to keep the verifier simple, we restrict the valid programs generated by the learner to the class
🔗 of deterministic programs. We believe that stochastic programs can encompass a larger class of behaviors and are important and
🔗 promising to include in future versions of AZR.
🔗 To implement the filtering of invalid probabilistic programs, and following the definition of a deterministic program highlighted in
🔗 Equation (7), we approximate this procedure by independently running the program j finite times and checking that all the outputs
🔗 are equal. For computational budget reasons, we fixed j = 2 for all experiments.
🔗 Solving Task Construction. If a task proposal passes these three checks, we deem it a valid task and apply appropriate procedures to

📐 FORMULE MATHÉMATIQUE:
    present part of the triplet to the solver. Specifically, we set x = (p, i) for deduction; x = (p, o) for abduction; and x = ({in, on}N//2


📐 FORMULE MATHÉMATIQUE:
    n=1 , m)

🔗 for induction, where half of the tests cases and a program description m is used. We use all valid tasks from timestep t; if the batch B is
🔗 not full, we uniformly sample from previously validated tasks to fill the batch.
🔗 3.3.4. Answer Verification

📐 FORMULE MATHÉMATIQUE:
    For abduction task, we receive iπ from the solver policy, then we equivalence match using p(iπ) = p(i⋆), where ∗refers to the


📐 FORMULE MATHÉMATIQUE:
    privileged gold information. The reason we do not just match iπ and i⋆is because p is not necessarily bĳective. For deduction task, we


📐 FORMULE MATHÉMATIQUE:
    match oπ = o⋆. For induction, we match all({pπ(i⋆


📐 FORMULE MATHÉMATIQUE:
    n) = o⋆


📐 FORMULE MATHÉMATIQUE:
    n}N). This part might be convoluted to explain in language, therefore we

🔗 recommend the reader to see how we did abduction, deduction and induction verification in code in Figures 10 to 12, respectively.
🔗 3.3.5. Task-Relative REINFORCE++
🔗 Since AZR trains the combination of roles and task types, it operates in a multitask reinforcement learning setup (Zhang & Yang, 2021;
🔗 Zhao et al., 2022; Wang et al., 2023; Yue et al., 2023). Instead of computing a single global baseline as in REINFORCE++ (Hu, 2025)
🔗 (Appendix A), we compute separate baselines for each of the six task-role configurations. This can be viewed as an interpolation between
🔗 per-question baselines, as in GRPO (Shao et al., 2024), and a global baseline, allowing for more structured variance reduction tailored to
🔗 each task setup. We refer to this variant as Task-Relative REINFORCE++ (TRR++). The normalized advantage Anorm is computed as:
🔗 Anorm

📐 FORMULE MATHÉMATIQUE:
    task,role = r −µtask,role

🔗 σtask,role

📐 FORMULE MATHÉMATIQUE:
    ,


📐 FORMULE MATHÉMATIQUE:
    task ∈{ind,ded,abd}, role ∈{propose,solve},

🔗 (8)
🔗 where the mean and standard deviation are computed within each task type and role, yielding six baselines.
🔗 4. Experiments
🔗 4.1. Experiment Setup
🔗 Training Details.
🔗 For all experiments, we initialize the buffers as described in Section 3.1. AZR models are trained using a batch
🔗 size of 64 × 6 (2 roles × 3 task types). We use constant learning rate= 1e−6 and the AdamW optimizer (Loshchilov & Hutter, 2019).
🔗 Complete list of hyperparameters is provided in Table 3.
🔗 For the main experiments,
🔗 we train AZR models on Qwen2.5-7B and Qwen2.5-7B-Coder,
🔗 resulting in Absolute
🔗 Zero Reasoner-base-7B and Absolute Zero Reasoner-Coder-7B, respectively.
🔗 Additional experiments include training
🔗 Qwen2.5-Coder-3B, Qwen2.5-Coder-14B, Qwen2.5-14B, Llama-3.1-8B (Yang et al., 2024a; Hui et al., 2024; Dubey et al.,
🔗 2024).
🔗 Evaluation Protocol.
🔗 To evaluate our models, we divide the datasets into in-distribution (ID) and out-of-distribution (OOD)
🔗 categories. For OOD benchmarks, which we emphasize more, we further categorize them into coding and mathematical reasoning
🔗 benchmarks. For coding tasks, we evaluate using Evalplus (Liu et al., 2023) on the HumanEval+ and MBPP+ benchmarks, as
🔗 well as LiveCodeBench Generation (v1-5, May 23-Feb 25) (Jain et al., 2024). For mathematical reasoning, we utilize six standard
🔗 benchmarks commonly used in recent zero-shot trained reasoners: AIME’24, AIME’25, OlympiadBench (He et al., 2024), Minerva,
🔗 Math500 (Hendrycks et al., 2021), and AMC’23. For ID benchmarks, we use CruxEval-I(nput), CruxEval-O(utput), and LiveCodeBench-
🔗 Execution (Gu et al., 2024; Jain et al., 2024), which assess reasoning capabilities regarding the input and output of programs (Li et al.,
🔗 2025). Greedy decoding is used for all baseline methods and AZR results to ensure reproducibility.
🔗 8