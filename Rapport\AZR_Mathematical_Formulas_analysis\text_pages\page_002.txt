🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
Reinforcement Learning with Verifiable Rewards

Absolute Zero (Ours)

Supervised Learning

Less Human Supervision

🔗 Figure 2. Absolute Zero Paradigm. Supervised learning relies on human-curated reasoning traces for behavior cloning. Reinforcement
🔗 learning from verified rewards, enables agents to self-learn reasoning, but still depends on expert-defined learning distribution and a
🔗 respective set of curated QA pairs, demanding domain expertise and manual effort. In contrast, we introduce a new paradigm, Absolute
🔗 Zero, for training reasoning models without any human-curated data. We envision that the agent should autonomously propose tasks
🔗 optimized for learnability and learn how to solve them using an unified model. The agent learns by interacting with an environment that
🔗 provides verifiable feedback, enabling reliable and continuous self-improvement entirely without human intervention.
🔗 1. Introduction
🔗 Large language models (LLMs) have recently achieved remarkable improvements in reasoning capabilities by employing Reinforcement
🔗 Learning with Verifiable Rewards (RLVR) (<PERSON> et al., 2024). Unlike methods that explicitly imitate intermediate reasoning steps,
🔗 RLVR uses only outcome-based feedback, enabling large-scale reinforcement learning over vast task datasets (DeepSeek-AI et al., 2025;
🔗 Team et al., 2025; <PERSON><PERSON><PERSON> et al., 2024; OpenAI, 2025b;a). A particularly compelling variant is the “zero” RLVR paradigm (DeepSeek-AI
🔗 et al., 2025), which forgoes any cold-start distillation data, using neither human-generated nor AI-generated reasoning traces, and applies
🔗 RLVR directly on the base model with task rewards. However, these methods still depend heavily on expertly curated distributions of
🔗 reasoning question–answer pairs, which raises serious concerns about their long-term scalability (Villalobos et al., 2024). As reasoning
🔗 models continue to advance, the effort required to construct large-scale, high-quality datasets may soon become unsustainable (Yue
🔗 et al., 2025). A similar scalability bottleneck has already been identified in the domain of LLM pretraining (Sutskever et al., 2024).
🔗 Furthermore, as AI systems continue to evolve and potentially exceed human intellect, an exclusive dependence on human-designed
🔗 tasks risks imposing constraints on their capacity for autonomous learning and growth (Hughes et al., 2024). This underscores the need
🔗 for a new paradigm that begins to explore possibilities beyond the constraints of human-designed tasks and prepares for a future in which
🔗 AI systems may surpass human intelligence.
🔗 To this end, we propose “Absolute Zero”, a new paradigm for reasoning models in which the model simultaneously learns to define tasks
🔗 that maximize learnability and to solve them effectively, enabling self-evolution through self-play without relying on external data. In
🔗 contrast to prior self-play methods that are limited to narrow domains, fixed functionalities, or learned reward models that are prone to
🔗 hacking (Silver et al., 2017; Chen et al., 2025; 2024), the Absolute Zero paradigm is designed to operate in open-ended settings while
🔗 remaining grounded in a real environment. It relies on feedback from the environment as a verifiable source of reward, mirroring how
🔗 humans learn and reason through interaction with the world, and helps prevent issues such as hacking with neural reward models (Hughes
🔗 et al., 2024). Similar to AlphaZero (Silver et al., 2017), which improves through self-play, our proposed paradigm requires no human
🔗 supervision and learns entirely through self-interaction. We believe the Absolute Zero paradigm represents a promising step toward
🔗 enabling large language models to autonomously achieve superhuman reasoning capabilities.
🔗 Building on this new reasoning paradigm, we introduce the Absolute Zero Reasoner (AZR), which proposes and solves coding tasks. We
🔗 cast code executor as an open-ended yet grounded environment, sufficient to both validate task integrity and also provide verifiable
🔗 feedback for stable training. We let AZR construct three types of coding tasks: infer and reason about one particular element in a
🔗 program, input, output triplet, which corresponds to three complementary modes of reasoning: induction, abduction, and deduction. We
🔗 train the entire system end-to-end with a newly proposed reinforcement learning advantage estimator tailored to the multitask nature of
🔗 the proposed approach.
🔗 Despite being trained entirely without any in-distribution data, AZR demonstrates remarkable capabilities across diverse reasoning tasks
🔗 in math and coding. In mathematics, AZR achieves competitive performance compared to zero reasoner models explicitly fine-tuned
🔗 with domain-specific supervision. In coding tasks, AZR establishes a new state-of-the-art performance, surpassing models specifically
🔗 trained with code datasets using RLVR. Furthermore, AZR outperforms all previous models by an average of 1.8 absolute points
🔗 2