# 🔧 ALGORITHME 1 AZR - ANALYSE COMPLÈTE ET DÉTAILLÉE

## 📋 INFORMATIONS GÉNÉRALES

**Algorithme analysé :** Algorithm 1: Self-Play Training of Absolute Zero Reasoner (AZR)
**Date d'extraction :** 12 juin 2025
**Source :** Image fournie par l'utilisateur - Page 7 du paper AZR
**Statut :** Informations complémentaires aux fichiers existants (détails manquants ajoutés)

---

## 🎯 **ALGORITHME 1 COMPLET - LIGNE PAR LIGNE**

### **📋 PARAMÈTRES D'ENTRÉE**
```
Require: 
- Pretrained base LLM π_θ (modèle de base pré-entraîné)
- Batch size B (taille du batch)
- #references K (nombre d'exemples de référence)
- Iterations T (nombre d'itérations d'entraînement)
```

### **🚀 PHASE D'INITIALISATION**
```python
1: P_ded, P_abd, P_ind ← INITSEEDING(π_θ)     # voir §3.3.1
2: for t ← 1 to T do                          # Boucle d'entraînement principale
```

**Explication Ligne 1 :**
- Initialisation des 3 buffers spécialisés
- P_ded : Buffer pour tâches de déduction
- P_abd : Buffer pour tâches d'abduction  
- P_ind : Buffer pour tâches d'induction
- INITSEEDING utilise le triplet identité "Hello World"

---

## 🔴 **PROPOSE PHASE (Lignes 3-12)**

### **🌱 INDUCTION TASK PROPOSAL (Lignes 3-7)**
```python
3:   for b ← 1 to B do                        # ▷ PROPOSE PHASE
4:     p ∼ P_ind ∪ P_ded                      # ▷ sample a program for induction task proposal
5:     {i^n}_{n=1}, m_x ← π_θ^propose(ind, p)  # ▷ generate N inputs and a description
6:     {(i^n, o^n)} ← VALIDATEBYEXECUTING(p, {i^n}, SYNTAX) then  # ▷ validate I/Os, see §3.3.3
7:       P_ind ← P_ind ∪ {(p, {(i^n, o^n)}, m_x)}  # ▷ update induction buffer
```

**Détails Lignes 3-7 :**
- **Ligne 4** : Échantillonnage programme depuis union des buffers induction et déduction
- **Ligne 5** : Génération de N inputs + message descriptif par le proposer
- **Ligne 6** : Validation syntaxique via exécution Python réelle
- **Ligne 7** : Ajout au buffer induction si validation réussie

### **🔍 DEDUCTION & ABDUCTION PROPOSALS (Lignes 8-12)**
```python
8:   for α ∈ {ded, abd} do
9:     (p_k, i_k, o_k)^K_{k=1} ← P_α           # ▷ sample K reference examples
10:    (p_x, i_x) ← π_θ^propose(α, {(p_k, i_k, o_k)})  # ▷ propose new task
11:    if o_x ← VALIDATEBYEXECUTING(p_x, i_x, SYNTAX,SAFETY,DETERMINISM) then  # ▷ see §3.3.3
12:      P_α ← P_α ∪ {(p_x, i_x, o_x)}         # ▷ if valid, update deduction or abduction buffers
```

**Détails Lignes 8-12 :**
- **Ligne 9** : Échantillonnage de K exemples de référence depuis buffer correspondant
- **Ligne 10** : Proposition nouvelle tâche conditionnée sur exemples de référence
- **Ligne 11** : Validation complète (syntaxe + sécurité + déterminisme)
- **Ligne 12** : Mise à jour buffer si validation réussie

---

## 🟢 **SOLVE PHASE (Lignes 13-15)**

### **🎯 RÉSOLUTION POUR TOUS LES MODES**
```python
13:  for all α ∈ {ded, abd, ind} do            # ▷ SOLVE PHASE
14:    (x, y*) ← SAMPLEPREPAREDTASKS(P_α, B, t)  # ▷ x, y* prepared based on α, see §3.3.4&3.4
15:    y_x ∼ π_θ^solve(x)                       # ▷ solve
```

**Détails Lignes 13-15 :**
- **Ligne 13** : Itération sur les 3 modes de raisonnement
- **Ligne 14** : Échantillonnage et préparation tâches selon le mode
- **Ligne 15** : Résolution par le même modèle π_θ en mode solve

---

## 📊 **REWARD & RL UPDATE (Lignes 16-17)**

### **🏆 CALCUL DES RÉCOMPENSES ET MISE À JOUR**
```python
16: Reward: Use proposed task triplets and solved answers to get r_propose & r_solve  # ▷ see §3.1
17: RL update: use Task Relative REINFORCE++ to update π_θ  # ▷ see §3.3.5
```

**Détails Lignes 16-17 :**
- **Ligne 16** : Calcul récompenses duales (proposition + résolution)
- **Ligne 17** : Mise à jour modèle via Task Relative REINFORCE++ (TRR++)

---

## 🔧 **FONCTIONS CLÉS DÉTAILLÉES**

### **VALIDATEBYEXECUTING - Validation Multi-Critères**
```python
def validate_by_executing(program, inputs, *validation_flags):
    """
    Validation complète selon les flags spécifiés
    
    Args:
        program: Code Python à valider
        inputs: Entrées à tester
        validation_flags: SYNTAX, SAFETY, DETERMINISM
    
    Returns:
        outputs si valide, None sinon
    """
    # 1. SYNTAX: Vérification syntaxe Python
    if 'SYNTAX' in validation_flags:
        try:
            compile(program, '<string>', 'exec')
        except SyntaxError:
            return None
    
    # 2. SAFETY: Vérification packages dangereux
    if 'SAFETY' in validation_flags:
        dangerous_packages = ['os', 'sys', 'shutil', 'subprocess']
        for pkg in dangerous_packages:
            if f'import {pkg}' in program or f'from {pkg}' in program:
                return None
    
    # 3. DETERMINISM: Vérification reproductibilité
    if 'DETERMINISM' in validation_flags:
        # Exécution multiple pour vérifier déterminisme
        results = []
        for _ in range(2):  # j=2 exécutions
            try:
                result = execute_safely(program, inputs)
                results.append(result)
            except:
                return None
        
        if len(set(results)) > 1:  # Résultats différents
            return None
    
    # Exécution finale si toutes validations passées
    try:
        return execute_safely(program, inputs)
    except:
        return None
```

### **SAMPLEPREPAREDTASKS - Préparation par Mode**
```python
def sample_prepared_tasks(buffer, batch_size, iteration):
    """
    Échantillonnage et préparation selon le mode de raisonnement
    
    Args:
        buffer: Buffer spécialisé (P_ded, P_abd, P_ind)
        batch_size: Nombre de tâches à préparer
        iteration: Itération courante
    
    Returns:
        List[(x, y*)]: Problèmes formatés et solutions de référence
    """
    prepared_tasks = []
    
    for _ in range(batch_size):
        triplet = buffer.sample_weighted(iteration)  # Échantillonnage pondéré
        
        if buffer.mode == 'deduction':
            # Format: Donné (programme, input) → prédire output
            x = f"Program: {triplet.program}\nInput: {triplet.input}\nOutput: ?"
            y_star = triplet.output
            
        elif buffer.mode == 'abduction':
            # Format: Donné (programme, output) → prédire input
            x = f"Program: {triplet.program}\nOutput: {triplet.output}\nInput: ?"
            y_star = triplet.input
            
        elif buffer.mode == 'induction':
            # Format: Donné {(input, output)} → prédire programme
            io_examples = triplet.io_pairs[:len(triplet.io_pairs)//2]  # Première moitié
            x = f"Examples: {io_examples}\nMessage: {triplet.message}\nProgram: ?"
            y_star = triplet.program
        
        prepared_tasks.append((x, y_star))
    
    return prepared_tasks
```

### **Task Relative REINFORCE++ (TRR++)**
```python
def task_relative_reinforce_plus_plus(rewards, task_types, roles):
    """
    Implémentation TRR++ avec 6 baselines séparées
    Innovation clé pour réduction de variance en multi-tâches
    
    Args:
        rewards: Liste des récompenses obtenues
        task_types: Types de tâches ['ded', 'abd', 'ind']
        roles: Rôles ['propose', 'solve']
    
    Returns:
        advantages: Avantages normalisés pour mise à jour gradient
    """
    # Calcul des 6 baselines séparées (3 task_types × 2 roles)
    baselines = {}
    
    for task_type in ['ded', 'abd', 'ind']:
        for role in ['propose', 'solve']:
            # Filtrage récompenses pour cette combinaison spécifique
            filtered_rewards = [
                rewards[i] for i, (t, r) in enumerate(zip(task_types, roles))
                if t == task_type and r == role
            ]
            
            if filtered_rewards:
                baselines[f"{task_type}_{role}"] = np.mean(filtered_rewards)
            else:
                baselines[f"{task_type}_{role}"] = 0.0
    
    # Calcul avantages normalisés
    advantages = []
    for reward, task_type, role in zip(rewards, task_types, roles):
        baseline_key = f"{task_type}_{role}"
        baseline = baselines[baseline_key]
        advantage = reward - baseline
        advantages.append(advantage)
    
    return advantages
```

---

## 🎯 **INNOVATIONS TECHNIQUES RÉVÉLÉES**

### **🔄 Self-Play Training Loop Complet**
- **Dual Phase Architecture** : PROPOSE → SOLVE avec buffers séparés
- **Multi-Mode Reasoning** : Gestion simultanée des 3 modes de raisonnement
- **Validation Rigoureuse** : Integrity + Safety + Determinism checks

### **📊 Buffer Management Sophistiqué**
- **Dynamic Growth** : Buffers s'enrichissent avec tâches valides uniquement
- **Intelligent Sampling** : K références pour promouvoir diversité
- **Stability Mechanisms** : Remplissage uniforme si nécessaire (>10 inputs)

### **🛡️ Robust Validation System**
- **Program Integrity** : Exécution Python réelle avec gestion d'erreurs
- **Program Safety** : Blacklist explicite de packages dangereux
- **Determinism Check** : Validation reproductibilité via j=2 exécutions

### **⚡ Task Relative REINFORCE++ (TRR++)**
- **6 Separate Baselines** : Réduction variance optimale pour multi-tâches
- **Dual Rewards** : r_propose + r_solve avec pondération λ
- **Specialized Updates** : Gradients adaptés par type de tâche et rôle

---

*Analyse complète de l'Algorithme 1 AZR avec tous les détails d'implémentation*
