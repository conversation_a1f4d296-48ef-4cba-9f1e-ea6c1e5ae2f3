# 🏗️ STRUCTURE DU REPOSITORY OFFICIEL AZR

## 📁 **ARCHITECTURE COMPLÈTE**

### **Repository** : https://github.com/LeapLabTHU/Absolute-Zero-Reasoner

```
LeapLabTHU/Absolute-Zero-Reasoner/
├── 📁 absolute_zero_reasoner/          # 🎯 CODE PRINCIPAL AZR
│   ├── 📁 data_construction/           # Construction des données
│   ├── 📁 environment/                 # Environnement d'exécution
│   ├── 📁 models/                      # Modèles et architectures
│   ├── 📁 rewards/                     # Système de récompenses
│   ├── 📁 training/                    # Entraînement self-play
│   └── 📁 utils/                       # Utilitaires
├── 📁 assets/                          # Images et ressources
├── 📁 data/                            # Données d'entraînement
├── 📁 evaluation/                      # 🔬 CODE D'ÉVALUATION
│   ├── 📁 code_eval/                   # Évaluation code
│   └── 📁 math_eval/                   # Évaluation math
├── 📁 extras/                          # Fonctionnalités supplémentaires
├── 📁 scripts/                         # 🚀 SCRIPTS D'ENTRAÎNEMENT
│   ├── 📁 seeding/                     # Scripts de seeding
│   └── 📁 selfplay/                    # Scripts self-play
├── 📁 verl/                            # 🧠 FRAMEWORK RL
├── 📄 requirements.txt                 # Dépendances Python
└── 📄 README.md                        # Documentation principale
```

---

## 🎯 **COMPOSANTS CLÉS**

### **1. absolute_zero_reasoner/ - CŒUR DU SYSTÈME**

#### **data_construction/**
- Construction des datasets de seeding
- Traitement des données d'évaluation
- Validation des tâches générées

#### **environment/**
- Exécuteur Python sécurisé
- Validation des programmes
- Gestion des timeouts et erreurs

#### **models/**
- Interfaces pour différents modèles
- Support Qwen2.5, Llama3.1
- Gestion des architectures

#### **rewards/**
- Système de récompenses AZR
- Learnability reward (Zone Goldilocks)
- Accuracy reward
- Récompenses personnalisables

#### **training/**
- Boucle self-play principale
- Gestion des buffers
- Optimisation TRR++

#### **utils/**
- Configuration système
- Utilitaires de conversion
- Helpers divers

### **2. scripts/ - SCRIPTS PRÊTS À L'EMPLOI**

#### **seeding/**
- `7b.sh` - Seeding pour modèles 7B
- `14b.sh` - Seeding pour modèles 14B
- `coder3b.sh` - Seeding pour Qwen2.5-Coder-3B
- `coder7b.sh` - Seeding pour Qwen2.5-Coder-7B
- `coder14b.sh` - Seeding pour Qwen2.5-Coder-14B
- `llama.sh` - Seeding pour Llama3.1

#### **selfplay/**
- Scripts d'entraînement pour chaque taille
- Configuration multi-GPU automatique
- Gestion des checkpoints

### **3. evaluation/ - ÉVALUATION COMPLÈTE**

#### **code_eval/**
- **HumanEval+** : Évaluation sur problèmes de code
- **MBPP+** : More Basic Python Problems
- **LiveCodeBench** : Benchmarks en temps réel

#### **math_eval/**
- **AIME** : American Invitational Mathematics Examination
- **AMC** : American Mathematics Competitions
- **MATH500** : Dataset mathématique
- **Minerva** : Benchmark de raisonnement mathématique
- **OlympiadBench** : Problèmes d'olympiades

### **4. verl/ - FRAMEWORK REINFORCEMENT LEARNING**

- Fork optimisé du framework veRL
- Support vLLM pour rollouts
- Intégration wandb pour monitoring
- Optimisations pour AZR

---

## 🛠️ **CONFIGURATION TECHNIQUE**

### **Prérequis Système**
```bash
# GPU Requirements
3B models:  2 x 80GB GPUs
7B models:  4 x 80GB GPUs  
14B models: 8 x 80GB GPUs

# CUDA
CUDA 12.4.1+

# Python
Python 3.10
```

### **Installation Complète**
```bash
# 1. Environnement
conda create -n azr python=3.10
conda activate azr
conda install nvidia/label/cuda-12.4.1::cuda-toolkit

# 2. veRL Framework
cd verl
pip install -e .
cd ..

# 3. Dépendances
pip install wheel
pip install flash-attn --no-build-isolation
pip install -r requirements.txt
pip uninstall vllm
pip install vllm==0.7.3
pip install transformers==4.47.1
pip install "math-verify[antlr4_9_3]"
pip install debugpy

# 4. Données d'évaluation
python -m absolute_zero_reasoner.data_construction.process_code_reasoning_data
```

---

## 🚀 **UTILISATION PRATIQUE**

### **1. Seeding (Optionnel)**
```bash
export OUTPUT_SEED_PATH=data/custom_seed.jsonl
export OUTPUT_CODE_F_SEED_PATH=data/custom_ind_seed.jsonl
bash scripts/seeding/coder7b.sh
```

### **2. Entraînement Self-Play**
```bash
# Qwen2.5-Coder-7B
bash scripts/selfplay/coder7b.sh

# Avec données personnalisées
export OUTPUT_SEED_PATH=data/my_seed.jsonl
export OUTPUT_CODE_F_SEED_PATH=data/my_ind_seed.jsonl
bash scripts/selfplay/coder7b.sh
```

### **3. Reprise d'Entraînement**
```bash
# Modifier le script avec wandb run id
trainer.wandb_run_id=<your_run_id>
bash scripts/selfplay/coder7b.sh
```

### **4. Conversion Checkpoints**
```bash
python -m absolute_zero_reasoner.utils.convert2hf \
  <verl_ckpt_path>/actor \
  <verl_ckpt_path>/actor/huggingface/ \
  <hf_ckpt_path>
```

### **5. Évaluation**
```bash
# HumanEval+
bash evaluation/code_eval/scripts/run_evalplus.sh 0 humaneval <model_name>

# MBPP+
bash evaluation/code_eval/scripts/run_evalplus.sh 0 mbpp <model_name>

# LiveCodeBench
bash evaluation/code_eval/scripts/run_lcb_gen.sh --model <model_name>
```

---

## 🎯 **TEMPLATE DE PROMPT**

```python
PROMPT_TEMPLATE = """A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> <answer> answer here </answer>. User: {question}\nAssistant: <think>"""
```

---

## 📊 **RÉSULTATS ATTENDUS**

### **Performance Benchmarks**
| Modèle | Base | Code Avg | Math Avg | Total Avg |
|--------|------|-----------|-----------|-----------|
| AZR (Base) | Qwen2.5-7B | 55.2 (+3.2) | 38.4 (+10.9) | 46.8 (+7.0) |
| AZR (Coder) | Qwen2.5-7B-Coder | **61.6** (+5.0) | 39.1 (+15.2) | **50.4** (+10.2) |

### **Scaling Results**
- **3B** : +5.7 points improvement
- **7B** : +10.2 points improvement  
- **14B** : +13.2 points improvement

---

## 🔧 **PERSONNALISATION**

### **Récompenses Personnalisées**
```python
# Dans configs, ajouter à azr.reward.generation_reward_config
custom_rewards = {
    'diversity_reward': DiversityReward(),
    'complexity_reward': ComplexityReward(),
    'custom_reward': YourCustomReward()
}
```

### **Nouveaux Types de Tâches**
- Étendre les classes de raisonnement
- Ajouter de nouveaux environnements
- Personnaliser les validateurs

**Le repository officiel fournit une base complète et extensible pour tous les besoins AZR !**
