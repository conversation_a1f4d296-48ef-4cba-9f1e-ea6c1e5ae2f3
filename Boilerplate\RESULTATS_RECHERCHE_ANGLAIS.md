# 🇺🇸 RÉSULTATS RECHERCHE ANGLAIS - BOILERPLATE AZR

## 🎯 DÉCOUVERTE MAJEURE : REPOSITORY OFFICIEL TROUVÉ !

### 📁 **Repository Principal**
- **URL** : https://github.com/LeapLabTHU/Absolute-Zero-Reasoner
- **Auteur** : LeapLabTHU (Tsinghua University)
- **Stars** : 1.5k ⭐
- **Forks** : 256 🍴
- **License** : MIT

### 🏗️ **Structure du Repository**

```
LeapLabTHU/Absolute-Zero-Reasoner/
├── absolute_zero_reasoner/     # Code principal AZR
├── assets/                     # Images et ressources
├── data/                       # Données d'entraînement
├── evaluation/                 # Code d'évaluation
├── extras/                     # Fonctionnalités supplémentaires
├── scripts/                    # Scripts d'entraînement
├── verl/                       # Framework RL (fork de veRL)
├── requirements.txt            # Dépendances Python
└── README.md                   # Documentation principale
```

### 🚀 **Fonctionnalités Clés**

#### **Entraînement Self-Play**
- Scripts pour différentes tailles de modèles (3B, 7B, 14B)
- Support multi-GPU (2x80GB pour 3B, 4x80GB pour 7B, 8x80GB pour 14B)
- Seeding optionnel avec données personnalisées
- Reprise d'entraînement avec wandb

#### **Architectures Supportées**
- Qwen2.5 (3B, 7B, 14B)
- Qwen2.5-Coder (3B, 7B, 14B)
- Llama3.1-8B

#### **Types de Tâches**
- **Déduction** : (programme, entrée) → sortie
- **Abduction** : (programme, sortie) → entrée  
- **Induction** : {(entrée, sortie)} → programme

### 🛠️ **Installation et Setup**

```bash
# Environnement
conda create -n azr python=3.10
conda activate azr
conda install nvidia/label/cuda-12.4.1::cuda-toolkit

# Installation veRL
cd verl
pip install -e .
cd ..

# Dépendances
pip install wheel
pip install flash-attn --no-build-isolation
pip install -r requirements.txt
pip uninstall vllm
pip install vllm==0.7.3
pip install transformers==4.47.1
pip install "math-verify[antlr4_9_3]"
pip install debugpy
```

### 🏋️ **Entraînement**

```bash
# Seeding (optionnel)
export OUTPUT_SEED_PATH=data/<seed_data_name>.jsonl
export OUTPUT_CODE_F_SEED_PATH=data/<ind_seed_data_name>.jsonl
bash scripts/seeding/<model_size>.sh

# Self-play
bash scripts/selfplay/<model_size>.sh
```

### 📊 **Évaluation**

#### **Benchmarks Supportés**
- **Code** : HumanEval+, MBPP+, LiveCodeBench
- **Math** : AIME'24, AIME'25, AMC'23, MATH500, Minerva, OlympiadBench

#### **Scripts d'Évaluation**
```bash
# LiveCodeBench
bash evaluation/code_eval/scripts/run_lcb_gen.sh --model <model_name>

# EvalPlus
bash evaluation/code_eval/scripts/run_evalplus.sh 0 <humaneval|mbpp> <model_name>
```

### 🎯 **Template de Prompt**

```
A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> <answer> answer here </answer>. User: {question}\nAssistant: <think>
```

### 📈 **Résultats de Performance**

| Modèle | Base | Code Avg | Math Avg | Total Avg |
|--------|------|-----------|-----------|-----------|
| AZR (Base) | Qwen2.5-7B | 55.2 (+3.2) | 38.4 (+10.9) | 46.8 (+7.0) |
| AZR (Coder) | Qwen2.5-7B-Coder | **61.6** (+5.0) | 39.1 (+15.2) | **50.4** (+10.2) |

### 🔧 **Composants Techniques**

#### **Framework RL**
- Fork de veRL (Volcano Engine RL)
- Support vLLM pour rollouts
- Intégration wandb pour logging

#### **Exécuteur Python**
- ⚠️ **ATTENTION** : Version recherche uniquement, non sécurisée
- Validation des tâches par exécution
- Filtrage des packages dangereux

#### **Système de Récompenses**
- **Learnability Reward** : Zone Goldilocks (ni trop facile, ni impossible)
- **Accuracy Reward** : Feedback objectif de l'environnement
- Récompenses personnalisables (diversité, complexité)

### 🔗 **Liens Utiles**

- **Paper** : https://arxiv.org/abs/2505.03335
- **Project Page** : https://andrewzh112.github.io/absolute-zero-reasoner/
- **Models HuggingFace** : https://huggingface.co/collections/andrewzh/absolute-zero-reasoner-68139b2bca82afb00bc69e5b
- **Logs W&B** : https://wandb.ai/andrewzhao112/AbsoluteZeroReasoner

### 📧 **Contact**
- **Auteur Principal** : Andrew Zhao (<EMAIL>)
- **Affiliation** : Tsinghua University

## 🎯 **CONCLUSION RECHERCHE ANGLAIS**

✅ **BOILERPLATE OFFICIEL TROUVÉ** : Repository complet avec code d'entraînement et d'évaluation
✅ **DOCUMENTATION COMPLÈTE** : Setup, entraînement, évaluation détaillés
✅ **MODÈLES PRÉ-ENTRAÎNÉS** : Disponibles sur HuggingFace
✅ **SCRIPTS PRÊTS** : Pour différentes tailles de modèles
✅ **FRAMEWORK COMPLET** : veRL + vLLM + wandb

**Le repository officiel constitue le boilerplate parfait pour AZR !**
