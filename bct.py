#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
🧠 BCT - BACCARAT COUNTING TOOL
================================================================================

Outil de comptage et prédiction Baccarat basé sur l'architecture AZR
VERSION STRUCTURE DE BASE : Interface graphique + Système de comptage

COMPOSANTS OPÉRATIONNELS :
- AZRConfig bien structuré
- Système de comptage conforme à systeme_comptage_baccarat_complet.txt
- Interface graphique complètement fonctionnelle (prédictions S/O)
- Ossature des classes (sans méthodes complexes)

AUTEUR : AZR System
DATE : 2025
VERSION : 2.0.0 (BCT)
================================================================================
"""

import os
import sys
import json
import logging
import threading
import multiprocessing
from typing import Dict, List, Optional, Tuple, Any, Iterator
from dataclasses import dataclass, field
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import tkinter as tk
from tkinter import ttk, messagebox

# Import de la configuration AZR
from AZRConfig import AZRConfig

# Import du système révolutionnaire AZR
from Rollouts import AZRRolloutManager

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bct.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

################################################################################
#                                                                              #
#  🎯 SECTION 2 : STRUCTURES DE DONNÉES SYSTÈME COMPTAGE + AZR                 #
#                                                                              #
################################################################################

# Nouvelles structures pour l'implémentation AZR
from typing import Callable, Union
import numpy as np
from dataclasses import dataclass, field
from enum import Enum

class AZRTaskType(Enum):
    """Types de tâches AZR adaptées au Baccarat"""
    PATTERN_ANALYSIS = "pattern_analysis"      # Équivalent Induction AZR
    SEQUENCE_PREDICTION = "sequence_prediction" # Équivalent Déduction AZR
    REVERSE_ENGINEERING = "reverse_engineering" # Équivalent Abduction AZR

@dataclass
class AZRTask:
    """
    Tâche AZR adaptée au Baccarat - Équivalent du triplet (p, i, o) d'AZR

    Basé sur Équations (15-17) d'AZR adaptées au contexte Baccarat
    """
    task_id: str
    task_type: AZRTaskType

    # Données d'entrée (équivalent 'x' dans AZR)
    input_sequence: List[str]           # Séquence P/B/T ou S/O
    input_metadata: Dict[str, Any]      # INDEX 1&2, états SYNC/DESYNC

    # Cible à prédire (équivalent 'y*' dans AZR)
    target_prediction: str              # S, O, ou pattern attendu
    target_confidence: float            # Confiance attendue [0,1]

    # Contexte de difficulté (pour learnability)
    difficulty_level: float             # [0,1] - 0.5 = optimal (Équation 4)
    complexity_score: float             # Mesure de complexité cognitive

    # Métadonnées AZR
    creation_timestamp: datetime = field(default_factory=datetime.now)
    solved_attempts: int = 0
    success_rate: float = 0.0           # Pour calcul learnability

    def calculate_learnability_reward(self) -> float:
        """
        Implémentation Équation (4) d'AZR pour BCT

        r_propose = {
            0,           si r̄_solve = 0 ou r̄_solve = 1
            1 - r̄_solve, sinon
        }
        """
        if self.success_rate == 0.0 or self.success_rate == 1.0:
            return 0.0  # Tâche triviale ou impossible
        else:
            return 1.0 - self.success_rate  # Zone d'apprentissage optimal

@dataclass
class AZRBuffer:
    """
    Buffer de tâches AZR pour chaque type de raisonnement

    Équivalent des buffers D_abd, D_ded, D_ind d'AZR
    """
    task_type: AZRTaskType
    tasks: List[AZRTask] = field(default_factory=list)
    max_size: int = 1000

    def add_task(self, task: AZRTask):
        """Ajoute une tâche au buffer avec rotation si nécessaire"""
        self.tasks.append(task)
        if len(self.tasks) > self.max_size:
            # Rotation FIFO
            self.tasks.pop(0)

    def sample_tasks(self, k: int) -> List[AZRTask]:
        """Échantillonne K tâches pour référence (comme dans AZR)"""
        if len(self.tasks) < k:
            return self.tasks.copy()
        return np.random.choice(self.tasks, k, replace=False).tolist()

    def get_average_success_rate(self) -> float:
        """Calcule le taux de succès moyen pour auto-curriculum"""
        if not self.tasks:
            return 0.0
        return np.mean([task.success_rate for task in self.tasks])

@dataclass
class AZRReward:
    """
    Structure de récompenses AZR adaptée au Baccarat

    Basé sur Équations (4), (5), (6) d'AZR
    """
    # Récompenses principales
    propose_reward: float = 0.0         # Équation (4) - Learnability
    solve_reward: float = 0.0           # Équation (5) - Accuracy

    # Récompenses composites (Équation 6)
    format_penalty: float = 0.0         # Pénalité formatage
    confidence_bonus: float = 0.0       # Bonus confiance

    # Métadonnées
    task_difficulty: float = 0.0
    prediction_accuracy: bool = False
    timestamp: datetime = field(default_factory=datetime.now)

    def calculate_composite_reward(self) -> float:
        """
        Implémentation Équation (6) d'AZR adaptée

        R(y_π) = {
            r_role,  si réponse correcte et bien formatée
            -0.5,    si réponse incorrecte mais bien formatée
            -1,      si erreurs de formatage
        }
        """
        if self.format_penalty < 0:
            return -1.0  # Erreurs de formatage
        elif not self.prediction_accuracy:
            return -0.5  # Incorrecte mais bien formatée
        else:
            # Récompense principale + bonus
            return self.solve_reward + self.confidence_bonus

################################################################################
#                                                                              #
#  🧮 SECTION 2.5 : MOTEUR MATHÉMATIQUE AZR COMPLET - ARCHITECTURE 3 ROLLOUTS #
#                                                                              #
################################################################################

class AZRMathEngine:
    """
    Moteur mathématique implémentant TOUTES les équations AZR adaptées au Baccarat

    ARCHITECTURE 3 ROLLOUTS :
    - ROLLOUT 1 (ANALYZER) : Analyse complète INDEX 1&2 → INDEX 3&4 (majeure partie)
    - ROLLOUT 2 (GENERATOR) : Génération d'hypothèses et séquences candidates
    - ROLLOUT 3 (PREDICTOR) : Prédiction finale S/O basée sur consensus

    Référence complète : EQUATIONS_AZR_REFERENCE_COMPLETE_FINALE.md (50 équations)
    Adaptation BCT : COMPREHENSION_COMPLETE_BCT_AZR_FINALE.md

    INNOVATION : Premier système d'auto-apprentissage pour prédiction Baccarat
    basé sur le paradigme Absolute Zero (apprentissage sans données externes)
    """

    def __init__(self, config: 'AZRConfig'):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.AZRMath")

        # Historiques pour TRR++ et normalisation (par rollout)
        self.reward_history = {
            # ROLLOUT 1 - ANALYZER
            'analyzer_pattern_analysis': [],
            'analyzer_correlation_detection': [],
            'analyzer_bias_detection': [],

            # ROLLOUT 2 - GENERATOR
            'generator_sequence_generation': [],
            'generator_hypothesis_creation': [],
            'generator_pattern_optimization': [],

            # ROLLOUT 3 - PREDICTOR
            'predictor_so_prediction': [],
            'predictor_consensus_building': [],
            'predictor_confidence_estimation': []
        }

        # Buffers pour auto-curriculum (par rollout)
        self.task_buffers = {
            'analyzer_tasks': [],      # Tâches d'analyse pour Rollout 1
            'generator_tasks': [],     # Tâches de génération pour Rollout 2
            'predictor_tasks': []      # Tâches de prédiction pour Rollout 3
        }

    # ========================================================================
    # 🎯 ÉQUATIONS ROLLOUT 1 - ANALYZER (MAJEURE PARTIE DU TRAVAIL)
    # Analyse complète INDEX 1&2 → INDEX 3&4, détection patterns et corrélations
    # ========================================================================

    def equation_1_sft_loss(self, predictions: List[str], targets: List[str]) -> float:
        """
        Équation (1) - SFT Loss adapté au Baccarat [ROLLOUT 1 - ANALYZER]

        L_SFT(θ) = -E_{(x,c*,y*) ~ D} log π_θ(c*,y* | x)

        USAGE ROLLOUT 1 : Évaluation qualité des analyses de corrélations INDEX 1&2 → INDEX 3&4
        Adaptation BCT : Loss pour apprentissage supervisé des prédictions S/O
        """
        if len(predictions) != len(targets):
            return float('inf')

        total_loss = 0.0
        for pred, target in zip(predictions, targets):
            # Probabilité de prédiction correcte (simulée)
            prob = 0.9 if pred == target else 0.1
            total_loss -= np.log(max(prob, 1e-10))  # Éviter log(0)

        return total_loss / len(predictions)

    def equation_2_rlvr_objective(self, predictions: List[str], targets: List[str],
                                 reward_function: Callable) -> float:
        """
        Équation (2) - RLVR Objective adapté au Baccarat [ROLLOUT 1 - ANALYZER]

        J_RLVR(θ) = E_{(x,y*) ~ D, y ~ π_θ(·|x)} [r(y,y*)]

        USAGE ROLLOUT 1 : Objectif d'optimisation pour analyse des patterns ternaires
        Adaptation BCT : Objectif RLVR pour prédictions S/O vérifiables
        """
        total_reward = 0.0
        for pred, target in zip(predictions, targets):
            total_reward += reward_function(pred, target)

        return total_reward / len(predictions) if predictions else 0.0

    def equation_3_azr_objective(self, propose_reward: float, solve_reward: float,
                                lambda_balance: float = 1.0) -> float:
        """
        Équation (3) - OBJECTIF PRINCIPAL AZR [TOUS ROLLOUTS - ÉQUATION MAÎTRESSE]

        J(θ) = max_θ E[r_propose(τ,π_θ) + λ * E[r_solve(y,y*)]]

        USAGE TOUS ROLLOUTS : Coordination des 3 rollouts via objectif unifié
        - ROLLOUT 1 : propose_reward = qualité analyse patterns
        - ROLLOUT 2 : propose_reward = qualité génération hypothèses
        - ROLLOUT 3 : solve_reward = précision prédictions S/O
        Adaptation BCT : Optimisation simultanée analyse patterns + prédiction S/O
        """
        return propose_reward + lambda_balance * solve_reward

    def equation_4_learnability_reward(self, success_rate: float) -> float:
        """
        Équation (4) - RÉCOMPENSE LEARNABILITY [ROLLOUT 1 - ANALYZER] (Innovation Clé AZR)

        r_propose = {
            0,           si r̄_solve = 0 ou r̄_solve = 1
            1 - r̄_solve, sinon
        }

        USAGE ROLLOUT 1 : Auto-curriculum pour générer tâches d'analyse optimales
        - Évite patterns trop faciles (100% succès) ou impossibles (0% succès)
        - Favorise patterns dans la "Zone Goldilocks" (~50% succès)
        Adaptation BCT : Zone Goldilocks pour prédictions S/O optimales
        """
        if success_rate == 0.0 or success_rate == 1.0:
            return 0.0  # Tâches triviales ou impossibles
        else:
            return 1.0 - success_rate  # Maximum à 0.5 quand success_rate = 0.5

    def equation_5_solver_reward(self, prediction: str, target: str) -> float:
        """
        Équation (5) - Récompense Solver

        r_solve = I(y = y*)

        Adaptation BCT : Récompense binaire pour prédiction S/O correcte
        """
        return 1.0 if prediction == target else 0.0

    def equation_6_composite_reward(self, base_reward: float,
                                  is_correct: bool, is_well_formatted: bool) -> float:
        """
        Équation (6) - Récompense Composite avec formatage

        R(y_π) = {
            r_role,  si réponse correcte et bien formatée
            -0.5,    si réponse incorrecte mais bien formatée
            -1,      si erreurs de formatage
        }

        Adaptation BCT : Pénalités graduées pour stabilité d'entraînement
        """
        if not is_well_formatted:
            return -1.0  # Erreurs de formatage
        elif not is_correct:
            return -0.5  # Incorrecte mais bien formatée
        else:
            return base_reward  # Récompense complète

    def equation_7_deterministic_programs(self, program_results: List[Any],
                                        num_executions: int = 2) -> bool:
        """
        Équation (7) - Validation Programmes Déterministes

        ∀p ∈ P_deterministic, ∀i ∈ I, (lim_{j→∞} p(i)^(1) = p(i)^(2) = ... = p(i)^(j))

        Adaptation BCT : Validation cohérence prédictions S/O répétées
        """
        if len(program_results) < num_executions:
            return False

        # Vérifier que toutes les exécutions donnent le même résultat
        first_result = program_results[0]
        return all(result == first_result for result in program_results[:num_executions])

    def equation_8_trr_plus_plus(self, reward: float, task_type: str,
                                role: str, reward_history: Dict = None) -> float:
        """
        Équation (8) - TRR++ (Task-Relative REINFORCE++)

        A_task,role^norm = (r - μ_task,role) / σ_task,role

        Adaptation BCT : Normalisation spécialisée par (pattern, rôle rollout)
        """
        if reward_history is None:
            reward_history = self.reward_history

        key = f"{task_type}_{role}"

        if key not in reward_history or len(reward_history[key]) < 2:
            return 0.0  # Pas assez d'historique

        rewards = reward_history[key]
        mean_reward = np.mean(rewards)
        std_reward = np.std(rewards)

        if std_reward == 0:
            return 0.0  # Éviter division par zéro

        return (reward - mean_reward) / std_reward

    # ========================================================================
    # ⚡ ÉQUATIONS ROLLOUT 2 - GENERATOR (GÉNÉRATION ET HYPOTHÈSES)
    # Sampling, génération de séquences, création d'hypothèses optimales
    # ========================================================================

    def equation_9_simple_program_sampling(self, function_pool: List[str]) -> str:
        """
        Équation (9) - Sampling Programmes Simples

        p ~ U(F_simple)

        Adaptation BCT : Sélection uniforme de patterns simples (pair_4, impair_5, pair_6)
        """
        if not function_pool:
            return 'pair_4'  # Défaut
        return np.random.choice(function_pool)

    def equation_10_composite_program_sampling(self, simple_programs: List[str],
                                             composition_count: int) -> str:
        """
        Équation (10) - Sampling Programmes Composites

        p = f_c ∘ f_{c-1} ∘ ... ∘ f_1, où c ~ U(1,3) et f_i ~ U(F_simple)

        Adaptation BCT : Composition de patterns pour séquences complexes
        """
        if composition_count <= 0 or not simple_programs:
            return self.equation_9_simple_program_sampling(simple_programs)

        # Composer les patterns
        composed_pattern = []
        for _ in range(min(composition_count, 3)):  # Maximum 3 compositions
            pattern = np.random.choice(simple_programs)
            composed_pattern.append(pattern)

        return '_'.join(composed_pattern)

    def equation_11_binary_decision(self, probability: float = 0.5) -> bool:
        """
        Équation (11) - Décision Binaire

        d ~ Bernoulli(0.5)

        Adaptation BCT : Choix entre patterns simples vs composites
        """
        return np.random.random() < probability

    def equation_12_uniform_composition_count(self, min_count: int = 1, max_count: int = 3) -> int:
        """
        Équation (12) - Comptage Uniforme de Compositions

        c ~ U(1,3)

        Adaptation BCT : Nombre de patterns à composer pour séquences complexes
        """
        return np.random.randint(min_count, max_count + 1)

    def equation_13_input_output_generation(self, program: str, input_space: List[Any]) -> Tuple[Any, Any]:
        """
        Équation (13) - Génération Entrée-Sortie

        i ~ U(I), o = p(i)

        Adaptation BCT : Génération de séquences test pour validation patterns
        """
        if not input_space:
            return None, None

        # Sélectionner entrée aléatoire
        input_val = np.random.choice(input_space)

        # Simuler exécution du programme (pattern)
        output_val = self._simulate_pattern_execution(program, input_val)

        return input_val, output_val

    def equation_14_k_examples_sampling(self, task_buffer: List[Dict], k: int = 6) -> List[Dict]:
        """
        Équation (14) - Sampling K Exemples

        {(p_j, i_j, o_j)}_{j=1}^K ~ U(D_task)

        Adaptation BCT : Sélection d'exemples passés pour conditionnement
        """
        if len(task_buffer) <= k:
            return task_buffer.copy()

        return np.random.choice(task_buffer, size=k, replace=False).tolist()

    def equation_15_task_conditioning(self, task_type: str, examples: List[Dict],
                                    description: str = "") -> Dict[str, Any]:
        """
        Équation (15) - Conditionnement de Tâche

        x = (task_type, {(p_j, i_j, o_j)}_{j=1}^K, m)

        Adaptation BCT : Construction contexte pour prédiction S/O
        """
        return {
            'task_type': task_type,
            'examples': examples,
            'description': description,
            'timestamp': datetime.now()
        }

    # ========================================================================
    # 🏆 ÉQUATIONS ROLLOUT 3 - PREDICTOR (PRÉDICTION ET CONSENSUS)
    # Évaluation, métriques, prédiction finale S/O, consensus des rollouts
    # ========================================================================

    def equation_16_monte_carlo_estimation(self, task: Dict, solver_function: Callable,
                                         n_rollouts: int = 8) -> float:
        """
        Équation (16) - Estimation Monte Carlo

        r̄_solve = (1/n) Σ_{i=1}^n r_solve^(i)

        Adaptation BCT : Estimation fiabilité prédictions S/O
        """
        successes = 0

        for i in range(n_rollouts):
            try:
                prediction = solver_function(task)
                target = task.get('target_prediction', '')
                if prediction == target:
                    successes += 1
            except Exception as e:
                self.logger.debug(f"Rollout {i} failed: {e}")
                # Échec compte comme 0

        return successes / n_rollouts

    def equation_17_accuracy_calculation(self, predictions: List[str], targets: List[str]) -> float:
        """
        Équation (17) - Calcul Précision

        Accuracy = (1/N) Σ_{i=1}^N I(y_i = y_i*)

        Adaptation BCT : Précision prédictions S/O sur historique
        """
        if len(predictions) != len(targets) or len(predictions) == 0:
            return 0.0

        correct = sum(1 for pred, target in zip(predictions, targets) if pred == target)
        return correct / len(predictions)

    def equation_18_diversity_metric(self, answers: List[str]) -> float:
        """
        Équation (18) - Métrique Diversité

        Diversity = 1 - p(answer)

        Adaptation BCT : Diversité des prédictions générées
        """
        if not answers:
            return 0.0

        # Calculer la probabilité de la réponse la plus fréquente
        from collections import Counter
        counts = Counter(answers)
        max_count = max(counts.values())
        max_probability = max_count / len(answers)

        return 1.0 - max_probability

    def equation_19_complexity_score(self, sequence: List[str], metadata: Dict[str, Any]) -> float:
        """
        Équation (19) - Score Complexité (inspiré ComplexiPy)

        Complexity = f(AST_depth, Halstead_metrics, cyclomatic_complexity)

        Adaptation BCT : Complexité cognitive des patterns de prédiction
        """
        complexity = 0.0

        # Complexité de la séquence
        complexity += len(sequence) * 0.1

        # Complexité des patterns uniques
        unique_patterns = len(set(sequence))
        complexity += unique_patterns * 0.2

        # Complexité des changements d'état SYNC/DESYNC
        if 'sync_changes' in metadata:
            complexity += metadata['sync_changes'] * 0.3

        # Complexité des TIE (plus difficile à prédire)
        tie_count = sequence.count('--')
        complexity += tie_count * 0.4

        # Complexité des transitions impair_5 (priorité maximale)
        impair_5_count = metadata.get('impair_5_count', 0)
        complexity += impair_5_count * 0.5

        # Normaliser entre 0 et 1
        return min(complexity / 10.0, 1.0)

    def calculate_complexity_score(self, recent_pb: List[str], data: Dict[str, Any]) -> float:
        """
        Méthode wrapper pour equation_19_complexity_score

        Calcule la complexité d'une séquence pour l'auto-curriculum AZR
        Utilisée par les rollouts pour générer des tâches dans la Zone Goldilocks
        """
        # Construire les métadonnées à partir des données disponibles
        metadata = {
            'sync_changes': data.get('sync_changes', 0),
            'impair_5_count': data.get('impair_5_count', 0),
            'tie_count': recent_pb.count('--') if recent_pb else 0
        }

        # Utiliser la séquence P/B récente ou une séquence par défaut
        sequence = recent_pb if recent_pb else ['P', 'B']

        return self.equation_19_complexity_score(sequence, metadata)

    def equation_20_combined_score(self, code_avg: float, math_avg: float) -> float:
        """
        Équation (20) - Score Combiné

        AVG = (CAvg + MAvg) / 2

        Adaptation BCT : Score combiné analyse patterns + prédiction S/O
        """
        return (code_avg + math_avg) / 2.0

    # ========================================================================
    # 🔧 ÉQUATIONS COMMUNES - OPTIMISATION ET APPRENTISSAGE (21-30)
    # Utilisées par tous les rollouts : PPO, gradients, loss, buffers
    # ========================================================================

    def equation_21_ppo_clipping(self, probability_ratio: float, advantage: float,
                                epsilon: float = 0.2) -> float:
        """
        Équation (21) - PPO avec Clipping

        L_PPO = E[min(s_t(θ)A^norm, clip(s_t(θ), 1-ε, 1+ε)A^norm)]

        Adaptation BCT : Optimisation stable des prédictions S/O
        """
        clipped_ratio = np.clip(probability_ratio, 1 - epsilon, 1 + epsilon)
        return min(probability_ratio * advantage, clipped_ratio * advantage)

    def equation_22_probability_ratio(self, new_log_prob: float, old_log_prob: float) -> float:
        """
        Équation (22) - Ratio de Probabilité

        s_t(θ) = π_θ(a_t|s_t) / π_θ_old(a_t|s_t)

        Adaptation BCT : Ratio pour mise à jour politique prédiction
        """
        return np.exp(new_log_prob - old_log_prob)

    def equation_23_advantage_normalization(self, advantages: List[float]) -> List[float]:
        """
        Équation (23) - Normalisation Avantages

        A^norm = (A - μ_A) / σ_A

        Adaptation BCT : Normalisation avantages pour stabilité
        """
        if len(advantages) <= 1:
            return advantages

        mean_adv = np.mean(advantages)
        std_adv = np.std(advantages)

        if std_adv == 0:
            return [0.0] * len(advantages)

        return [(adv - mean_adv) / std_adv for adv in advantages]

    def equation_24_learning_rate_schedule(self, initial_lr: float, step: int,
                                         total_steps: int, schedule_type: str = 'linear') -> float:
        """
        Équation (24) - Planification Taux d'Apprentissage

        lr_t = lr_0 * f(t, T)

        Adaptation BCT : Décroissance adaptative pour convergence
        """
        if schedule_type == 'linear':
            return initial_lr * (1 - step / total_steps)
        elif schedule_type == 'cosine':
            return initial_lr * 0.5 * (1 + np.cos(np.pi * step / total_steps))
        elif schedule_type == 'exponential':
            decay_rate = 0.95
            return initial_lr * (decay_rate ** (step / 100))
        else:
            return initial_lr

    def equation_25_gradient_clipping(self, gradients: List[float], max_norm: float = 1.0) -> List[float]:
        """
        Équation (25) - Clipping de Gradients

        g_clipped = g * min(1, max_norm / ||g||)

        Adaptation BCT : Stabilisation entraînement prédictions
        """
        if not gradients:
            return gradients

        grad_norm = np.linalg.norm(gradients)

        if grad_norm <= max_norm:
            return gradients

        scaling_factor = max_norm / grad_norm
        return [grad * scaling_factor for grad in gradients]

    def equation_26_entropy_regularization(self, probabilities: List[float], beta: float = 0.01) -> float:
        """
        Équation (26) - Régularisation Entropie

        H(π) = -Σ π(a) log π(a)

        Adaptation BCT : Encourager exploration prédictions diverses
        """
        if not probabilities or sum(probabilities) == 0:
            return 0.0

        # Normaliser les probabilités
        total = sum(probabilities)
        normalized_probs = [p / total for p in probabilities]

        entropy = 0.0
        for prob in normalized_probs:
            if prob > 0:
                entropy -= prob * np.log(prob)

        return beta * entropy

    def equation_27_value_function_loss(self, predicted_values: List[float],
                                      target_values: List[float]) -> float:
        """
        Équation (27) - Loss Fonction de Valeur

        L_V = (1/N) Σ (V(s) - V_target(s))²

        Adaptation BCT : Estimation valeur des patterns détectés
        """
        if len(predicted_values) != len(target_values):
            return float('inf')

        mse = np.mean([(pred - target) ** 2 for pred, target in zip(predicted_values, target_values)])
        return mse

    def equation_28_policy_loss(self, log_probs: List[float], advantages: List[float]) -> float:
        """
        Équation (28) - Loss Politique

        L_π = -E[log π(a|s) * A(s,a)]

        Adaptation BCT : Optimisation politique prédiction S/O
        """
        if len(log_probs) != len(advantages):
            return 0.0

        policy_loss = -np.mean([log_prob * advantage for log_prob, advantage in zip(log_probs, advantages)])
        return policy_loss

    def equation_29_total_loss(self, policy_loss: float, value_loss: float, entropy_loss: float,
                             c1: float = 0.5, c2: float = 0.01) -> float:
        """
        Équation (29) - Loss Totale

        L_total = L_π + c1 * L_V + c2 * L_H

        Adaptation BCT : Combinaison optimale des objectifs d'apprentissage
        """
        return policy_loss + c1 * value_loss + c2 * entropy_loss

    def equation_30_buffer_update(self, buffer: List[Dict], new_experiences: List[Dict],
                                 max_size: int = 1000) -> List[Dict]:
        """
        Équation (30) - Mise à jour Buffer

        D_t+1 = D_t ∪ {nouvelles_expériences}

        Adaptation BCT : Gestion mémoire expériences prédiction
        """
        updated_buffer = buffer + new_experiences

        # Limiter la taille du buffer
        if len(updated_buffer) > max_size:
            # Garder les expériences les plus récentes
            updated_buffer = updated_buffer[-max_size:]

        return updated_buffer

    # ========================================================================
    # 🚀 ÉQUATIONS SPÉCIALISÉES BCT-AZR (31-50) - INNOVATIONS AVANCÉES
    # Spécialisations pour Baccarat : curriculum, confiance, ensemble, complexité
    # ========================================================================

    def equation_31_task_buffer_management(self, buffer: List[Dict], new_task: Dict,
                                         max_size: int = 1000) -> List[Dict]:
        """
        Équation (31) - Gestion Buffer de Tâches

        D_task = D_task ∪ {nouvelle_tâche} avec rotation si |D_task| > max_size

        Adaptation BCT : Gestion mémoire patterns de prédiction
        """
        updated_buffer = buffer + [new_task]

        if len(updated_buffer) > max_size:
            # Rotation FIFO pour garder les patterns récents
            updated_buffer = updated_buffer[-max_size:]

        return updated_buffer

    def equation_32_curriculum_progression(self, current_difficulty: float,
                                         success_rate: float, step_size: float = 0.1) -> float:
        """
        Équation (32) - Progression Curriculum

        difficulty_t+1 = difficulty_t + α * f(success_rate)

        Adaptation BCT : Auto-curriculum pour complexité prédictions
        """
        if success_rate > 0.8:
            # Trop facile, augmenter difficulté
            return min(current_difficulty + step_size, 1.0)
        elif success_rate < 0.3:
            # Trop difficile, réduire difficulté
            return max(current_difficulty - step_size, 0.0)
        else:
            # Zone optimale, maintenir
            return current_difficulty

    def equation_33_exploration_exploitation(self, exploration_rate: float,
                                           step: int, total_steps: int) -> float:
        """
        Équation (33) - Balance Exploration/Exploitation

        ε_t = ε_0 * exp(-λt)

        Adaptation BCT : Décroissance exploration pour convergence
        """
        decay_rate = 0.995
        return exploration_rate * (decay_rate ** step)

    def equation_34_confidence_estimation(self, predictions: List[str],
                                        targets: List[str], window_size: int = 10) -> float:
        """
        Équation (34) - Estimation Confiance

        confidence = accuracy_recent * consistency_factor

        Adaptation BCT : Confiance prédictions S/O récentes
        """
        if len(predictions) < window_size:
            window_size = len(predictions)

        if window_size == 0:
            return 0.0

        # Précision récente
        recent_preds = predictions[-window_size:]
        recent_targets = targets[-window_size:]
        accuracy = self.equation_17_accuracy_calculation(recent_preds, recent_targets)

        # Facteur de consistance (moins de variabilité = plus de confiance)
        consistency = 1.0 - self.equation_18_diversity_metric(recent_preds)

        return accuracy * consistency

    def equation_35_adaptive_learning_rate(self, base_lr: float, gradient_norm: float,
                                         target_norm: float = 1.0) -> float:
        """
        Équation (35) - Taux d'Apprentissage Adaptatif

        lr_adaptive = lr_base * min(1, target_norm / gradient_norm)

        Adaptation BCT : Adaptation automatique vitesse apprentissage
        """
        if gradient_norm == 0:
            return base_lr

        scaling_factor = min(1.0, target_norm / gradient_norm)
        return base_lr * scaling_factor

    def equation_36_pattern_similarity(self, pattern1: List[str], pattern2: List[str]) -> float:
        """
        Équation (36) - Similarité Patterns

        similarity = |intersection| / |union|

        Adaptation BCT : Mesure similarité séquences S/O
        """
        if not pattern1 or not pattern2:
            return 0.0

        set1 = set(pattern1)
        set2 = set(pattern2)

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def equation_37_temporal_weighting(self, values: List[float], decay_factor: float = 0.9) -> List[float]:
        """
        Équation (37) - Pondération Temporelle

        weight_i = decay_factor^(n-i) pour i = 1..n

        Adaptation BCT : Poids décroissant pour historique ancien
        """
        n = len(values)
        weights = [decay_factor ** (n - i - 1) for i in range(n)]

        # Normaliser les poids
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]

        return [v * w for v, w in zip(values, weights)]

    def equation_38_ensemble_prediction(self, predictions: List[List[str]],
                                      weights: List[float] = None) -> str:
        """
        Équation (38) - Prédiction Ensemble

        prediction_final = argmax_c Σ w_i * P_i(c)

        Adaptation BCT : Consensus pondéré des 3 rollouts
        """
        if not predictions:
            return 'S'  # Défaut

        if weights is None:
            weights = [1.0] * len(predictions)

        # Compter votes pondérés
        vote_counts = {'S': 0.0, 'O': 0.0}

        for pred_list, weight in zip(predictions, weights):
            for pred in pred_list:
                if pred in vote_counts:
                    vote_counts[pred] += weight

        # Retourner prédiction majoritaire
        return max(vote_counts, key=vote_counts.get)

    def equation_39_uncertainty_quantification(self, predictions: List[str]) -> float:
        """
        Équation (39) - Quantification Incertitude

        uncertainty = entropy(prediction_distribution)

        Adaptation BCT : Mesure incertitude prédictions
        """
        if not predictions:
            return 1.0  # Incertitude maximale

        from collections import Counter
        counts = Counter(predictions)
        total = len(predictions)

        # Calculer entropie
        entropy = 0.0
        for count in counts.values():
            prob = count / total
            if prob > 0:
                entropy -= prob * np.log2(prob)

        # Normaliser par entropie maximale
        max_entropy = np.log2(len(counts)) if len(counts) > 1 else 0
        return entropy / max_entropy if max_entropy > 0 else 0.0

    def equation_40_performance_tracking(self, current_performance: float,
                                       history: List[float], window_size: int = 20) -> Dict[str, float]:
        """
        Équation (40) - Suivi Performance

        metrics = {trend, volatility, improvement}

        Adaptation BCT : Métriques évolution performance
        """
        updated_history = history + [current_performance]
        if len(updated_history) > window_size:
            updated_history = updated_history[-window_size:]

        if len(updated_history) < 2:
            return {'trend': 0.0, 'volatility': 0.0, 'improvement': 0.0}

        # Tendance (régression linéaire simple)
        x = list(range(len(updated_history)))
        y = updated_history
        n = len(x)

        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(xi * yi for xi, yi in zip(x, y))
        sum_x2 = sum(xi * xi for xi in x)

        trend = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x) if n * sum_x2 - sum_x * sum_x != 0 else 0.0

        # Volatilité (écart-type)
        volatility = np.std(updated_history)

        # Amélioration (différence première/dernière)
        improvement = updated_history[-1] - updated_history[0]

        return {
            'trend': trend,
            'volatility': volatility,
            'improvement': improvement,
            'current': current_performance,
            'average': np.mean(updated_history)
        }

    # ========================================================================
    # ÉQUATIONS SPÉCIALISÉES AZR (41-50) - COMPLÉTION FINALE
    # ========================================================================

    def equation_41_cognitive_complexity_difference(self, propose_complexity: float,
                                                   solve_complexity: float) -> float:
        """
        Équation (41) - Différence Complexité Cognitive

        complexipy(p_π_propose) - complexipy(p_π_solve) = 0.27

        Adaptation BCT : Mesure différence cognitive entre rôles
        """
        return propose_complexity - solve_complexity

    def equation_42_task_specialization(self, task_type: str) -> str:
        """
        Équation (42) - Spécialisation Tâches

        induction → (f), deduction → (o), abduction → (i)

        Adaptation BCT : Mapping types de tâches vers objectifs
        """
        task_mapping = {
            'pattern_analysis': 'f',    # Analyser fonction/pattern
            'sequence_prediction': 'o', # Prédire sortie/résultat
            'reverse_engineering': 'i'  # Retrouver entrée/cause
        }
        return task_mapping.get(task_type, 'f')

    def equation_43_monte_carlo_detailed(self, solver_rewards: List[float]) -> float:
        """
        Équation (43) - Moyenne Monte Carlo Détaillée

        r̄_solve = (1/n) Σ_{i=1}^N r_solve^(i)

        Adaptation BCT : Calcul précis taux de succès
        """
        if not solver_rewards:
            return 0.0
        return np.mean(solver_rewards)

    def equation_44_numerical_constraints(self, x: int, y: int) -> bool:
        """
        Équation (44) - Contraintes Numériques

        1 < x < y, x + y ≤ 100

        Adaptation BCT : Validation contraintes patterns
        """
        return 1 < x < y and x + y <= 100

    def equation_45_valid_pair_example(self) -> Tuple[int, int]:
        """
        Équation (45) - Exemple Paire Valide

        (x, y) = (4, 13)

        Adaptation BCT : Exemple de paire satisfaisant contraintes
        """
        return (4, 13)

    def equation_46_invalidity_conditions(self, x: int, y: int) -> bool:
        """
        Équation (46) - Conditions d'Invalidité

        x ≤ 1 or y ≤ 1 or y ≤ x or (x + y) > 100

        Adaptation BCT : Détection paires invalides
        """
        return x <= 1 or y <= 1 or y <= x or (x + y) > 100

    def equation_47_composite_function_detailed(self, functions: List[str], input_val: Any) -> Any:
        """
        Équation (47) - Fonction Composite Détaillée

        f(g_0, ..., g_c, i)

        Adaptation BCT : Composition avancée de patterns
        """
        result = input_val
        for func in functions:
            result = self._simulate_pattern_execution(func, result)
        return result

    def equation_48_non_trivial_composition_constraint(self, original_result: Any,
                                                     composed_result: Any) -> bool:
        """
        Équation (48) - Contrainte Composition Non-Triviale

        f(g(x)) ≠ g(x)

        Adaptation BCT : Éviter compositions triviales
        """
        return original_result != composed_result

    def equation_49_detailed_binary_decision(self, probability: float = 0.5) -> int:
        """
        Équation (49) - Décision Binaire Détaillée

        decision ~ Binomial(p=0.5)

        Adaptation BCT : Choix binaire avec résultat explicite
        """
        return 1 if np.random.random() < probability else 0

    def equation_50_uniform_sampling_composition(self, min_val: int = 1, max_val: int = 3) -> int:
        """
        Équation (50) - Sampling Uniforme Composition

        c ~ U(1,3)

        Adaptation BCT : Contrôle complexité compositions
        """
        return np.random.randint(min_val, max_val + 1)

    # ========================================================================
    # MÉTHODES UTILITAIRES POUR ÉQUATIONS AZR
    # ========================================================================

    def _simulate_pattern_execution(self, pattern: str, input_val: Any) -> Any:
        """Simule l'exécution d'un pattern pour génération entrée-sortie"""
        # Simulation simple basée sur le pattern
        if 'impair_5' in pattern:
            return 'O'  # Changement probable
        elif 'pair_4' in pattern or 'pair_6' in pattern:
            return 'S'  # Continuité probable
        else:
            return np.random.choice(['S', 'O'])

    def get_equations_by_rollout(self) -> Dict[str, List[str]]:
        """
        Classification des 50 équations AZR par rollout selon l'architecture BCT

        ARCHITECTURE 3 ROLLOUTS :
        - ROLLOUT 1 (ANALYZER) : 20 équations - Majeure partie du travail
        - ROLLOUT 2 (GENERATOR) : 15 équations - Génération et hypothèses
        - ROLLOUT 3 (PREDICTOR) : 10 équations - Prédiction et consensus
        - COMMUNES : 5 équations - Partagées entre rollouts
        """
        return {
            'ROLLOUT_1_ANALYZER': [
                'equation_1',   # SFT Loss pour évaluation analyses
                'equation_2',   # RLVR Objective pour patterns
                'equation_4',   # Learnability Reward (auto-curriculum)
                'equation_7',   # Validation programmes déterministes
                'equation_8',   # TRR++ normalisation spécialisée
                'equation_16',  # Estimation Monte Carlo
                'equation_17',  # Calcul précision
                'equation_18',  # Métrique diversité
                'equation_19',  # Score complexité cognitive
                'equation_20',  # Score combiné
                'equation_34',  # Estimation confiance
                'equation_36',  # Similarité patterns
                'equation_37',  # Pondération temporelle
                'equation_40',  # Suivi performance
                'equation_41',  # Différence complexité cognitive
                'equation_42',  # Spécialisation tâches
                'equation_43',  # Monte Carlo détaillé
                'equation_44',  # Contraintes numériques
                'equation_45',  # Exemple paire valide
                'equation_46'   # Conditions invalidité
            ],

            'ROLLOUT_2_GENERATOR': [
                'equation_9',   # Sampling programmes simples
                'equation_10',  # Sampling programmes composites
                'equation_11',  # Décision binaire
                'equation_12',  # Comptage uniforme compositions
                'equation_13',  # Génération entrée-sortie
                'equation_14',  # Sampling K exemples
                'equation_15',  # Conditionnement tâche
                'equation_31',  # Gestion buffer tâches
                'equation_32',  # Progression curriculum
                'equation_33',  # Balance exploration/exploitation
                'equation_47',  # Fonction composite détaillée
                'equation_48',  # Contrainte composition non-triviale
                'equation_49',  # Décision binaire détaillée
                'equation_50',  # Sampling uniforme composition
                'equation_35'   # Taux apprentissage adaptatif
            ],

            'ROLLOUT_3_PREDICTOR': [
                'equation_5',   # Solver Reward (prédiction S/O)
                'equation_6',   # Récompense composite
                'equation_38',  # Prédiction ensemble (consensus)
                'equation_39',  # Quantification incertitude
                'equation_21',  # PPO avec clipping
                'equation_22',  # Ratio probabilité
                'equation_23',  # Normalisation avantages
                'equation_26',  # Régularisation entropie
                'equation_27',  # Loss fonction de valeur
                'equation_28'   # Loss politique
            ],

            'COMMUNES_TOUS_ROLLOUTS': [
                'equation_3',   # Objectif principal AZR (ÉQUATION MAÎTRESSE)
                'equation_24',  # Planification taux apprentissage
                'equation_25',  # Clipping gradients
                'equation_29',  # Loss totale combinée
                'equation_30'   # Mise à jour buffer
            ]
        }

    def get_all_equations_summary(self) -> Dict[str, str]:
        """
        Retourne un résumé de TOUTES les équations AZR implémentées (50/50)

        INNOVATION : Premier moteur mathématique complet AZR pour Baccarat
        COMPLÉTUDE : 100% - Toutes les équations du modèle AZR adaptées
        ARCHITECTURE : Organisées selon les 3 rollouts spécialisés
        """
        return {
            # Équations principales (1-8) - CŒUR DU SYSTÈME
            'equation_1': 'SFT Loss adapté Baccarat',
            'equation_2': 'RLVR Objective prédictions S/O',
            'equation_3': 'Objectif principal AZR-BCT (ÉQUATION MAÎTRESSE)',
            'equation_4': 'Learnability Reward (Zone Goldilocks - INNOVATION CLÉE)',
            'equation_5': 'Solver Reward binaire',
            'equation_6': 'Récompense composite formatage',
            'equation_7': 'Validation programmes déterministes',
            'equation_8': 'TRR++ normalisation spécialisée',

            # Sampling et génération (9-15) - AUTO-CURRICULUM
            'equation_9': 'Sampling programmes simples',
            'equation_10': 'Sampling programmes composites',
            'equation_11': 'Décision binaire',
            'equation_12': 'Comptage uniforme compositions',
            'equation_13': 'Génération entrée-sortie',
            'equation_14': 'Sampling K exemples',
            'equation_15': 'Conditionnement tâche',

            # Évaluation et métriques (16-25) - PERFORMANCE
            'equation_16': 'Estimation Monte Carlo',
            'equation_17': 'Calcul précision',
            'equation_18': 'Métrique diversité',
            'equation_19': 'Score complexité cognitive',
            'equation_20': 'Score combiné',
            'equation_21': 'PPO avec clipping',
            'equation_22': 'Ratio probabilité',
            'equation_23': 'Normalisation avantages',
            'equation_24': 'Planification taux apprentissage',
            'equation_25': 'Clipping gradients',

            # Optimisation avancée (26-30) - APPRENTISSAGE
            'equation_26': 'Régularisation entropie',
            'equation_27': 'Loss fonction de valeur',
            'equation_28': 'Loss politique',
            'equation_29': 'Loss totale combinée',
            'equation_30': 'Mise à jour buffer',

            # Spécialisations BCT (31-40) - INNOVATIONS
            'equation_31': 'Gestion buffer tâches',
            'equation_32': 'Progression curriculum automatique',
            'equation_33': 'Balance exploration/exploitation',
            'equation_34': 'Estimation confiance prédictions',
            'equation_35': 'Taux apprentissage adaptatif',
            'equation_36': 'Similarité patterns',
            'equation_37': 'Pondération temporelle',
            'equation_38': 'Prédiction ensemble (consensus rollouts)',
            'equation_39': 'Quantification incertitude',
            'equation_40': 'Suivi performance avancé',

            # Complétion finale (41-50) - SPÉCIALISATIONS AVANCÉES
            'equation_41': 'Différence complexité cognitive',
            'equation_42': 'Spécialisation tâches',
            'equation_43': 'Monte Carlo détaillé',
            'equation_44': 'Contraintes numériques',
            'equation_45': 'Exemple paire valide',
            'equation_46': 'Conditions invalidité',
            'equation_47': 'Fonction composite détaillée',
            'equation_48': 'Contrainte composition non-triviale',
            'equation_49': 'Décision binaire détaillée',
            'equation_50': 'Sampling uniforme composition'
        }

    def get_rollout_specific_equations(self, rollout_id: int) -> List[str]:
        """
        Retourne les équations spécifiques à un rollout donné

        Args:
            rollout_id: 1 (Analyzer), 2 (Generator), 3 (Predictor)

        Returns:
            List[str]: Liste des noms d'équations pour ce rollout
        """
        equations_by_rollout = self.get_equations_by_rollout()

        if rollout_id == 1:
            return equations_by_rollout['ROLLOUT_1_ANALYZER']
        elif rollout_id == 2:
            return equations_by_rollout['ROLLOUT_2_GENERATOR']
        elif rollout_id == 3:
            return equations_by_rollout['ROLLOUT_3_PREDICTOR']
        else:
            return equations_by_rollout['COMMUNES_TOUS_ROLLOUTS']

    def get_rollout_equation_count(self) -> Dict[str, int]:
        """
        Retourne le nombre d'équations par rollout

        RÉPARTITION OPTIMALE :
        - ROLLOUT 1 (ANALYZER) : 20 équations (40% - majeure partie)
        - ROLLOUT 2 (GENERATOR) : 15 équations (30% - génération)
        - ROLLOUT 3 (PREDICTOR) : 10 équations (20% - prédiction)
        - COMMUNES : 5 équations (10% - partagées)
        TOTAL : 50 équations (100%)
        """
        equations_by_rollout = self.get_equations_by_rollout()

        return {
            'ROLLOUT_1_ANALYZER': len(equations_by_rollout['ROLLOUT_1_ANALYZER']),
            'ROLLOUT_2_GENERATOR': len(equations_by_rollout['ROLLOUT_2_GENERATOR']),
            'ROLLOUT_3_PREDICTOR': len(equations_by_rollout['ROLLOUT_3_PREDICTOR']),
            'COMMUNES_TOUS_ROLLOUTS': len(equations_by_rollout['COMMUNES_TOUS_ROLLOUTS']),
            'TOTAL': sum(len(eqs) for eqs in equations_by_rollout.values())
        }

    def validate_equation_classification(self) -> Dict[str, Any]:
        """
        Valide la classification des 50 équations par rollout

        VÉRIFICATIONS :
        1. Toutes les 50 équations sont classées
        2. Aucune équation n'est dupliquée
        3. Répartition équilibrée selon l'architecture
        """
        equations_by_rollout = self.get_equations_by_rollout()
        counts = self.get_rollout_equation_count()

        # Collecter toutes les équations classées
        all_classified = []
        for rollout_equations in equations_by_rollout.values():
            all_classified.extend(rollout_equations)

        # Vérifier complétude (50 équations)
        expected_equations = [f'equation_{i}' for i in range(1, 51)]
        missing_equations = set(expected_equations) - set(all_classified)
        duplicate_equations = [eq for eq in all_classified if all_classified.count(eq) > 1]

        # Vérifier répartition
        is_balanced = (
            counts['ROLLOUT_1_ANALYZER'] >= 15 and  # Analyzer fait la majeure partie
            counts['ROLLOUT_2_GENERATOR'] >= 10 and  # Generator génère
            counts['ROLLOUT_3_PREDICTOR'] >= 8 and   # Predictor prédit
            counts['COMMUNES_TOUS_ROLLOUTS'] >= 3     # Quelques communes
        )

        return {
            'total_classified': len(all_classified),
            'expected_total': 50,
            'is_complete': len(missing_equations) == 0,
            'missing_equations': list(missing_equations),
            'has_duplicates': len(duplicate_equations) > 0,
            'duplicate_equations': duplicate_equations,
            'is_balanced': is_balanced,
            'counts_by_rollout': counts,
            'validation_passed': (
                len(missing_equations) == 0 and
                len(duplicate_equations) == 0 and
                is_balanced and
                len(all_classified) == 50
            )
        }

@dataclass
class BaccaratHand:
    """
    Structure de données pour une MAIN de Baccarat
    
    Conforme à systeme_comptage_baccarat_complet.txt
    Distinction claire MAIN vs MANCHE
    """
    
    # Identification
    hand_number: int                    # Numéro de main (incluant TIE)
    pb_hand_number: Optional[int]       # Numéro de manche P/B (None si TIE)
    
    # INDEX 1 : Comptage cartes distribuées
    cards_distributed: int              # 4, 5, ou 6 cartes
    cards_parity: str                   # 'PAIR' ou 'IMPAIR'
    cards_category: str                 # 'pair_4', 'pair_6', 'impair_5'
    
    # INDEX 2 : État SYNC/DESYNC
    sync_state: str                     # 'SYNC' ou 'DESYNC'

    # INDEX 3 : Résultat P/B uniquement
    result: str                         # 'PLAYER', 'BANKER', '--' (-- pour TIE)

    # INDEX 4 : Conversion S/O
    so_conversion: str                  # 'S', 'O', '--' (-- pour TIE ou première manche)
    
    # Métadonnées
    timestamp: datetime = field(default_factory=datetime.now)

    @property
    def combined_state(self) -> str:
        """
        Calcule l'état combiné à la demande (ancien INDEX 3)
        Combine INDEX 1 (cards_category) + INDEX 2 (sync_state)

        Returns:
            str: État combiné comme 'pair_4_sync', 'impair_5_desync', etc.
        """
        return f"{self.cards_category}_{self.sync_state.lower()}"

    def is_pb_hand(self) -> bool:
        """Vérifie si c'est une manche P/B (pas TIE)"""
        return self.result in ['PLAYER', 'BANKER']

    def is_tie_hand(self) -> bool:
        """Vérifie si c'est un TIE"""
        return self.result == '--'

    def is_burn_hand(self) -> bool:
        """Vérifie si c'est la main de brûlage (main 0)"""
        return self.hand_number == 0

@dataclass  
class BaccaratGame:
    """
    Structure de données pour une partie complète de Baccarat
    
    Fenêtre de 60 manches P/B maximum (2^60 possibilités)
    """
    
    # Identification
    game_number: int
    
    # Initialisation
    burn_cards_count: int               # 2-11 cartes brûlées
    burn_parity: str                    # 'PAIR' ou 'IMPAIR'
    initial_sync_state: str             # 'SYNC' ou 'DESYNC'
    
    # Données de la partie
    hands: List[BaccaratHand] = field(default_factory=list)
    
    # Statistiques
    total_hands: int = 0                # Toutes les mains (incluant TIE)
    pb_hands: int = 0                   # Manches P/B seulement
    tie_hands: int = 0                  # TIE seulement
    so_conversions: int = 0             # Conversions S/O calculées
    
    # État actuel
    current_sync_state: str = 'SYNC'
    last_pb_result: Optional[str] = None
    
    def add_hand(self, hand: BaccaratHand):
        """Ajoute une main à la partie avec mise à jour des statistiques"""
        self.hands.append(hand)

        # Ne pas compter la main 0 (brûlage) dans les statistiques
        if not hand.is_burn_hand():
            self.total_hands += 1

            if hand.result in ['PLAYER', 'BANKER']:
                self.pb_hands += 1
                if hand.so_conversion in ['S', 'O']:
                    self.so_conversions += 1
            elif hand.result == '--':
                self.tie_hands += 1
    
    def is_complete(self, max_pb_hands: int = 60) -> bool:
        """Vérifie si la partie est complète (60 manches P/B)"""
        return self.pb_hands >= max_pb_hands
    
    def get_pb_sequence(self) -> List[str]:
        """Retourne la séquence P/B (sans TIE)"""
        return [hand.result for hand in self.hands if hand.result in ['PLAYER', 'BANKER']]
    
    def get_so_sequence(self) -> List[str]:
        """Retourne la séquence S/O (sans '--')"""
        return [hand.so_conversion for hand in self.hands
                if hand.so_conversion in ['S', 'O']]

    # ========================================================================
    # MÉTHODES D'ACCÈS ENRICHI POUR ALGORITHMES AVANCÉS
    # Exploitation complète des influences TIE
    # ========================================================================

    def get_full_influence_sequence(self) -> List[Dict[str, Any]]:
        """
        Retourne toutes les influences (P, B, T) pour algorithmes avancés

        CRUCIAL : Permet aux algorithmes d'exploiter les INDEX 1&2 des TIE
        pour enrichir les prédictions S/O
        """
        return [{
            'hand_number': hand.hand_number,
            'result': hand.result,
            'cards_category': hand.cards_category,
            'cards_parity': hand.cards_parity,
            'sync_state': hand.sync_state,
            'combined_state': hand.combined_state,
            'so_conversion': hand.so_conversion,
            'is_tie': hand.result == '--',
            'is_pb': hand.result in ['PLAYER', 'BANKER'],
            'timestamp': hand.timestamp
        } for hand in self.hands]

    def get_sync_pattern_complete(self) -> List[str]:
        """
        Pattern SYNC/DESYNC incluant TIE

        ESSENTIEL : Continuité des états SYNC/DESYNC même avec TIE intermédiaires
        """
        return [hand.sync_state for hand in self.hands]

    def get_combined_states_all(self) -> List[str]:
        """
        États combinés de toutes les mains (P, B, T)

        RICHESSE : Séquence complète pour détection patterns avancés
        """
        return [hand.combined_state for hand in self.hands]

    def get_parity_pattern_complete(self) -> List[str]:
        """
        Pattern parité cartes incluant TIE

        ASYMÉTRIE : Détection impair_5 même dans TIE
        """
        return [hand.cards_parity for hand in self.hands]

    def get_algorithmic_summary(self) -> Dict[str, Any]:
        """
        Résumé complet pour algorithmes de prédiction

        EXPLOITATION MAXIMALE : Toutes les données nécessaires aux prédictions
        """
        return {
            # Séquences complètes (P, B, T)
            'full_influences': self.get_full_influence_sequence(),
            'sync_pattern': self.get_sync_pattern_complete(),
            'combined_states': self.get_combined_states_all(),
            'parity_pattern': self.get_parity_pattern_complete(),

            # Séquences filtrées (P, B seulement)
            'pb_sequence': self.get_pb_sequence(),
            'so_sequence': self.get_so_sequence(),

            # Statistiques
            'total_hands': self.total_hands,
            'pb_hands': self.pb_hands,
            'tie_hands': self.tie_hands,
            'last_pb_result': self.last_pb_result,
            'current_sync_state': self.current_sync_state,

            # Contexte pour prédictions
            'recent_influences': self.get_full_influence_sequence()[-10:],  # 10 dernières
            'recent_sync_pattern': self.get_sync_pattern_complete()[-10:],
            'recent_combined_states': self.get_combined_states_all()[-10:]
        }

################################################################################
#                                                                              #
#  ⚙️ SECTION 3 : MOTEUR DE COMPTAGE OPÉRATIONNEL                              #
#                                                                              #
################################################################################

class BaccaratCountingEngine:
    """
    Moteur de comptage conforme à systeme_comptage_baccarat_complet.txt

    Calcule les 5 INDEX de comptage pour chaque main
    OPÉRATIONNEL et TESTÉ
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.CountingEngine")

    def calculate_cards_distributed(self, total_cards: int, cards_category: str = None) -> Tuple[int, str, str]:
        """
        Calcule INDEX 1 : nombre de cartes distribuées et catégorie

        Args:
            total_cards: Nombre total de cartes distribuées (4, 5, ou 6)
            cards_category: Catégorie pré-calculée (optionnel)

        Returns:
            Tuple[total_cards, parity, category]
        """
        if total_cards % 2 == 0:
            parity = 'PAIR'
            if total_cards == 4:
                category = 'pair_4'
            elif total_cards == 6:
                category = 'pair_6'
            else:
                # Cas exceptionnels (ne devrait pas arriver pour les mains normales)
                category = f'pair_{total_cards}'
        else:
            parity = 'IMPAIR'
            if total_cards == 5:
                category = 'impair_5'
            else:
                # Cas exceptionnels
                category = f'impair_{total_cards}'

        # Utiliser la catégorie pré-calculée si fournie
        if cards_category:
            category = cards_category

        return total_cards, parity, category

    def create_burn_hand(self, burn_cards_count: int, burn_parity: str, initial_sync_state: str) -> BaccaratHand:
        """
        Crée la main 0 (brûlage) avec seulement INDEX 1 et INDEX 2 renseignés

        Args:
            burn_cards_count: Nombre de cartes brûlées (2-11)
            burn_parity: Parité du brûlage ('PAIR' ou 'IMPAIR')
            initial_sync_state: État initial ('SYNC' ou 'DESYNC')

        Returns:
            BaccaratHand: Main 0 avec INDEX 1&2 seulement
        """
        # INDEX 1 : Catégorie de brûlage selon le nombre de cartes
        if burn_parity == 'PAIR':
            if burn_cards_count in [2, 4, 6, 8, 10]:
                cards_category = f'pair_{burn_cards_count}'
            else:
                cards_category = 'pair_4'  # Défaut si nombre invalide
        else:  # IMPAIR
            if burn_cards_count in [3, 5, 7, 9, 11]:
                cards_category = f'impair_{burn_cards_count}'
            else:
                cards_category = 'impair_5'  # Défaut si nombre invalide

        # Créer la main 0 (brûlage)
        burn_hand = BaccaratHand(
            hand_number=0,                    # Main 0
            pb_hand_number=0,                 # Manche 0 (brûlage)
            cards_distributed=burn_cards_count,  # Nombre de cartes brûlées
            cards_parity=burn_parity,         # INDEX 1 : Parité
            cards_category=cards_category,    # INDEX 1 : Catégorie
            sync_state=initial_sync_state,    # INDEX 2 : État initial
            result='',                        # INDEX 3 : Vide
            so_conversion=''                  # INDEX 4 : Vide
        )

        self.logger.debug(f"Main de brûlage créée: {cards_category}_{initial_sync_state}")
        return burn_hand

    def calculate_sync_state(self, current_sync_state: str, cards_parity: str) -> str:
        """
        Calcule INDEX 2 : nouvel état SYNC/DESYNC

        LOGIQUE :
        - Nombre PAIR de cartes → CONSERVE l'état
        - Nombre IMPAIR de cartes → CHANGE l'état
        """
        if cards_parity == 'PAIR':
            # PAIR conserve l'état
            return current_sync_state
        else:
            # IMPAIR change l'état
            return 'DESYNC' if current_sync_state == 'SYNC' else 'SYNC'

    def calculate_so_conversion(self, current_result: str, last_pb_result: Optional[str]) -> str:
        """
        Calcule INDEX 4 : conversion S/O (ancien INDEX 5)

        LOGIQUE :
        - S (Same) : Même résultat que la dernière manche P/B
        - O (Opposite) : Résultat opposé à la dernière manche P/B
        - '--' : Première manche P/B ou TIE
        """
        if current_result == 'TIE':
            return '--'  # TIE n'a pas de conversion S/O

        if last_pb_result is None:
            return '--'  # Première manche P/B

        if current_result == last_pb_result:
            return 'S'   # Same
        else:
            return 'O'   # Opposite

    def process_hand(self, game: BaccaratGame, result: str,
                    total_cards: int, cards_category: str) -> BaccaratHand:
        """
        Traite une main complète et calcule tous les index

        MÉTHODE UNIVERSELLE : Utilisée par tous les clusters

        Args:
            game: Partie en cours
            result: 'PLAYER', 'BANKER', 'TIE'
            total_cards: Nombre total de cartes distribuées (4, 5, ou 6)
            cards_category: Catégorie pré-calculée ('pair_4', 'impair_5', 'pair_6')
        """
        # INDEX 1 : Comptage cartes
        total_cards, cards_parity, cards_category = self.calculate_cards_distributed(
            total_cards, cards_category
        )

        # INDEX 2 : Nouvel état SYNC/DESYNC
        new_sync_state = self.calculate_sync_state(
            game.current_sync_state, cards_parity
        )

        # INDEX 4 : Conversion S/O (ancien INDEX 5)
        so_conversion = self.calculate_so_conversion(result, game.last_pb_result)

        # Numéro de manche selon la logique correcte
        pb_hand_number = None
        if result in ['PLAYER', 'BANKER']:
            # P/B : incrémente le compteur de manches
            pb_hand_number = game.pb_hands + 1
        elif result == 'TIE':
            # TIE : garde le numéro de la dernière manche P/B (MÊME manche)
            if game.pb_hands > 0:
                pb_hand_number = game.pb_hands  # MÊME numéro que la dernière manche P/B
            else:
                pb_hand_number = 0  # Si pas encore de P/B, reste à 0 (comme brûlage)

        # Créer la main (numérotation commence à 1 car main 0 = brûlage)
        # Si main 0 (brûlage) existe, les mains suivantes sont 1, 2, 3...
        hand_number = len(game.hands)  # game.hands[0] = brûlage, donc len = 1 → main 1

        # INDEX 3 et 4 : Modification pour TIE
        if result == 'TIE':
            result_index3 = '--'
            so_conversion_index4 = '--'
        else:
            result_index3 = result
            so_conversion_index4 = so_conversion

        hand = BaccaratHand(
            hand_number=hand_number,
            pb_hand_number=pb_hand_number,
            cards_distributed=total_cards,
            cards_parity=cards_parity,
            cards_category=cards_category,
            sync_state=new_sync_state,
            result=result_index3,
            so_conversion=so_conversion_index4
        )

        # Mettre à jour l'état du jeu
        game.current_sync_state = new_sync_state
        if result in ['PLAYER', 'BANKER']:
            game.last_pb_result = result

        # Ajouter la main au jeu
        game.add_hand(hand)

        self.logger.debug(f"Main traitée: {result} {hand.cards_distributed} cartes -> {hand.cards_category}_{hand.sync_state}_{hand.result} {hand.so_conversion}")

        return hand

################################################################################
#                                                                              #
#  🎯 SECTION 4 : OSSATURE ROLLOUTS (STRUCTURE SEULEMENT)                     #
#                                                                              #
################################################################################

class UniversalRollout:
    """
    Classe de base pour tous les rollouts BCT

    OSSATURE SEULEMENT - Méthodes à implémenter plus tard
    """

    def __init__(self, rollout_id: int, config: AZRConfig):
        self.rollout_id = rollout_id
        self.config = config
        self.rollout_params = config.get_rollout_params(rollout_id)
        self.logger = logging.getLogger(f"{__name__}.BCT.Rollout{rollout_id}")

    def get_rollout_name(self) -> str:
        """Retourne le nom du rollout pour identification"""
        rollout_names = {1: 'Analyzer', 2: 'Generator', 3: 'Predictor'}
        return f"BCT_{rollout_names.get(self.rollout_id, f'Rollout{self.rollout_id}')}"

class AnalyzerRollout(UniversalRollout):
    """
    ROLLOUT 1 : ANALYSEUR BCT - IMPLÉMENTATION AZR COMPLÈTE

    Équivalent du rôle "Proposer" d'AZR adapté au Baccarat
    Génère des tâches de prédiction optimales (Équation 3 & 4)
    """

    def __init__(self, rollout_id: int, config: AZRConfig):
        super().__init__(1, config)  # Toujours rollout_id = 1
        self.math_engine = AZRMathEngine(config)
        self.task_buffer = AZRBuffer(AZRTaskType.PATTERN_ANALYSIS)
        self.reward_history = {'pattern_analysis_propose': []}

    def analyze_game_state(self, game: BaccaratGame) -> Dict[str, Any]:
        """
        IMPLÉMENTATION AZR : Analyse l'état et propose des tâches optimales

        Basé sur le rôle "Proposer" d'AZR (Équations 3 & 4)
        """
        start_time = datetime.now()

        # Obtenir données complètes pour analyse
        algorithmic_data = game.get_algorithmic_summary()

        # 1. ANALYSE DES PATTERNS (Équivalent Induction AZR)
        pattern_analysis = self._analyze_patterns(algorithmic_data)

        # 2. DÉTECTION DE BIAIS STRUCTURELS
        bias_detection = self._detect_structural_bias(algorithmic_data)

        # 3. GÉNÉRATION DE TÂCHES OPTIMALES (Cœur AZR)
        proposed_tasks = self._propose_optimal_tasks(algorithmic_data)

        # 4. CALCUL LEARNABILITY REWARDS (Équation 4)
        learnability_scores = self._calculate_learnability_scores(proposed_tasks)

        # 5. SÉLECTION TÂCHE OPTIMALE (Zone Goldilocks)
        optimal_task = self._select_optimal_task(proposed_tasks, learnability_scores)

        processing_time = (datetime.now() - start_time).total_seconds() * 1000

        return {
            'rollout_id': self.rollout_id,
            'rollout_name': self.get_rollout_name(),
            'status': 'azr_implemented',

            # Résultats d'analyse AZR
            'pattern_analysis': pattern_analysis,
            'bias_detection': bias_detection,
            'proposed_tasks': len(proposed_tasks),
            'optimal_task': optimal_task,
            'learnability_scores': learnability_scores,

            # Métriques de performance
            'processing_time_ms': processing_time,
            'confidence_level': self._calculate_confidence(algorithmic_data),

            # Données enrichies (exploitation TIE)
            'data_exploitation': {
                'total_influences': len(algorithmic_data['full_influences']),
                'pb_hands': algorithmic_data['pb_hands'],
                'tie_hands': algorithmic_data['tie_hands'],
                'tie_exploitation_score': self._calculate_tie_exploitation(algorithmic_data),
                'sync_pattern_continuity': len(algorithmic_data['sync_pattern']),
                'combined_states_richness': len(set(algorithmic_data['combined_states']))
            },

            'timestamp': datetime.now()
        }

    def _analyze_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyse des patterns avec ordre de priorité (impair_5 > pair_6 > pair_4)"""
        patterns = {
            'pair_4_frequency': 0,      # PRIORITÉ 3
            'impair_5_frequency': 0,    # PRIORITÉ 1 (commutateur d'état)
            'pair_6_frequency': 0,      # PRIORITÉ 2
            'priority_score': 0.0
        }

        # Analyser les états combinés
        combined_states = data.get('combined_states', [])
        for state in combined_states:
            if 'pair_4' in state:
                patterns['pair_4_frequency'] += 1
                patterns['priority_score'] += 1  # PRIORITÉ 3
            elif 'impair_5' in state:
                patterns['impair_5_frequency'] += 1
                patterns['priority_score'] += 3  # PRIORITÉ 1 (maximale)
            elif 'pair_6' in state:
                patterns['pair_6_frequency'] += 1
                patterns['priority_score'] += 2  # PRIORITÉ 2

        return patterns

    def _detect_structural_bias(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Détection de biais structurels avec sensibilité accrue"""
        bias_threshold = self.rollout_params.get('bias_detection_sensitivity', 1.5)

        # Analyser les séquences P/B
        pb_sequence = data.get('pb_sequence', [])
        if len(pb_sequence) < 5:
            return {'bias_detected': False, 'confidence': 0.0}

        # Calculer déviations par rapport à l'équilibre
        player_ratio = pb_sequence.count('PLAYER') / len(pb_sequence)
        banker_ratio = pb_sequence.count('BANKER') / len(pb_sequence)

        # Détecter biais significatif
        bias_score = abs(player_ratio - 0.5) * bias_threshold

        return {
            'bias_detected': bias_score > 0.1,
            'bias_score': bias_score,
            'player_ratio': player_ratio,
            'banker_ratio': banker_ratio,
            'confidence': min(bias_score * 2, 1.0)
        }

    def _propose_optimal_tasks(self, data: Dict[str, Any]) -> List[AZRTask]:
        """Génère des tâches de prédiction optimales (Cœur AZR)"""
        tasks = []

        # Obtenir séquences récentes
        recent_pb = data.get('pb_sequence', [])[-10:]  # 10 dernières
        recent_so = data.get('so_sequence', [])[-10:]

        if len(recent_pb) < 3:
            return tasks  # Pas assez de données

        # Tâche 1 : Prédiction S/O basée sur pattern P/B
        task1 = AZRTask(
            task_id=f"so_prediction_{len(tasks)+1}",
            task_type=AZRTaskType.PATTERN_ANALYSIS,
            input_sequence=recent_pb[:-1],  # Tout sauf le dernier
            input_metadata={
                'sync_pattern': data.get('recent_sync_pattern', []),
                'combined_states': data.get('recent_combined_states', [])
            },
            target_prediction=recent_so[-1] if recent_so else 'S',
            target_confidence=0.7,
            difficulty_level=0.5,  # Zone optimale
            complexity_score=self.math_engine.calculate_complexity_score(
                recent_pb, data
            )
        )
        tasks.append(task1)

        # Tâche 2 : Prédiction basée sur états SYNC/DESYNC
        if data.get('recent_sync_pattern'):
            task2 = AZRTask(
                task_id=f"sync_prediction_{len(tasks)+1}",
                task_type=AZRTaskType.SEQUENCE_PREDICTION,
                input_sequence=data['recent_sync_pattern'][:-1],
                input_metadata={'pb_context': recent_pb},
                target_prediction=data['recent_sync_pattern'][-1],
                target_confidence=0.6,
                difficulty_level=0.4,
                complexity_score=0.3
            )
            tasks.append(task2)

        return tasks

    def _calculate_learnability_scores(self, tasks: List[AZRTask]) -> List[float]:
        """Calcule les scores de learnability (Équation 4 AZR)"""
        scores = []

        for task in tasks:
            # Simuler le taux de succès basé sur la complexité
            estimated_success_rate = max(0.1, 1.0 - task.complexity_score)

            # Appliquer Équation 4 d'AZR
            learnability = self.math_engine.equation_4_learnability_reward(
                estimated_success_rate
            )

            scores.append(learnability)

        return scores

    def _select_optimal_task(self, tasks: List[AZRTask],
                           scores: List[float]) -> Optional[AZRTask]:
        """Sélectionne la tâche avec le meilleur score de learnability"""
        if not tasks or not scores:
            return None

        # Trouver l'index du score maximum
        max_index = np.argmax(scores)
        optimal_task = tasks[max_index]

        # Ajouter au buffer pour référence future
        self.task_buffer.add_task(optimal_task)

        return optimal_task

    def _calculate_confidence(self, data: Dict[str, Any]) -> float:
        """Calcule la confiance globale de l'analyse"""
        factors = []

        # Facteur 1 : Quantité de données (SANS CAP ARBITRAIRE)
        total_hands = data.get('total_hands', 0)
        data_factor = total_hands / 20.0 if total_hands > 0 else 0.0
        factors.append(data_factor)

        # Facteur 2 : Richesse des patterns
        combined_states = data.get('combined_states', [])
        pattern_richness = len(set(combined_states)) / 6.0  # 6 états possibles
        factors.append(pattern_richness)

        # Facteur 3 : Exploitation TIE
        tie_factor = self._calculate_tie_exploitation(data)
        factors.append(tie_factor)

        return np.mean(factors)

    def _calculate_tie_exploitation(self, data: Dict[str, Any]) -> float:
        """Calcule le score d'exploitation des TIE (innovation BCT)"""
        tie_hands = data.get('tie_hands', 0)
        total_hands = data.get('total_hands', 1)

        # Plus de TIE = plus d'informations exploitables
        tie_ratio = tie_hands / total_hands

        # Score d'exploitation basé sur la richesse des données TIE (SANS CAP)
        return tie_ratio * 2.0  # Facteur d'amplification sans cap arbitraire

class GeneratorRollout(UniversalRollout):
    """
    ROLLOUT 2 : GÉNÉRATEUR BCT - IMPLÉMENTATION AZR COMPLÈTE

    Équivalent du rôle "Solver" d'AZR pour génération de séquences
    Génère des séquences candidates optimales (Équation 3 & 5)
    """

    def __init__(self, rollout_id: int, config: AZRConfig):
        super().__init__(2, config)  # Toujours rollout_id = 2
        self.math_engine = AZRMathEngine(config)
        self.sequence_buffer = AZRBuffer(AZRTaskType.SEQUENCE_PREDICTION)
        self.generation_history = []

    def generate_sequences(self, analysis: Dict[str, Any], game: BaccaratGame) -> Dict[str, Any]:
        """
        IMPLÉMENTATION AZR : Génère des séquences candidates optimales

        Basé sur le rôle "Solver" d'AZR adapté à la génération de séquences
        """
        start_time = datetime.now()

        # Obtenir données complètes incluant TIE
        algorithmic_data = game.get_algorithmic_summary()

        # 1. GÉNÉRATION DE SÉQUENCES MULTIPLES (Diversité AZR)
        candidate_sequences = self._generate_candidate_sequences(algorithmic_data)

        # 2. EXPLOITATION PATTERNS TERNAIRES (pair_4, impair_5, pair_6)
        ternary_patterns = self._exploit_ternary_patterns(algorithmic_data)

        # 3. OPTIMISATION SÉQUENCES (Ordre de priorité)
        optimized_sequences = self._optimize_sequences(candidate_sequences, ternary_patterns)

        # 4. ÉVALUATION QUALITÉ (Équation 5 adaptée)
        sequence_scores = self._evaluate_sequence_quality(optimized_sequences, algorithmic_data)

        # 5. SÉLECTION FINALE (Meilleure séquence)
        best_sequence = self._select_best_sequence(optimized_sequences, sequence_scores)

        processing_time = (datetime.now() - start_time).total_seconds() * 1000

        return {
            'rollout_id': self.rollout_id,
            'rollout_name': self.get_rollout_name(),
            'status': 'azr_implemented',

            # Résultats de génération AZR
            'candidate_sequences': candidate_sequences,
            'ternary_patterns': ternary_patterns,
            'optimized_sequences': optimized_sequences,
            'best_sequence': best_sequence,
            'sequence_scores': sequence_scores,

            # Métriques de performance
            'processing_time_ms': processing_time,
            'generation_diversity': self._calculate_diversity(candidate_sequences),
            'sequence_optimization_score': np.mean(sequence_scores) if sequence_scores else 0.0,

            # Exploitation TIE enrichie
            'tie_exploitation': {
                'full_sequence_length': len(algorithmic_data['full_influences']),
                'sync_continuity': algorithmic_data['sync_pattern'],
                'combined_states_all': algorithmic_data['combined_states'],
                'tie_pattern_extraction': self._extract_tie_patterns(algorithmic_data),
                'enrichment_factor': self._calculate_enrichment_factor(algorithmic_data)
            },

            'timestamp': datetime.now()
        }

    def _generate_candidate_sequences(self, data: Dict[str, Any]) -> List[List[str]]:
        """Génère plusieurs séquences candidates (Diversité AZR)"""
        sequences = []

        # Obtenir contexte récent
        recent_pb = data.get('pb_sequence', [])[-8:]  # 8 dernières manches
        recent_so = data.get('so_sequence', [])[-8:]

        if len(recent_pb) < 3:
            return sequences

        # Séquence 1 : Continuation pattern P/B
        pb_pattern_seq = self._generate_pb_pattern_sequence(recent_pb)
        sequences.append(pb_pattern_seq)

        # Séquence 2 : Alternance S/O optimisée
        so_alternation_seq = self._generate_so_alternation_sequence(recent_so)
        sequences.append(so_alternation_seq)

        # Séquence 3 : Basée sur états SYNC/DESYNC
        sync_pattern = data.get('recent_sync_pattern', [])
        if sync_pattern:
            sync_based_seq = self._generate_sync_based_sequence(sync_pattern, recent_pb)
            sequences.append(sync_based_seq)

        # Séquence 4 : Exploitation TIE (Innovation BCT)
        tie_exploitation_seq = self._generate_tie_exploitation_sequence(data)
        if tie_exploitation_seq:
            sequences.append(tie_exploitation_seq)

        return sequences

    def _generate_pb_pattern_sequence(self, recent_pb: List[str]) -> List[str]:
        """Génère séquence basée sur patterns P/B"""
        if len(recent_pb) < 3:
            return ['S']  # Défaut

        # Analyser tendance récente
        last_3 = recent_pb[-3:]

        # Pattern de répétition
        if last_3[-1] == last_3[-2]:
            return ['S']  # Continuer la tendance
        else:
            return ['O']  # Changer la tendance

    def _generate_so_alternation_sequence(self, recent_so: List[str]) -> List[str]:
        """Génère séquence basée sur alternance S/O"""
        if not recent_so:
            return ['S']  # Défaut

        last_so = recent_so[-1]

        # Génération basée sur diversité (SANS BOOST ARBITRAIRE)
        diversity_factor = self.rollout_params.get('generation_diversity', 0.8)

        if diversity_factor > 0.7:
            # Favoriser alternance
            return ['O'] if last_so == 'S' else ['S']
        else:
            # Favoriser répétition
            return [last_so]

    def _generate_sync_based_sequence(self, sync_pattern: List[str],
                                    pb_context: List[str]) -> List[str]:
        """Génère séquence basée sur états SYNC/DESYNC"""
        if not sync_pattern or not pb_context:
            return ['S']

        current_sync = sync_pattern[-1]
        last_pb = pb_context[-1] if pb_context else 'PLAYER'

        # Logique basée sur l'état SYNC/DESYNC
        if current_sync == 'SYNC':
            # En SYNC, favoriser la continuité
            return ['S']
        else:
            # En DESYNC, favoriser le changement
            return ['O']

    def _generate_tie_exploitation_sequence(self, data: Dict[str, Any]) -> Optional[List[str]]:
        """Génère séquence exploitant les patterns TIE (Innovation BCT)"""
        full_influences = data.get('full_influences', [])

        if not full_influences:
            return None

        # Analyser les TIE récents
        recent_ties = [inf for inf in full_influences[-10:] if inf.get('is_tie', False)]

        if not recent_ties:
            return ['S']  # Pas de TIE récents

        # Exploiter les INDEX 1&2 des TIE
        tie_states = [tie.get('combined_state', '') for tie in recent_ties]

        # Si beaucoup d'impair_5 dans les TIE (PRIORITÉ 1)
        impair_5_ties = [state for state in tie_states if 'impair_5' in state]

        if len(impair_5_ties) >= 2:
            # Pattern impair_5 détecté → Prédiction forte
            return ['O']  # Changement probable
        else:
            return ['S']  # Continuité probable

    def _exploit_ternary_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Exploite les patterns ternaires BCT (pair_4, impair_5, pair_6)"""
        combined_states = data.get('combined_states', [])

        patterns = {
            'pair_4_sequences': [],
            'impair_5_sequences': [],  # PRIORITÉ 1 (commutateur d'état)
            'pair_6_sequences': [],
            'dominant_pattern': None,
            'significance_score': 0.0
        }

        # Analyser les séquences par type
        current_sequence = []
        current_type = None

        for state in combined_states[-10:]:  # 10 derniers états
            if 'pair_4' in state:
                if current_type != 'pair_4':
                    if current_sequence:
                        patterns[f'{current_type}_sequences'].append(current_sequence)
                    current_sequence = [state]
                    current_type = 'pair_4'
                else:
                    current_sequence.append(state)
            elif 'impair_5' in state:
                if current_type != 'impair_5':
                    if current_sequence:
                        patterns[f'{current_type}_sequences'].append(current_sequence)
                    current_sequence = [state]
                    current_type = 'impair_5'
                else:
                    current_sequence.append(state)
            elif 'pair_6' in state:
                if current_type != 'pair_6':
                    if current_sequence:
                        patterns[f'{current_type}_sequences'].append(current_sequence)
                    current_sequence = [state]
                    current_type = 'pair_6'
                else:
                    current_sequence.append(state)

        # Ajouter la dernière séquence
        if current_sequence and current_type:
            patterns[f'{current_type}_sequences'].append(current_sequence)

        # Calculer le pattern dominant avec ordre de priorité
        scores = {
            'pair_4': len(patterns['pair_4_sequences']) * 1,  # PRIORITÉ 3
            'impair_5': len(patterns['impair_5_sequences']) * 3,  # PRIORITÉ 1 (maximale)
            'pair_6': len(patterns['pair_6_sequences']) * 2   # PRIORITÉ 2
        }

        if scores:
            patterns['dominant_pattern'] = max(scores, key=scores.get)
            patterns['significance_score'] = max(scores.values())

        return patterns

    def _optimize_sequences(self, sequences: List[List[str]],
                          ternary_patterns: Dict[str, Any]) -> List[List[str]]:
        """Optimise les séquences avec ordre de priorité"""
        optimized = []

        # Optimisation basée sur priorités (SANS BOOST ARBITRAIRE)
        for seq in sequences:
            optimized_seq = seq.copy()

            # Appliquer logique selon pattern dominant
            dominant = ternary_patterns.get('dominant_pattern')

            if dominant == 'pair_4':
                # PRIORITÉ 3 : favoriser continuité
                optimized_seq = ['S'] * len(seq)
            elif dominant == 'pair_6':
                # PRIORITÉ 2 : favoriser alternance modérée
                optimized_seq = ['S', 'O'] * (len(seq) // 2 + 1)
                optimized_seq = optimized_seq[:len(seq)]
            elif dominant == 'impair_5':
                # PRIORITÉ 1 : commutateur d'état → changement probable
                optimized_seq = ['O'] * len(seq)

            optimized.append(optimized_seq)

        return optimized

    def _evaluate_sequence_quality(self, sequences: List[List[str]],
                                 data: Dict[str, Any]) -> List[float]:
        """Évalue la qualité des séquences (Équation 5 adaptée)"""
        scores = []

        for seq in sequences:
            score = 0.0

            # Facteur 1 : Cohérence avec historique
            recent_so = data.get('so_sequence', [])[-5:]
            if recent_so and seq:
                # Calculer cohérence
                coherence = self._calculate_coherence(seq[0], recent_so)
                score += coherence * 0.4

            # Facteur 2 : Complexité appropriée
            complexity = len(set(seq)) / len(seq) if seq else 0
            score += complexity * 0.3

            # Facteur 3 : Exploitation TIE
            tie_factor = self._calculate_tie_factor(data)
            score += tie_factor * 0.3

            scores.append(score)

        return scores

    def _select_best_sequence(self, sequences: List[List[str]],
                            scores: List[float]) -> Optional[List[str]]:
        """Sélectionne la meilleure séquence"""
        if not sequences or not scores:
            return None

        best_index = np.argmax(scores)
        return sequences[best_index]

    def _calculate_diversity(self, sequences: List[List[str]]) -> float:
        """Calcule la diversité des séquences générées"""
        if not sequences:
            return 0.0

        unique_sequences = set(tuple(seq) for seq in sequences)
        return len(unique_sequences) / len(sequences)

    def _extract_tie_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrait les patterns des TIE pour exploitation"""
        full_influences = data.get('full_influences', [])

        tie_patterns = {
            'tie_frequency': 0,
            'tie_combined_states': [],
            'tie_sync_states': [],
            'impair_5_in_ties': 0
        }

        for influence in full_influences:
            if influence.get('is_tie', False):
                tie_patterns['tie_frequency'] += 1
                tie_patterns['tie_combined_states'].append(
                    influence.get('combined_state', '')
                )
                tie_patterns['tie_sync_states'].append(
                    influence.get('sync_state', '')
                )

                # Compter impair_5 dans TIE (PRIORITÉ 1)
                if 'impair_5' in influence.get('combined_state', ''):
                    tie_patterns['impair_5_in_ties'] += 1

        return tie_patterns

    def _calculate_enrichment_factor(self, data: Dict[str, Any]) -> float:
        """Calcule le facteur d'enrichissement par exploitation TIE"""
        total_hands = data.get('total_hands', 1)
        tie_hands = data.get('tie_hands', 0)

        if tie_hands == 0:
            return 0.0

        # Plus de TIE = plus d'enrichissement possible
        base_factor = tie_hands / total_hands

        # Bonus pour impair_5 dans TIE
        tie_patterns = self._extract_tie_patterns(data)
        impair_5_bonus = tie_patterns['impair_5_in_ties'] * 0.3

        return min(base_factor + impair_5_bonus, 1.0)

    def _calculate_coherence(self, prediction: str, history: List[str]) -> float:
        """Calcule la cohérence d'une prédiction avec l'historique"""
        if not history:
            return 0.5  # Neutre

        last_so = history[-1]

        # Cohérence basée sur la tendance récente
        if len(history) >= 3:
            recent_trend = history[-3:]
            s_count = recent_trend.count('S')
            o_count = recent_trend.count('O')

            if s_count > o_count and prediction == 'S':
                return 0.8  # Cohérent avec tendance S
            elif o_count > s_count and prediction == 'O':
                return 0.8  # Cohérent avec tendance O
            else:
                return 0.3  # Contre-tendance

        return 0.5  # Neutre si pas assez d'historique

    def _calculate_tie_factor(self, data: Dict[str, Any]) -> float:
        """Calcule le facteur d'influence des TIE"""
        tie_hands = data.get('tie_hands', 0)
        total_hands = data.get('total_hands', 1)

        return tie_hands / total_hands * 2  # Facteur TIE sans cap arbitraire

class PredictorRollout(UniversalRollout):
    """
    ROLLOUT 3 : PRÉDICTEUR BCT

    OSSATURE SEULEMENT - À implémenter plus tard
    """

    def __init__(self, rollout_id: int, config: AZRConfig):
        super().__init__(3, config)  # Toujours rollout_id = 3

    def predict_next_hand(self, analysis: Dict[str, Any], generation: Dict[str, Any],
                         game: BaccaratGame) -> Dict[str, Any]:
        """
        MÉTHODE À IMPLÉMENTER : Prédit la prochaine conversion S/O

        IMPORTANT : Prédit S (Same) ou O (Opposite), PAS P/B/T
        ENRICHI : Basé sur séquence complète incluant influences TIE
        """
        # Obtenir données complètes pour prédiction enrichie
        algorithmic_data = game.get_algorithmic_summary()

        # Retour enrichi avec exploitation influences TIE
        return {
            'rollout_id': self.rollout_id,
            'rollout_name': self.get_rollout_name(),
            'status': 'not_implemented',
            'next_so_prediction': 'WAIT',  # S, O, ou WAIT
            'prediction_confidence': 0.0,
            'prediction_basis': {
                'full_influences_used': len(algorithmic_data['full_influences']),
                'tie_influences_count': algorithmic_data['tie_hands'],
                'recent_pattern': algorithmic_data['recent_combined_states'],
                'sync_trend': algorithmic_data['recent_sync_pattern'],
                'reference_pb': algorithmic_data['last_pb_result']
            },
            'timestamp': datetime.now()
        }

################################################################################
#                                                                              #
#  🏗️ SECTION 5 : GESTIONNAIRE BCT (3 ROLLOUTS SEULEMENT)                    #
#                                                                              #
################################################################################

class BCTRolloutManager:
    """
    Gestionnaire BCT utilisant le système révolutionnaire AZR

    CONNEXION SIMPLE : Utilise directement AZRRolloutManager de Rollouts.py
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.BCTManager")

        # Utiliser le système révolutionnaire AZR
        self.azr_manager = AZRRolloutManager(config)

        self.logger.info("BCTRolloutManager initialisé avec système révolutionnaire AZR")

    def process_game_state(self, game: BaccaratGame) -> Dict[str, Any]:
        """
        Traite l'état du jeu avec le système révolutionnaire AZR

        UTILISE : AZRRolloutManager de Rollouts.py pour analyse complète
        """
        try:
            # Convertir BaccaratGame en format compatible pour AZR
            so_sequence = game.get_so_sequence()  # C'est ce que Rollouts.py attend comme 'history'
            algorithmic_data = game.get_algorithmic_summary()

            # Format exact attendu par Rollouts.py
            game_context = {
                'history': so_sequence,  # Liste des 'S' et 'O' - format principal attendu
                'current_index': len(so_sequence) - 1 if so_sequence else 0,
                'game_state': {
                    'pb_sequence': game.get_pb_sequence(),
                    'so_sequence': so_sequence,
                    'algorithmic_data': algorithmic_data,
                    'total_hands': len(game.get_pb_sequence())
                }
            }

            # Utiliser le système révolutionnaire AZR
            azr_results = self.azr_manager.execute_sophisticated_azr_bct_self_play(game_context)

            # Obtenir données algorithmiques complètes
            algorithmic_data = game.get_algorithmic_summary()

            # Extraire prédiction S/O du système révolutionnaire
            consensus_prediction = 'WAIT'
            consensus_confidence = 0.0

            if azr_results and 'rollout_3_results' in azr_results:
                rollout_3_results = azr_results['rollout_3_results']
                # Chercher la prédiction S/O finale
                if 'final_so_prediction' in rollout_3_results:
                    consensus_prediction = rollout_3_results['final_so_prediction']
                    consensus_confidence = rollout_3_results.get('final_confidence', 0.0)
                elif 'multidimensional_prediction' in rollout_3_results:
                    multi_pred = rollout_3_results['multidimensional_prediction']
                    if 'dimensional_predictions' in multi_pred and multi_pred['dimensional_predictions']:
                        first_pred = multi_pred['dimensional_predictions'][0]
                        consensus_prediction = first_pred.get('so_prediction', 'WAIT')
                        consensus_confidence = first_pred.get('confidence', 0.0)

            # Retour enrichi avec système révolutionnaire
            return {
                'rollout_results': azr_results,
                'consensus': {
                    'consensus_so_prediction': consensus_prediction,
                    'consensus_confidence': consensus_confidence,
                    'participating_rollouts': 3,
                    'revolutionary_system': True
                },
                'global_exploitation': {
                    'total_influences_available': len(algorithmic_data['full_influences']),
                    'tie_influences_exploited': algorithmic_data['tie_hands'],
                    'pb_references': algorithmic_data['pb_hands'],
                    'sync_pattern_continuity': len(algorithmic_data['sync_pattern']),
                    'combined_states_richness': len(algorithmic_data['combined_states'])
                },
                'status': 'revolutionary_system_active',
                'timestamp': datetime.now()
            }

        except Exception as e:
            self.logger.error(f"Erreur système révolutionnaire AZR: {e}")
            # Fallback simple
            return {
                'rollout_results': {},
                'consensus': {
                    'consensus_so_prediction': 'WAIT',
                    'consensus_confidence': 0.0,
                    'participating_rollouts': 0
                },
                'global_exploitation': {},
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now()
            }

    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Retourne un résumé des performances du système révolutionnaire AZR
        """
        try:
            # Obtenir métriques du système révolutionnaire
            azr_metrics = self.azr_manager.get_performance_metrics()

            return {
                'rollout_summaries': {
                    'analyzer': azr_metrics.get('rollout_1_metrics', {'status': 'active'}),
                    'generator': azr_metrics.get('rollout_2_metrics', {'status': 'active'}),
                    'predictor': azr_metrics.get('rollout_3_metrics', {'status': 'active'})
                },
                'global_stats': {
                    'total_predictions': azr_metrics.get('total_predictions', 0),
                    'average_accuracy': azr_metrics.get('average_accuracy', 0.0),
                    'active_rollouts': 3,
                    'revolutionary_system': True
                },
                'azr_metrics': azr_metrics,
                'status': 'revolutionary_system_active'
            }

        except Exception as e:
            self.logger.error(f"Erreur métriques système révolutionnaire: {e}")
            return {
                'rollout_summaries': {
                    'analyzer': {'status': 'error'},
                    'generator': {'status': 'error'},
                    'predictor': {'status': 'error'}
                },
                'global_stats': {
                    'total_predictions': 0,
                    'average_accuracy': 0.0,
                    'active_rollouts': 0
                },
                'status': 'error',
                'error': str(e)
            }

    def shutdown(self):
        """Arrêt propre du gestionnaire BCT"""
        self.logger.info("BCTRolloutManager arrêté")

################################################################################
#                                                                              #
#  🎮 SECTION 6 : INTERFACE GRAPHIQUE OPÉRATIONNELLE                          #
#                                                                              #
################################################################################

class AZRBaccaratInterface:
    """
    Interface graphique avec 9 boutons (3 résultats × 3 nombres de cartes)

    COMPLÈTEMENT OPÉRATIONNELLE
    Conforme aux spécifications corrigées
    """

    def __init__(self, config: AZRConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.Interface")

        # Composants système
        self.counting_engine = BaccaratCountingEngine(config)
        self.rollout_manager = BCTRolloutManager(config)

        # État de l'interface
        self.current_game = None
        self.burn_initialized = False

        # Interface graphique
        self.root = tk.Tk()
        self.root.title("🧠 BCT - Baccarat Counting Tool")
        self.root.geometry("1200x800")

        # Variables d'affichage
        self.prediction_var = tk.StringVar(value="WAIT")
        self.confidence_var = tk.StringVar(value="0.0%")
        self.game_stats_var = tk.StringVar(value="Partie: 0/60")
        self.explanation_var = tk.StringVar(value="Initialisez le brûlage pour commencer")

        self._create_interface()

        self.logger.info("Interface graphique initialisée")

    def _create_interface(self):
        """Crée l'interface graphique complète"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Section initialisation brûlage
        self._create_burn_section(main_frame)

        # Section affichage prédictions
        self._create_prediction_display(main_frame)

        # Section 9 boutons (3×3)
        self._create_nine_buttons_section(main_frame)

        # Section contrôles
        self._create_controls_section(main_frame)

        # Section statistiques
        self._create_stats_section(main_frame)

    def _create_burn_section(self, parent):
        """Crée la section d'initialisation du brûlage"""
        burn_frame = ttk.LabelFrame(parent, text="🔥 Initialisation", padding="10")
        burn_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(burn_frame, text="Brûlage:", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # Seulement 2 boutons : PAIR et IMPAIR
        buttons_frame = ttk.Frame(burn_frame)
        buttons_frame.pack(side=tk.LEFT, padx=(20, 0))

        # Bouton PAIR - fond noir, police jaune, plus petit
        pair_btn = tk.Button(buttons_frame, text="PAIR",
                            font=("Arial", 10, "bold"),
                            bg="black", fg="yellow",
                            width=8, height=1,
                            command=lambda: self._initialize_burn('PAIR'))
        pair_btn.pack(side=tk.LEFT, padx=5)

        # Bouton IMPAIR - fond noir, police jaune, plus petit
        impair_btn = tk.Button(buttons_frame, text="IMPAIR",
                              font=("Arial", 10, "bold"),
                              bg="black", fg="yellow",
                              width=8, height=1,
                              command=lambda: self._initialize_burn('IMPAIR'))
        impair_btn.pack(side=tk.LEFT, padx=5)

    def _create_prediction_display(self, parent):
        """Crée la section d'affichage des prédictions S/O"""
        pred_frame = ttk.LabelFrame(parent, text="🎯 Prédictions S/O (Same/Opposite)", padding="10")
        pred_frame.pack(fill=tk.X, pady=(0, 10))

        # Prédiction principale S/O - centrée
        main_pred_frame = ttk.Frame(pred_frame)
        main_pred_frame.pack(fill=tk.X)

        ttk.Label(main_pred_frame, text="Prédiction S/O:", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # Frame pour centrer la prédiction
        center_frame = ttk.Frame(main_pred_frame)
        center_frame.pack(expand=True, fill=tk.X)

        ttk.Label(center_frame, textvariable=self.prediction_var,
                 font=("Arial", 16, "bold"), foreground="blue").pack(anchor=tk.CENTER)

        # Explication S/O
        expl_frame = ttk.Frame(pred_frame)
        expl_frame.pack(fill=tk.X, pady=(2, 0))

        ttk.Label(expl_frame, textvariable=self.explanation_var,
                 font=("Arial", 9), foreground="gray").pack(side=tk.LEFT, padx=(10, 0))

        # Confiance
        conf_frame = ttk.Frame(pred_frame)
        conf_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(conf_frame, text="Confiance:", font=("Arial", 10)).pack(side=tk.LEFT)
        ttk.Label(conf_frame, textvariable=self.confidence_var,
                 font=("Arial", 10, "bold"), foreground="green").pack(side=tk.LEFT, padx=(10, 0))

        # Statistiques partie - avec formatage personnalisé
        stats_frame = ttk.Frame(pred_frame)
        stats_frame.pack(fill=tk.X, pady=(5, 0))

        # Frame pour les statistiques avec formatage mixte
        self.stats_display_frame = ttk.Frame(stats_frame)
        self.stats_display_frame.pack(side=tk.LEFT)

        # Labels séparés pour formatage différent
        self.manche_label = ttk.Label(self.stats_display_frame, text="Manche : ", font=("Arial", 10))
        self.manche_label.pack(side=tk.LEFT)

        self.manche_numbers = ttk.Label(self.stats_display_frame, text="0 / 60", font=("Arial", 10, "bold"))
        self.manche_numbers.pack(side=tk.LEFT)

        self.other_stats = ttk.Label(self.stats_display_frame, text="", font=("Arial", 10))
        self.other_stats.pack(side=tk.LEFT)

    def _create_nine_buttons_section(self, parent):
        """
        Crée la section des 9 boutons (3 résultats × 3 nombres de cartes)
        AMÉLIORATIONS INTERFACE :
        - Suppression des étiquettes headers inutiles
        - Réduction taille boutons par 2
        - Police plus claire pour meilleure lisibilité
        """
        buttons_frame = ttk.LabelFrame(parent, text="🎲 Saisie Manches (Résultat + Nombre de cartes)", padding="15")
        buttons_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Configuration grid
        for i in range(3):
            buttons_frame.columnconfigure(i, weight=1)

        # Boutons pour chaque combinaison (résultat + nombre de cartes)
        # SUPPRESSION DES HEADERS INUTILES - directement les boutons
        headers = ["PLAYER", "BANKER", "TIE"]
        colors = ["#1E3A8A", "#B91C1C", "#166534"]  # Bleu, Rouge, Vert
        card_counts = [
            (4, "4 cartes totales"),
            (5, "5 cartes totales"),
            (6, "6 cartes totales")
        ]

        for row, (total_cards, card_desc) in enumerate(card_counts, start=0):  # start=0 car plus de headers
            for col, (result, color) in enumerate(zip(headers, colors)):
                btn_text = f"{result} {total_cards}\n({card_desc})"

                btn = tk.Button(buttons_frame, text=btn_text,
                              font=("Arial", 8, "bold"),  # Police plus petite mais lisible
                              bg=color, fg="#F0F0F0",      # Couleur police plus claire
                              relief="raised", bd=2,
                              height=2, width=12,          # Taille réduite par 2
                              command=lambda r=result, c=total_cards: self._process_hand(r, c))
                btn.grid(row=row, column=col, sticky="ew", padx=2, pady=2)

    def _create_controls_section(self, parent):
        """Crée la section des contrôles"""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        # Boutons de contrôle
        ttk.Button(controls_frame, text="💾 Sauvegarder",
                  command=self._save_game).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(controls_frame, text="🔄 Nouvelle Partie",
                  command=self._new_game).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="📊 Statistiques",
                  command=self._show_statistics).pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="❌ Quitter",
                  command=self._quit_application).pack(side=tk.RIGHT)

    def _create_stats_section(self, parent):
        """Crée la section des statistiques en temps réel"""
        stats_frame = ttk.LabelFrame(parent, text="📈 Statistiques Temps Réel", padding="10")
        stats_frame.pack(fill=tk.X)

        # Créer un Text widget pour affichage des stats
        self.stats_text = tk.Text(stats_frame, height=6, width=80,
                                 font=("Courier", 9), state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.config(yscrollcommand=scrollbar.set)

    def _initialize_burn(self, parity: str):
        """Initialise le brûlage avec la parité ET crée la main 0"""
        if self.burn_initialized:
            messagebox.showwarning("Attention", "Brûlage déjà initialisé pour cette partie")
            return

        # Déterminer état initial et nombre de cartes par défaut
        initial_sync_state = self.config.initial_sync_mapping[parity]
        burn_cards_count = 4 if parity == 'PAIR' else 5  # Défaut : pair_4 ou impair_5

        # Créer nouvelle partie (initialisation)
        self.current_game = BaccaratGame(
            game_number=1,
            burn_cards_count=burn_cards_count,
            burn_parity=parity,
            initial_sync_state=initial_sync_state,
            current_sync_state=initial_sync_state
        )

        # Créer la main 0 (brûlage) avec seulement INDEX 1 et INDEX 2
        burn_hand = self.counting_engine.create_burn_hand(
            burn_cards_count, parity, initial_sync_state
        )

        # Ajouter la main 0 au jeu
        self.current_game.add_hand(burn_hand)

        self.burn_initialized = True

        # Mettre à jour affichage
        self._update_display()

        self.logger.info(f"Brûlage initialisé: parité {parity} -> {initial_sync_state}")
        self.logger.info(f"Main 0 créée: {burn_hand.cards_category}_{burn_hand.sync_state}")
        messagebox.showinfo("Brûlage Initialisé",
                          f"Brûlage: parité {parity}\nÉtat initial: {initial_sync_state}\nMain 0 créée: {burn_hand.cards_category}\n\nVous pouvez maintenant saisir les manches.")

    def _process_hand(self, result: str, total_cards: int):
        """
        Traite une main avec résultat et nombre total de cartes

        Args:
            result: 'PLAYER', 'BANKER', 'TIE' (le gagnant de la main)
            total_cards: 4, 5, ou 6 (nombre total de cartes distribuées dans la main)
        """
        if not self.burn_initialized:
            messagebox.showwarning("Attention", "Veuillez d'abord initialiser le brûlage")
            return

        if self.current_game.is_complete():
            messagebox.showinfo("Partie Terminée",
                              f"Partie complète: {self.config.max_manches_per_game} manches P/B atteintes")
            return

        try:
            # Mapping automatique vers catégorie INDEX 1
            category_mapping = {
                4: 'pair_4',     # 4 cartes totales = pair_4
                5: 'impair_5',   # 5 cartes totales = impair_5
                6: 'pair_6'      # 6 cartes totales = pair_6
            }

            cards_category = category_mapping.get(total_cards)
            if not cards_category:
                raise ValueError(f"Nombre de cartes invalide: {total_cards}")

            # Traiter la main avec le moteur de comptage
            # Le moteur calcule automatiquement tous les INDEX
            hand = self.counting_engine.process_hand(
                self.current_game, result, total_cards, cards_category
            )

            # Obtenir prédictions des 3 rollouts BCT (pour l'instant retour minimal)
            rollout_results = self.rollout_manager.process_game_state(self.current_game)

            # VALIDATION AZR : Comparer prédiction vs résultat réel
            if rollout_results and 'rollout_results' in rollout_results:
                azr_results = rollout_results['rollout_results']
                if azr_results and 'rollout_3_results' in azr_results:
                    rollout_3_results = azr_results['rollout_3_results']
                    predicted_so = rollout_3_results.get('final_so_prediction', 'WAIT')
                    actual_so = hand.so_conversion

                    # Valider seulement si on a une prédiction et un résultat réel
                    if predicted_so in ['S', 'O'] and actual_so in ['S', 'O']:
                        # Envoyer la validation au système AZR pour mise à jour des métriques
                        self.rollout_manager.azr_manager.validate_prediction(predicted_so, actual_so)
                        correct_status = "CORRECT" if predicted_so == actual_so else "INCORRECT"
                        self.logger.info(f"VALIDATION AZR: Predit={predicted_so}, Reel={actual_so}, "
                                       f"Status={correct_status}")

            # Mettre à jour affichage
            self._update_display(rollout_results)

            # Log de la main traitée avec TOUS les INDEX
            self.logger.info(f"Main traitée: {result} {total_cards} cartes -> {hand.cards_category}_{hand.sync_state}_{hand.result} {hand.so_conversion}")

        except Exception as e:
            self.logger.error(f"Erreur traitement main: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du traitement: {e}")

    def _update_display(self, rollout_results: Dict = None):
        """Met à jour l'affichage de l'interface"""
        if not self.current_game:
            return

        # Mettre à jour statistiques de partie avec formatage personnalisé
        self.manche_numbers.config(text=f"{self.current_game.pb_hands} / {self.config.max_manches_per_game}")
        self.other_stats.config(text=f" | Total: {self.current_game.total_hands} | TIE: {self.current_game.tie_hands}")

        # Mettre à jour prédictions S/O
        if rollout_results and 'consensus' in rollout_results:
            consensus = rollout_results['consensus']
            so_prediction = consensus.get('consensus_so_prediction', 'WAIT')
            confidence = consensus.get('consensus_confidence', 0.0)

            self.prediction_var.set(so_prediction)
            self.confidence_var.set(f"{confidence:.1%}")

            # Explication de la prédiction S/O
            if so_prediction == 'S':
                last_pb = self.current_game.last_pb_result or "?"
                self.explanation_var.set(f"Same: répéter {last_pb}")
            elif so_prediction == 'O':
                last_pb = self.current_game.last_pb_result or "?"
                opposite = "BANKER" if last_pb == "PLAYER" else "PLAYER" if last_pb == "BANKER" else "?"
                self.explanation_var.set(f"Opposite: jouer {opposite}")
            else:
                self.explanation_var.set("Attendre plus de données")

            # Mettre à jour statistiques détaillées
            self._update_stats_display(rollout_results)

            # Démonstration exploitation TIE
            self._demonstrate_tie_exploitation()
        else:
            self.prediction_var.set("WAIT")
            self.confidence_var.set("0.0%")
            self.explanation_var.set("En attente d'analyse...")

    def _update_stats_display(self, rollout_results: Dict):
        """Met à jour l'affichage des statistiques détaillées"""
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)

        # Consensus S/O
        consensus = rollout_results.get('consensus', {})
        so_prediction = consensus.get('consensus_so_prediction', 'N/A')
        confidence = consensus.get('consensus_confidence', 0.0)
        self.stats_text.insert(tk.END, f"🎯 CONSENSUS S/O: {so_prediction} ({confidence:.1%})\n\n")

        # Affichage système de comptage
        self.stats_text.insert(tk.END, f"⚙️ SYSTÈME DE COMPTAGE:\n")
        self.stats_text.insert(tk.END, f"  • État SYNC/DESYNC: {self.current_game.current_sync_state}\n")
        self.stats_text.insert(tk.END, f"  • Dernière main P/B: {self.current_game.last_pb_result or 'Aucune'}\n")

        if self.current_game.hands:
            last_hand = self.current_game.hands[-1]
            self.stats_text.insert(tk.END, f"\nDernière main traitée:\n")
            self.stats_text.insert(tk.END, f"  • Résultat: {last_hand.result}\n")
            self.stats_text.insert(tk.END, f"  • Cartes: {last_hand.cards_distributed} ({last_hand.cards_category})\n")
            self.stats_text.insert(tk.END, f"  • État SYNC: {last_hand.sync_state}\n")
            self.stats_text.insert(tk.END, f"  • État combiné: {last_hand.combined_state}\n")
            self.stats_text.insert(tk.END, f"  • Conversion S/O: {last_hand.so_conversion}\n")

        self.stats_text.insert(tk.END, f"\n📊 SÉQUENCES:\n")
        pb_sequence = self.current_game.get_pb_sequence()
        so_sequence = self.current_game.get_so_sequence()
        self.stats_text.insert(tk.END, f"  • P/B: {' '.join(pb_sequence[-10:]) if pb_sequence else 'Aucune'}\n")
        self.stats_text.insert(tk.END, f"  • S/O: {' '.join(so_sequence[-10:]) if so_sequence else 'Aucune'}\n")

        self.stats_text.config(state=tk.DISABLED)
        self.stats_text.see(tk.END)

    def _demonstrate_tie_exploitation(self):
        """
        Démonstration de l'exploitation des influences TIE

        VALIDATION : Montre que les TIE sont exploités pour prédictions
        """
        if not self.current_game or not self.current_game.hands:
            return

        # Obtenir données algorithmiques enrichies
        algorithmic_data = self.current_game.get_algorithmic_summary()

        # Ajouter section démonstration dans les stats
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.insert(tk.END, f"\n🔬 EXPLOITATION INFLUENCES TIE:\n")

        # Montrer richesse des données
        self.stats_text.insert(tk.END, f"  • Influences totales: {len(algorithmic_data['full_influences'])}\n")
        self.stats_text.insert(tk.END, f"  • Influences TIE: {algorithmic_data['tie_hands']}\n")
        self.stats_text.insert(tk.END, f"  • Pattern SYNC complet: {' → '.join(algorithmic_data['recent_sync_pattern'])}\n")
        self.stats_text.insert(tk.END, f"  • États combinés récents: {' → '.join(algorithmic_data['recent_combined_states'])}\n")

        # Montrer exploitation TIE
        tie_influences = [inf for inf in algorithmic_data['full_influences'] if inf['is_tie']]
        if tie_influences:
            self.stats_text.insert(tk.END, f"\n📊 TIE EXPLOITÉS POUR PRÉDICTIONS:\n")
            for tie in tie_influences[-3:]:  # 3 derniers TIE
                self.stats_text.insert(tk.END, f"  • Main {tie['hand_number']}: {tie['combined_state']} (INDEX 1&2 exploités)\n")

        self.stats_text.config(state=tk.DISABLED)

    def _save_game(self):
        """Sauvegarde la partie actuelle"""
        if not self.current_game:
            messagebox.showwarning("Attention", "Aucune partie en cours")
            return

        try:
            # Créer données de sauvegarde
            save_data = {
                'game': {
                    'game_number': self.current_game.game_number,
                    'burn_cards_count': self.current_game.burn_cards_count,
                    'burn_parity': self.current_game.burn_parity,
                    'initial_sync_state': self.current_game.initial_sync_state,
                    'hands': [
                        {
                            'hand_number': hand.hand_number,
                            'pb_hand_number': hand.pb_hand_number,
                            'cards_distributed': hand.cards_distributed,
                            'cards_parity': hand.cards_parity,
                            'cards_category': hand.cards_category,
                            'sync_state': hand.sync_state,
                            'combined_state': hand.combined_state,
                            'result': hand.result,
                            'so_conversion': hand.so_conversion,
                            'timestamp': hand.timestamp.isoformat()
                        }
                        for hand in self.current_game.hands
                    ]
                },
                'save_timestamp': datetime.now().isoformat()
            }

            # Sauvegarder dans fichier JSON
            filename = f"bct_game_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("Sauvegarde", f"Partie sauvegardée: {filename}")
            self.logger.info(f"Partie sauvegardée: {filename}")

        except Exception as e:
            self.logger.error(f"Erreur sauvegarde: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _new_game(self):
        """Démarre une nouvelle partie"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Nouvelle Partie",
                                     "Voulez-vous vraiment démarrer une nouvelle partie ?\n"
                                     "La partie actuelle sera perdue si non sauvegardée."):
                return

        # Réinitialiser état
        self.current_game = None
        self.burn_initialized = False

        # Réinitialiser affichage
        self.prediction_var.set("WAIT")
        self.confidence_var.set("0.0%")
        self.manche_numbers.config(text="0 / 60")
        self.other_stats.config(text=" | Total: 0 | TIE: 0")
        self.explanation_var.set("Nouvelle partie - Initialisez le brûlage")

        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, "🆕 Nouvelle partie initialisée\n")
        self.stats_text.insert(tk.END, "Veuillez initialiser le brûlage pour commencer\n")
        self.stats_text.config(state=tk.DISABLED)

        self.logger.info("Nouvelle partie initialisée")

    def _show_statistics(self):
        """Affiche les statistiques détaillées dans une fenêtre séparée"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title("📊 Statistiques Détaillées BCT")
        stats_window.geometry("800x600")

        # Text widget avec scrollbar
        text_frame = ttk.Frame(stats_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        stats_text = tk.Text(text_frame, font=("Courier", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=stats_text.yview)

        stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        stats_text.config(yscrollcommand=scrollbar.set)

        # Générer statistiques complètes
        stats_content = "🧠 STATISTIQUES BCT (Baccarat Counting Tool)\n"
        stats_content += "=" * 60 + "\n\n"

        # Configuration système
        system_limits = self.config.get_system_limits()
        stats_content += "📊 CONFIGURATION SYSTÈME:\n"
        stats_content += f"  • Rollouts BCT: {system_limits['nb_rollouts']}\n"
        stats_content += f"  • Cœurs CPU: {system_limits['nb_cores']}\n"
        stats_content += f"  • Mémoire max: {system_limits['max_memory_gb']}GB\n"
        stats_content += f"  • Mémoire par rollout: {system_limits['memory_per_rollout_mb']}MB\n\n"

        # Partie actuelle
        if self.current_game:
            stats_content += f"🎲 PARTIE ACTUELLE:\n"
            stats_content += f"  • Numéro: {self.current_game.game_number}\n"
            stats_content += f"  • Brûlage: parité {self.current_game.burn_parity}\n"
            stats_content += f"  • État initial: {self.current_game.initial_sync_state}\n"
            stats_content += f"  • État actuel: {self.current_game.current_sync_state}\n"
            stats_content += f"  • Manches P/B: {self.current_game.pb_hands}/{self.config.max_manches_per_game}\n"
            stats_content += f"  • Total mains: {self.current_game.total_hands}\n"
            stats_content += f"  • TIE: {self.current_game.tie_hands}\n"
            stats_content += f"  • Conversions S/O: {self.current_game.so_conversions}\n\n"

            # Détails des mains
            if self.current_game.hands:
                stats_content += f"📋 DÉTAILS DES MAINS:\n"
                for hand in self.current_game.hands[-5:]:  # 5 dernières mains
                    stats_content += f"  Main {hand.hand_number}: {hand.result} {hand.cards_distributed}c → {hand.combined_state} ({hand.so_conversion})\n"
        else:
            stats_content += "🎲 AUCUNE PARTIE EN COURS\n"

        stats_text.insert(tk.END, stats_content)
        stats_text.config(state=tk.DISABLED)

    def _quit_application(self):
        """Quitte l'application proprement"""
        if self.current_game and self.current_game.hands:
            if not messagebox.askyesno("Quitter",
                                     "Voulez-vous vraiment quitter ?\n"
                                     "La partie actuelle sera perdue si non sauvegardée."):
                return

        self.logger.info("Fermeture de l'application")

        # Arrêt propre du rollout manager
        self.rollout_manager.shutdown()

        # Fermer interface
        self.root.quit()
        self.root.destroy()

    def run(self):
        """Lance l'interface graphique"""
        self.logger.info("Démarrage de l'interface graphique")

        # Gestionnaire de fermeture
        self.root.protocol("WM_DELETE_WINDOW", self._quit_application)

        # Démarrer boucle principale
        self.root.mainloop()

################################################################################
#                                                                              #
#  🚀 SECTION 7 : FONCTION PRINCIPALE                                         #
#                                                                              #
################################################################################

def main():
    """
    Fonction principale du programme BCT (Baccarat Counting Tool)

    Initialise tous les composants et lance l'interface graphique
    """
    print("🧠 BCT - BACCARAT COUNTING TOOL")
    print("=" * 50)
    print("Initialisation du système...")

    try:
        # Initialiser configuration
        config = AZRConfig()

        # Afficher informations système
        system_limits = config.get_system_limits()
        print(f"📊 Configuration système:")
        print(f"  • Rollouts BCT: {system_limits['nb_rollouts']}")
        print(f"  • Cœurs CPU: {system_limits['nb_cores']}")
        print(f"  • Mémoire max: {system_limits['max_memory_gb']}GB")
        print(f"  • Mémoire par rollout: {system_limits['memory_per_rollout_mb']}MB")

        print(f"\n✅ Composants opérationnels:")
        print(f"  • AZRConfig bien structuré")
        print(f"  • Système de comptage conforme")
        print(f"  • Interface graphique complète")
        print(f"  • Ossature des classes établie")

        # Initialiser et lancer interface
        print("\n🎮 Lancement de l'interface graphique...")
        interface = AZRBaccaratInterface(config)
        interface.run()

    except KeyboardInterrupt:
        print("\n⚠️ Interruption utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale: {e}", exc_info=True)
    finally:
        print("\n👋 Arrêt du programme")

if __name__ == "__main__":
    main()
