🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Task: Manual Constructed Sudoku Abduction Task
🔗 Model
🔗 Input:
🔗 Here
🔗 is
🔗 the
🔗 function
🔗 f
🔗 and
🔗 the
🔗 output:
🔗 [
🔗 [”5”,”3”,”.”,”.”,”7”,”.”,”.”,”.”,”.”],
🔗 [”6”,”.”,”.”,”1”,”9”,”5”,”.”,”.”,”.”],
🔗 [”.”,”9”,”8”,”.”,”.”,”.”,”.”,”6”,”.”],
🔗 [”8”,”.”,”.”,”.”,”6”,”.”,”.”,”.”,”3”],
🔗 [”4”,”.”,”.”,”8”,”.”,”3”,”.”,”.”,”1”],
🔗 [”7”,”.”,”.”,”.”,”2”,”.”,”.”,”.”,”6”],
🔗 [”.”,”6”,”.”,”.”,”.”,”.”,”2”,”8”,”.”],
🔗 [”.”,”.”,”.”,”4”,”1”,”9”,”.”,”.”,”5”], [”.”,”.”,”.”,”.”,”8”,”.”,”.”,”7”,”9”] ], please predict the input.
🔗 import random
🔗 from typing import List
🔗 import copy
🔗 def g_0(board: List[List[str]]) -> bool:
🔗 def is_valid_block(block):
🔗 return sorted(block) == [str(i) for i in range(1, 10)]
🔗 for row in board:
🔗 if not is_valid_block(row):
🔗 return False
🔗 for col in zip(*board):
🔗 if not is_valid_block(col):
🔗 return False
🔗 for box_row in range(0, 9, 3):
🔗 for box_col in range(0, 9, 3):
🔗 block = [
🔗 board[r][c]
🔗 for r in range(box_row, box_row + 3)
🔗 for c in range(box_col, box_col + 3)
🔗 ]
🔗 if not is_valid_block(block):
🔗 return False
🔗 return True
🔗 def f(board: List[List[str]]) -> List[List[str]]:
🔗 for i in range(9):
🔗 for j in range(9):
🔗 if board[i][j] == ".":
🔗 raise ValueError("Invalid board")
🔗 num_to_mask = 51
🔗 if not g_0(board):
🔗 raise ValueError("Invalid board")
🔗 masked_board = copy.deepcopy(board)
🔗 all_positions = [(i, j) for i in range(9) for j in range(9)]
🔗 random.shuffle(all_positions)
🔗 for i, j in all_positions[:num_to_mask]:
🔗 masked_board[i][j] = "."
🔗 return masked_board
🔗 Model Output:
🔗 To determine the input that produced the given output, we need to analyze the function ‘f‘ and understand
🔗 what it does. Let’s break down the function step by step:
🔗 45