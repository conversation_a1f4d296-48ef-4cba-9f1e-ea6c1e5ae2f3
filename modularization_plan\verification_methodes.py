#!/usr/bin/env python3
"""
Script de vérification de la correspondance exacte entre les méthodes
dans Rollouts.py et celles listées dans liste_complete_methodes_rollouts.txt
"""

import re

def extract_methods_from_rollouts():
    """Extrait toutes les méthodes de Rollouts.py"""
    with open('Rollouts.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    pattern = r'def ([a-zA-Z_][a-zA-Z0-9_]*)\('
    methods = re.findall(pattern, content)
    return set(methods)

def extract_methods_from_list():
    """Extrait les méthodes mentionnées dans la liste"""
    with open('modularization_plan/liste_complete_methodes_rollouts.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    methods = set()
    
    # Patterns pour extraire les méthodes
    patterns = [
        r'- ([a-zA-Z_][a-zA-Z0-9_]*)\(\)',  # Format "- method()"
        r'([a-zA-Z_][a-zA-Z0-9_]*)\(\)',    # Format "method()"
    ]
    
    for line in content.split('\n'):
        line = line.strip()
        if line and not line.startswith('#') and not line.startswith('=') and not line.startswith('📊'):
            for pattern in patterns:
                matches = re.findall(pattern, line)
                for match in matches:
                    # Exclure certains mots-clés qui ne sont pas des méthodes
                    if match not in ['def', 'class', 'import', 'from', 'return', 'if', 'else', 'for', 'while']:
                        methods.add(match)
    
    return methods

def main():
    print("🔍 VÉRIFICATION DE LA CORRESPONDANCE DES MÉTHODES")
    print("=" * 60)
    
    # Extraire les méthodes
    methods_rollouts = extract_methods_from_rollouts()
    methods_list = extract_methods_from_list()
    
    print(f"📁 Méthodes dans Rollouts.py: {len(methods_rollouts)}")
    print(f"📋 Méthodes dans la liste: {len(methods_list)}")
    print()
    
    # Vérifier les différences
    missing_in_list = methods_rollouts - methods_list
    extra_in_list = methods_list - methods_rollouts
    
    if missing_in_list:
        print(f"❌ MÉTHODES MANQUANTES DANS LA LISTE ({len(missing_in_list)}):")
        for i, method in enumerate(sorted(missing_in_list), 1):
            print(f"  {i:3d}. {method}()")
        print()
    
    if extra_in_list:
        print(f"⚠️ MÉTHODES EN TROP DANS LA LISTE ({len(extra_in_list)}):")
        for i, method in enumerate(sorted(extra_in_list), 1):
            print(f"  {i:3d}. {method}()")
        print()
    
    if not missing_in_list and not extra_in_list:
        print("✅ PARFAIT! Toutes les méthodes sont correctement listées.")
        print("🎯 La liste est complète et exacte.")
    else:
        print("📊 RÉSUMÉ DE LA VÉRIFICATION:")
        print(f"  - Méthodes dans Rollouts.py: {len(methods_rollouts)}")
        print(f"  - Méthodes dans la liste: {len(methods_list)}")
        print(f"  - Méthodes manquantes: {len(missing_in_list)}")
        print(f"  - Méthodes en trop: {len(extra_in_list)}")
        print(f"  - Correspondance: {len(methods_rollouts & methods_list)}/{len(methods_rollouts)} ({len(methods_rollouts & methods_list)/len(methods_rollouts)*100:.1f}%)")
    
    print()
    print("🔧 RECOMMANDATIONS:")
    if missing_in_list:
        print("  1. Ajouter les méthodes manquantes à la liste")
    if extra_in_list:
        print("  2. Retirer les méthodes en trop de la liste")
    if not missing_in_list and not extra_in_list:
        print("  ✅ Aucune action requise - Liste parfaite!")

if __name__ == "__main__":
    main()
