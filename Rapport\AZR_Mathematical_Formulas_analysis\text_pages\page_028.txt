🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Task: Propose Induction Task
🔗 Model Input: Here is the function f. Analyze its behaviour and relate it to the observed inputs/outputs.
🔗 def f(numbers: list[int]) -> int:
🔗 replacement = {
🔗 0: 2,
🔗 1: 3,
🔗 2: 1,
🔗 3: 0,
🔗 4: 4
🔗 }
🔗 return sum(replacement[num] for num in numbers)
🔗 Model Output:
🔗 To solve this task, we need to come up with 10 different inputs for the function f(numbers: list[int]) -> int. The function
🔗 replaces each number in the list according to a specific dictionary and then sums up the replaced numbers. Let’s analyze the
🔗 dictionary:
🔗 This dictionary tells us that:
🔗 -0 becomes 2
🔗 -1 becomes 3
🔗 -2 becomes 1
🔗 -3 becomes 0
🔗 -4 remains 4
🔗 To create diverse outputs, we should include lists with different sets of numbers from the list {0, 1, 2, 3, 4}.
🔗 We also need
🔗 to ensure that the inputs are valid (i.e., lists of integers).
🔗 Let’s illustrate using provided examples step-by-step:
🔗 1. Input: [0] ⇒Replacement: [2] ⇒sum = 2
🔗 2. Input: [1, 2, 3] ⇒Replacement: [3, 1, 0] ⇒sum = 4
🔗 3. Input: [4, 1] ⇒Replacement: [4, 3] ⇒sum = 7
🔗 4. Input: [2, 1, 0] ⇒Replacement: [1, 3, 2] ⇒sum = 6
🔗 5. Input: [3, 0, 2, 4] ⇒Replacement: [0, 2, 1, 4] ⇒sum = 7
🔗 6. Input: [1, 1, 1] ⇒Replacement: [3, 3, 3] ⇒sum = 9
🔗 7. Input: [2, 2, 0, 1] ⇒Replacement: [1, 1, 2, 3] ⇒sum = 7
🔗 8. Input: [3, 4, 2] ⇒Replacement: [0, 4, 1] ⇒sum = 5
🔗 9. Input: [0, 1, 2, 3, 4] ⇒Replacement: [2, 3, 1, 0, 4] ⇒sum = 10
🔗 10. Input: [1, 2, 3, 0, 4] ⇒Replacement: [3, 1, 0, 2, 4] ⇒sum = 10
🔗 Thus, the rule is clearly replacing numbers according to the given mapping and then summing the results.
🔗 Inputs: ['[0]', '[1, 2, 3]', '[4, 1]', '[2, 1, 0]', '[3, 0, 2, 4]', '[1, 1, 1]',
🔗 [2, 2, 0, 1], ’[3, 4, 2]’, ’[0, 1, 2, 3, 4]’, ’[1, 2, 3, 0, 4]’]|
🔗 Message: You are given a function that takes a list of integers as input and returns a single integer as output. Can you determine
🔗 the rule this function is following by analyzing the outputs for the given inputs?
🔗 (After execution:) Outputs: ['2', '4', '7', '6', '7', '9', '7', '5', '10', '10']
🔗 Figure 21. An example response of proposing an induction task. Given a random selected program, the model needs to propose 10 inputs
🔗 and a description of program. These inputs will be executed and verified to get outputs. At solve phase, the model need to generate the
🔗 program given the message and 5 input/output pairs (half is hidden cases)
🔗 27
🔗 Figure 21. Propose Induction Task Example. An example response of proposing an induction task. Given a random selected program,
🔗 the model needs to propose 10 inputs and a description of program. These inputs will be executed and verified to get outputs. At solve
🔗 phase, the model need to generate the program given the message and 5 input/output pairs (half is hidden cases)
🔗 28