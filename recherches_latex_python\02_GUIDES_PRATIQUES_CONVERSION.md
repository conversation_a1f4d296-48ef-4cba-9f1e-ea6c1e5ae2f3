# Guides Pratiques de Conversion LaTeX vers Python

## 1. MÉTHODE 1: UTILISATION DE latex2sympy2

### Installation
```bash
pip install latex2sympy2
pip install antlr4-python3-runtime==4.9.3  # Version spécifique recommandée
```

### Exemple basique
```python
from latex2sympy2 import latex2sympy

# Conversion simple
latex_expr = r"\frac{x^2 + 2x + 1}{x + 1}"
sympy_expr = latex2sympy(latex_expr)
print(sympy_expr)  # (x**2 + 2*x + 1)/(x + 1)

# Simplification
simplified = sympy_expr.simplify()
print(simplified)  # x + 1
```

### Expressions complexes supportées
```python
# Intégrales
latex_integral = r"\int_{0}^{\pi} \sin(x) dx"
sympy_integral = latex2sympy(latex_integral)

# Sommes
latex_sum = r"\sum_{n=1}^{\infty} \frac{1}{n^2}"
sympy_sum = latex2sympy(latex_sum)

# Matrices
latex_matrix = r"\begin{pmatrix} 1 & 2 \\ 3 & 4 \end{pmatrix}"
sympy_matrix = latex2sympy(latex_matrix)
```

## 2. MÉTHODE 2: SYMPY PARSING NATIF

### Utilisation du parser SymPy
```python
from sympy.parsing.latex import parse_latex
from sympy import *

# Parsing direct
latex_str = r"x^2 + 2x + 1"
expr = parse_latex(latex_str)
print(expr)  # x**2 + 2*x + 1

# Avec variables définies
x, y = symbols('x y')
latex_str = r"\frac{\partial}{\partial x}(x^2 y)"
expr = parse_latex(latex_str)
```

### Configuration avancée
```python
from sympy.parsing.latex import parse_latex
from sympy.parsing.latex._parse_latex_antlr import LaTeXParser

# Parser avec options personnalisées
def custom_parse(latex_str):
    try:
        return parse_latex(latex_str)
    except Exception as e:
        print(f"Erreur de parsing: {e}")
        return None
```

## 3. MÉTHODE 3: CONVERSION MANUELLE AVEC REGEX

### Patterns de conversion courants
```python
import re
from sympy import *

def latex_to_python_basic(latex_str):
    """Conversion basique LaTeX vers Python"""
    
    # Remplacements de base
    conversions = {
        r'\\frac\{([^}]+)\}\{([^}]+)\}': r'(\1)/(\2)',
        r'\^(\w+)': r'**(\1)',
        r'\^{([^}]+)}': r'**(\1)',
        r'_(\w+)': r'[\1]',  # Pour les indices
        r'_{([^}]+)}': r'[\1]',
        r'\\sqrt\{([^}]+)\}': r'sqrt(\1)',
        r'\\sin': 'sin',
        r'\\cos': 'cos',
        r'\\tan': 'tan',
        r'\\log': 'log',
        r'\\ln': 'log',
        r'\\exp': 'exp',
        r'\\pi': 'pi',
        r'\\infty': 'oo',
    }
    
    result = latex_str
    for pattern, replacement in conversions.items():
        result = re.sub(pattern, replacement, result)
    
    return result

# Exemple d'usage
latex = r"\frac{x^2 + 1}{\sqrt{x + 1}}"
python_expr = latex_to_python_basic(latex)
print(python_expr)  # (x**2 + 1)/sqrt(x + 1)
```

## 4. MÉTHODE 4: UTILISATION D'OCR POUR IMAGES

### Avec pix2tex
```python
from pix2tex.cli import LatexOCR

# Initialisation du modèle
model = LatexOCR()

# Conversion d'image vers LaTeX
latex_code = model('path/to/equation_image.png')
print(f"LaTeX détecté: {latex_code}")

# Puis conversion vers SymPy
from latex2sympy2 import latex2sympy
sympy_expr = latex2sympy(latex_code)
```

### Avec Pix2Text
```python
from pix2text import Pix2Text

# Initialisation
p2t = Pix2Text()

# Reconnaissance complète (texte + formules)
result = p2t('path/to/document_image.png')
print(result)

# Extraction des formules LaTeX uniquement
formulas = p2t.recognize_formula('path/to/formula_image.png')
```

## 5. GESTION DES ERREURS ET LIMITATIONS

### Gestion robuste des erreurs
```python
def safe_latex_conversion(latex_str):
    """Conversion LaTeX sécurisée avec gestion d'erreurs"""
    
    try:
        # Tentative avec latex2sympy2
        from latex2sympy2 import latex2sympy
        return latex2sympy(latex_str)
    except Exception as e1:
        print(f"Échec latex2sympy2: {e1}")
        
        try:
            # Tentative avec SymPy natif
            from sympy.parsing.latex import parse_latex
            return parse_latex(latex_str)
        except Exception as e2:
            print(f"Échec SymPy: {e2}")
            
            try:
                # Conversion manuelle en dernier recours
                return latex_to_python_basic(latex_str)
            except Exception as e3:
                print(f"Échec conversion manuelle: {e3}")
                return None
```

### Préprocessing LaTeX
```python
def preprocess_latex(latex_str):
    """Nettoyage et préparation du LaTeX"""
    
    # Suppression des espaces superflus
    latex_str = re.sub(r'\s+', ' ', latex_str.strip())
    
    # Normalisation des commandes
    latex_str = latex_str.replace('\\left(', '(')
    latex_str = latex_str.replace('\\right)', ')')
    latex_str = latex_str.replace('\\left[', '[')
    latex_str = latex_str.replace('\\right]', ']')
    
    # Gestion des environnements mathématiques
    latex_str = latex_str.replace('$$', '')
    latex_str = latex_str.replace('$', '')
    
    return latex_str
```

## 6. CONVERSION VERS DIFFÉRENTS FORMATS PYTHON

### Vers NumPy
```python
import numpy as np
from sympy import lambdify

# Conversion SymPy vers fonction NumPy
x = symbols('x')
expr = x**2 + 2*x + 1
numpy_func = lambdify(x, expr, 'numpy')

# Utilisation
x_values = np.linspace(-5, 5, 100)
y_values = numpy_func(x_values)
```

### Vers code Python exécutable
```python
from sympy import *
from sympy.utilities.codegen import codegen

# Génération de code C/Python
x, y = symbols('x y')
expr = x**2 + y**2
code = codegen(('my_function', expr), 'Python')
print(code[0][1])  # Code Python généré
```

## 7. VALIDATION ET TESTS

### Tests de conversion
```python
def test_conversion(latex_str, expected_result=None):
    """Test de conversion avec validation"""
    
    print(f"Test: {latex_str}")
    
    # Conversion
    result = safe_latex_conversion(latex_str)
    
    if result is not None:
        print(f"Résultat: {result}")
        
        # Validation si résultat attendu fourni
        if expected_result:
            if str(result) == str(expected_result):
                print("✓ Test réussi")
            else:
                print("✗ Test échoué")
    else:
        print("✗ Conversion impossible")
    
    print("-" * 50)

# Exemples de tests
test_conversion(r"x^2 + 1", "x**2 + 1")
test_conversion(r"\frac{1}{x}", "1/x")
test_conversion(r"\sqrt{x}", "sqrt(x)")
```

## 8. OPTIMISATION ET PERFORMANCE

### Cache des conversions
```python
from functools import lru_cache

@lru_cache(maxsize=1000)
def cached_latex_conversion(latex_str):
    """Conversion avec cache pour améliorer les performances"""
    return safe_latex_conversion(latex_str)
```

### Traitement par lots
```python
def batch_convert_latex(latex_list):
    """Conversion par lots avec parallélisation"""
    from concurrent.futures import ThreadPoolExecutor
    
    with ThreadPoolExecutor(max_workers=4) as executor:
        results = list(executor.map(safe_latex_conversion, latex_list))
    
    return results
```

## 9. INTÉGRATION AVEC JUPYTER

### Magic commands personnalisées
```python
from IPython.core.magic import register_line_magic
from IPython.display import display, Math

@register_line_magic
def latex2py(line):
    """Magic command pour conversion LaTeX vers Python"""
    result = safe_latex_conversion(line)
    if result:
        print(f"Python: {result}")
        display(Math(line))  # Affichage LaTeX
        return result
    else:
        print("Conversion échouée")

# Usage dans Jupyter: %latex2py \frac{x^2}{2}
```
