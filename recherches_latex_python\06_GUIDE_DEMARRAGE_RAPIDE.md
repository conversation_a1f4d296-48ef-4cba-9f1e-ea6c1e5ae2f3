# Guide de Démarrage Rapide - Conversion LaTeX vers Python

## 🚀 INSTALLATION RAPIDE

### Méthode recommandée (la plus stable)
```bash
# Création d'un environnement virtuel
python -m venv latex_env
source latex_env/bin/activate  # Linux/Mac
# ou latex_env\Scripts\activate  # Windows

# Installation des dépendances dans l'ordre
pip install antlr4-python3-runtime==4.9.3
pip install latex2sympy2
pip install sympy matplotlib numpy
```

### Vérification de l'installation
```python
# Test rapide
from latex2sympy2 import latex2sympy
result = latex2sympy(r"x^2 + 1")
print(result)  # Doit afficher: x**2 + 1
```

## ⚡ CONVERSIONS DE BASE (5 MINUTES)

### 1. Expressions simples
```python
from latex2sympy2 import latex2sympy

# Polynômes
latex2sympy(r"x^2 + 2x + 1")        # x**2 + 2*x + 1
latex2sympy(r"a^3 - b^3")           # a**3 - b**3

# Fractions
latex2sympy(r"\frac{1}{x}")         # 1/x
latex2sympy(r"\frac{x+1}{x-1}")     # (x + 1)/(x - 1)

# Racines
latex2sympy(r"\sqrt{x}")            # sqrt(x)
latex2sympy(r"\sqrt[3]{x}")         # x**(1/3)
```

### 2. Fonctions mathématiques
```python
# Trigonométrie
latex2sympy(r"\sin(x)")             # sin(x)
latex2sympy(r"\cos(2x)")            # cos(2*x)
latex2sympy(r"\tan(\pi x)")         # tan(pi*x)

# Logarithmes et exponentielles
latex2sympy(r"\log(x)")             # log(x)
latex2sympy(r"\ln(x)")              # log(x)
latex2sympy(r"e^x")                 # exp(x)
latex2sympy(r"e^{-x^2}")            # exp(-x**2)
```

### 3. Calcul différentiel et intégral
```python
# Dérivées (notation limitée)
latex2sympy(r"\frac{d}{dx}x^2")     # Peut nécessiter du préprocessing

# Intégrales
latex2sympy(r"\int x^2 dx")         # Integral(x**2, x)
latex2sympy(r"\int_0^1 x dx")       # Integral(x, (x, 0, 1))

# Sommes
latex2sympy(r"\sum_{i=1}^n i")      # Sum(i, (i, 1, n))
```

## 🔧 FONCTION UNIVERSELLE (COPIER-COLLER)

```python
def convertir_latex_python(latex_str, debug=False):
    """
    Fonction universelle de conversion LaTeX vers Python
    
    Args:
        latex_str (str): Expression LaTeX à convertir
        debug (bool): Affichage des informations de debug
    
    Returns:
        sympy.Expr ou None: Expression SymPy convertie
    """
    import re
    from latex2sympy2 import latex2sympy
    
    if debug:
        print(f"Input: {latex_str}")
    
    # Étape 1: Nettoyage de base
    cleaned = latex_str.strip()
    cleaned = re.sub(r'\s+', ' ', cleaned)  # Espaces multiples
    cleaned = cleaned.replace('$$', '').replace('$', '')  # Délimiteurs math
    
    # Étape 2: Remplacements courants
    replacements = {
        # Ensembles de nombres
        r'\\mathbb\{R\}': 'R',
        r'\\mathbb\{N\}': 'N',
        r'\\mathbb\{Z\}': 'Z',
        r'\\mathbb\{Q\}': 'Q',
        r'\\mathbb\{C\}': 'C',
        
        # Texte et formatage
        r'\\text\{([^}]+)\}': r'\1',
        r'\\mathrm\{([^}]+)\}': r'\1',
        r'\\mathcal\{([A-Z])\}': r'\1',
        
        # Parenthèses automatiques
        r'\\left\(': '(',
        r'\\right\)': ')',
        r'\\left\[': '[',
        r'\\right\]': ']',
        r'\\left\{': '{',
        r'\\right\}': '}',
    }
    
    for pattern, replacement in replacements.items():
        cleaned = re.sub(pattern, replacement, cleaned)
    
    if debug:
        print(f"Cleaned: {cleaned}")
    
    # Étape 3: Tentatives de conversion
    try:
        # Tentative principale avec latex2sympy2
        result = latex2sympy(cleaned)
        if debug:
            print(f"Success with latex2sympy2: {result}")
        return result
        
    except Exception as e1:
        if debug:
            print(f"latex2sympy2 failed: {e1}")
        
        try:
            # Tentative avec SymPy natif
            from sympy.parsing.latex import parse_latex
            result = parse_latex(cleaned)
            if debug:
                print(f"Success with SymPy native: {result}")
            return result
            
        except Exception as e2:
            if debug:
                print(f"SymPy native failed: {e2}")
            
            # Dernière tentative: conversion manuelle basique
            try:
                manual_result = conversion_manuelle_basique(cleaned)
                if debug:
                    print(f"Manual conversion: {manual_result}")
                return manual_result
            except Exception as e3:
                if debug:
                    print(f"All methods failed. Last error: {e3}")
                return None

def conversion_manuelle_basique(latex_str):
    """Conversion manuelle pour cas simples"""
    import re
    from sympy import sympify
    
    # Remplacements basiques
    simple_replacements = {
        r'\^(\w+)': r'**(\1)',
        r'\^{([^}]+)}': r'**(\1)',
        r'\\frac\{([^}]+)\}\{([^}]+)\}': r'((\1)/(\2))',
        r'\\sqrt\{([^}]+)\}': r'sqrt(\1)',
        r'\\sin': 'sin',
        r'\\cos': 'cos',
        r'\\tan': 'tan',
        r'\\log': 'log',
        r'\\ln': 'log',
        r'\\exp': 'exp',
        r'\\pi': 'pi',
    }
    
    result = latex_str
    for pattern, replacement in simple_replacements.items():
        result = re.sub(pattern, replacement, result)
    
    # Tentative de parsing avec SymPy
    return sympify(result)

# EXEMPLES D'USAGE
if __name__ == "__main__":
    # Tests rapides
    exemples = [
        r"x^2 + 1",
        r"\frac{x+1}{x-1}",
        r"\sqrt{x^2 + y^2}",
        r"\sin(\pi x) + \cos(2\pi x)",
        r"e^{-x^2/2}",
        r"\int_0^1 x^2 dx",
        r"\sum_{n=1}^{\infty} \frac{1}{n^2}",
    ]
    
    print("=== TESTS DE CONVERSION ===")
    for latex in exemples:
        result = convertir_latex_python(latex, debug=False)
        status = "✓" if result else "✗"
        print(f"{status} {latex:<25} → {result}")
```

## 📊 UTILISATION AVEC NUMPY/MATPLOTLIB

```python
def plot_latex_function(latex_formula, x_range=(-10, 10), num_points=1000):
    """
    Trace une fonction définie en LaTeX
    
    Args:
        latex_formula (str): Formule LaTeX (une variable x)
        x_range (tuple): Intervalle de tracé
        num_points (int): Nombre de points
    """
    import numpy as np
    import matplotlib.pyplot as plt
    from sympy.utilities.lambdify import lambdify
    from sympy import symbols
    
    # Conversion LaTeX → SymPy
    expr = convertir_latex_python(latex_formula)
    if expr is None:
        print(f"Erreur: impossible de convertir {latex_formula}")
        return
    
    # Création de la fonction numérique
    x = symbols('x')
    func = lambdify(x, expr, 'numpy')
    
    # Génération des points
    x_vals = np.linspace(x_range[0], x_range[1], num_points)
    
    try:
        y_vals = func(x_vals)
        
        # Tracé
        plt.figure(figsize=(10, 6))
        plt.plot(x_vals, y_vals, linewidth=2)
        plt.title(f'$f(x) = {latex_formula}$', fontsize=14)
        plt.xlabel('x', fontsize=12)
        plt.ylabel('f(x)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.show()
        
    except Exception as e:
        print(f"Erreur lors du tracé: {e}")

# Exemples d'usage
plot_latex_function(r"x^2 - 4")
plot_latex_function(r"\sin(x) + \cos(2x)")
plot_latex_function(r"e^{-x^2}")
```

## 🧮 CALCULS SYMBOLIQUES RAPIDES

```python
def calculer_derivee(latex_formula, variable='x'):
    """Calcule la dérivée d'une fonction LaTeX"""
    from sympy import diff, symbols
    
    expr = convertir_latex_python(latex_formula)
    if expr is None:
        return None
    
    var = symbols(variable)
    derivee = diff(expr, var)
    
    print(f"f(x) = {expr}")
    print(f"f'(x) = {derivee}")
    
    return derivee

def calculer_integrale(latex_formula, variable='x', bornes=None):
    """Calcule l'intégrale d'une fonction LaTeX"""
    from sympy import integrate, symbols
    
    expr = convertir_latex_python(latex_formula)
    if expr is None:
        return None
    
    var = symbols(variable)
    
    if bornes:
        integrale = integrate(expr, (var, bornes[0], bornes[1]))
        print(f"∫[{bornes[0]} à {bornes[1]}] {expr} dx = {integrale}")
    else:
        integrale = integrate(expr, var)
        print(f"∫ {expr} dx = {integrale}")
    
    return integrale

# Exemples
calculer_derivee(r"x^3 + 2x^2 + x + 1")
calculer_integrale(r"x^2", bornes=(0, 1))
```

## 🔍 DEBUGGING RAPIDE

```python
def debug_conversion(latex_str):
    """Diagnostic complet d'une conversion"""
    print("="*50)
    print(f"DIAGNOSTIC: {latex_str}")
    print("="*50)
    
    # Test avec debug activé
    result = convertir_latex_python(latex_str, debug=True)
    
    if result:
        print(f"\n✓ SUCCÈS: {result}")
        print(f"Type: {type(result)}")
        
        # Test d'évaluation numérique
        try:
            if hasattr(result, 'evalf'):
                numeric = result.evalf()
                print(f"Valeur numérique: {numeric}")
        except:
            print("Évaluation numérique impossible")
    else:
        print("\n✗ ÉCHEC de toutes les méthodes")
    
    print("="*50)

# Usage pour diagnostiquer des problèmes
debug_conversion(r"\frac{x^2 + 1}{\sqrt{x}}")
```

## 📚 RESSOURCES RAPIDES

### Documentation essentielle
- **latex2sympy2**: https://pypi.org/project/latex2sympy2/
- **SymPy**: https://docs.sympy.org/
- **Exemples LaTeX**: https://en.wikibooks.org/wiki/LaTeX/Mathematics

### Commandes LaTeX les plus courantes
```latex
# Fractions
\frac{a}{b}

# Exposants et indices
x^2, x_{ij}, x^{n+1}

# Racines
\sqrt{x}, \sqrt[n]{x}

# Fonctions
\sin(x), \cos(x), \tan(x), \log(x), \ln(x), \exp(x)

# Intégrales et sommes
\int f(x) dx, \int_a^b f(x) dx
\sum_{i=1}^n a_i, \prod_{i=1}^n a_i

# Dérivées
\frac{d}{dx}f(x), \frac{\partial}{\partial x}f(x,y)

# Limites
\lim_{x \to 0} f(x)
```

### Erreurs courantes et solutions
1. **Erreur ANTLR**: Installer `antlr4-python3-runtime==4.9.3`
2. **Commande non reconnue**: Utiliser le préprocessing
3. **Timeout**: Simplifier l'expression ou utiliser le cache
4. **Symboles Unicode**: Convertir en commandes LaTeX standard

## 🎯 CHECKLIST DE DÉMARRAGE

- [ ] Environnement virtuel créé
- [ ] Dependencies installées (antlr4 + latex2sympy2)
- [ ] Test de base réussi
- [ ] Fonction universelle copiée
- [ ] Premier exemple de tracé testé
- [ ] Documentation bookmarkée

**Vous êtes prêt à convertir du LaTeX vers Python ! 🎉**
