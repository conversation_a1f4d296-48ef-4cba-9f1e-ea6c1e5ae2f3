🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 50
🔗 75
🔗 100
🔗 125
🔗 150
🔗 175
🔗 200
🔗 225
🔗 250
🔗 Training Steps
🔗 0.30
🔗 0.35
🔗 0.40
🔗 0.45
🔗 0.50
🔗 0.55
🔗 0.60
🔗 0.65
🔗 0.70
🔗 In-Distribution Accuracy
🔗 AZR-Llama3.1-8b
🔗 AZR-3B-Coder
🔗 AZR-7B-Coder
🔗 AZR-14B-Coder
🔗 (a)
🔗 Model Family
🔗 Variant
🔗 Code Avg
🔗 Math Avg
🔗 Total Avg
🔗 Llama3.1-8b
🔗 28.5
🔗 3.4
🔗 16.0
🔗 Llama3.1-8b
🔗 + SimpleRL[85]
🔗 33.7+5.2
🔗 7.2+3.8
🔗 20.5+4.5
🔗 Llama3.1-8b
🔗 + AZR (Ours)
🔗 31.6+3.1
🔗 6.8+3.4
🔗 19.2+3.2
🔗 Qwen2.5-3B Coder
🔗 51.2
🔗 18.8
🔗 35.0
🔗 Qwen2.5-3B Coder
🔗 + AZ<PERSON> (Ours)
🔗 54.9+3.7
🔗 26.5+7.7
🔗 40.7+5.7
🔗 Qwen2.5-7B Coder
🔗 56.6
🔗 23.9
🔗 40.2
🔗 Qwen2.5-7B Coder
🔗 + AZR (Ours)
🔗 61.6+5.0
🔗 39.1+15.2
🔗 50.4+10.2
🔗 Qwen2.5-14B Coder
🔗 60.0
🔗 20.2
🔗 40.1
🔗 Qwen2.5-14B Coder
🔗 + AZR (Ours)
🔗 63.6+3.6
🔗 43.0+22.8
🔗 53.3+13.2
🔗 (b)
🔗 Figure 6. (a) In-Distribution & (b) Out-of-Distribution Reasoning Task Performances. (a) Scores on CruxEval-I, CruxEval-O,
🔗 and LiveCodeBench-Execution, which correspond to abduction, deduction, and deduction task types respectively, used to evaluate
🔗 in-distribution abilities of AZR during training across different model sizes and types; (b) Out-of-distribution reasoning performance,
🔗 reported as the average of code tasks, math tasks, and their overall average, across different model sizes and types. A detailed breakdown
🔗 of all benchmark results can be found in Table 5.
🔗 self-play process. Strikingly, although the coder base model variant started with a lower average performance in math than the vanilla
🔗 base model (23.9 vs. 27.5), it ultimately outperformed it after AZR training. This highlights the importance of initial code competency
🔗 as a catalyst for enhancing broader reasoning abilities within the Absolute Zero Reasoner approach.
🔗 Research Question 3: How does varying model size effect AZR’s in-distribution and out-of-distribution
🔗 capabilities?
🔗 We examine the effects of scaling model size and present both in-distribution and out-of-distribution results in Figure 6
🔗 (a) and (b), respectively. Given the strong performance of coder models in the 7B category, we extend the analysis by evaluating smaller
🔗 and larger variants: Qwen2.5-3B-Coder and Qwen2.5-14B-Coder. Due to the absence of existing baselines for these zero-style
🔗 reasoner models, we compare each model’s performance to its corresponding base coder model.
🔗 The results reveal a clear trend: our method delivers greater gains on larger, more capable models. In the in-distribution setting, the 7B
🔗 and 14B models continue to improve beyond 200 training steps, whereas the smaller 3B model appears to plateau. For out-of-distribution
🔗 domains, larger models also show greater overall performance improvements than smaller ones: +5.7, +10.2, +13.2 overall performance
🔗 gains, respectively for 3B, 7B and 14B. This is an encouraging sign, since base models continue to improve and also suggesting that
🔗 scaling enhances the effectiveness of AZR. In future work, we aim to investigate the scaling laws that govern performance in the Absolute
🔗 Zero paradigm.
🔗 Research Question 4: Any interesting observations by changing the model class?
🔗 We also evaluate our method
🔗 on a different model class, using Llama3.1-8B as the base shown in Figure 6. Unlike the 3B and 14B categories, this setting has an
🔗 existing baseline, SimpleRL (Zeng et al., 2025b), which enables a direct comparison. Although Llama3.1-8B is less capable than
🔗 the Qwen2.5 models, our method still produces moderate improvements (+3.2), demonstrating AZR’s effectiveness even on relatively
🔗 weaker models. However, these gains appear more limited, which aligns with our earlier observation that performance improvements
🔗 tend to scale with initial base model potency.
🔗 Research Question 5: Any interesting behaviors or patterns observed during AZR training?
🔗 We observed
🔗 interesting response patterns in both the proposal and solution stages. The model is capable of proposing diverse programs, such as
🔗 string manipulation tasks, dynamic programming problems, and practical cases (e.g., calculating a triangle’s area using Heron’s formula).
🔗 We show a concrete example in Figure 7, where AZR proposes a code problem that searches for the sum of continuous sub-arrays
🔗 matching a target value and solves it through trial-and-error.
🔗 Overall, the models trained exhibits distinct reasoning patterns depending on the task type. For example, when solving abduction tasks,
🔗 it repeatedly tests different input patterns, self-correcting until the reasoned output matches the given input. When predicting outputs,
🔗 it steps through the code and records structured intermediate results (such as dynamic programming arrays) until the final output is
🔗 reached. When inducting programs from given inputs, outputs, and descriptions, the model systematically checks each test case to
🔗 confirm that its program produces correct results. We showcase more concrete examples of these behaviors in Figures 18 and 20 to 26.
🔗 We also share some fun “vibe checks” such as solving Sudoku and solving the sum-product game in Figures 40 and 41.
🔗 Intermediate Planning During Code Response. Another interesting pattern emerged in our AZR models during the code induction
🔗 task: the final code outputs were often interleaved with comments that resembled immediate step-by-step plans, reminiscent of the ReAct
🔗 prompting framework (Yao et al., 2023). A similar behavior has been observed in recent formal math proving models, such as DeepSeek
🔗 10