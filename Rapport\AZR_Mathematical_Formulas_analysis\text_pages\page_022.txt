🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Model
🔗 Data Curation
🔗 Base Model
🔗 Oat-7B (<PERSON> et al., 2025)
🔗 8.5k math pairs (<PERSON><PERSON><PERSON><PERSON> et al., 2021)
🔗 Qwen2.5-7B-Math
🔗 SimpleRL-Zoo (<PERSON><PERSON> et al., 2025b)
🔗 8.5k math pairs (<PERSON><PERSON><PERSON><PERSON> et al., 2021)
🔗 Qwen2.5-7B-Base
🔗 OpenReasonerZero (<PERSON> et al., 2025)
🔗 57k STEM + math samples
🔗 Qwen2.5-7B-Base
🔗 PRIME-Zero (<PERSON><PERSON> et al., 2025)
🔗 457k math + 27k code problems
🔗 Qwen2.5Math-7B-Base
🔗 CodeR1-Zero-7B-LC2k-1088 (<PERSON> & <PERSON>, 2025)
🔗 2k Leetcode pairs
🔗 Qwen2.5-7B-Instruct-1M
🔗 CodeR1-Zero-7B-12k-832 (<PERSON> & <PERSON>, 2025)
🔗 2k Leetcode + 10k TACO pairs (<PERSON> et al., 2023)
🔗 Qwen2.5-7B-Instruct-1M
🔗 AceCoder-7B-Ins-RM (<PERSON><PERSON> et al., 2025a)
🔗 22k code data
🔗 Qwen2.5-7B-Instruct
🔗 AceCoder-7B-Ins-Rule (<PERSON><PERSON> et al., 2025a)
🔗 22k code data
🔗 Qwen2.5-7B-Instruct
🔗 AceCoder-7B-Code-RM (Zeng et al., 2025a)
🔗 22k code data
🔗 Qwen2.5-7B-Coder
🔗 AceCoder-7B-Code-Rule (Zeng et al., 2025a)
🔗 22k code data
🔗 Qwen2.5-7B-Coder
🔗 Qwen-7B-Instruct (Yang et al., 2024a)
🔗 1M SFT + 150k RL pairs
🔗 Qwen2.5-7B-Base
🔗 AZR-7B (Ours)
🔗 No data
🔗 Qwen2.5-7B-Base
🔗 AZR-7B-Coder (Ours)
🔗 No data
🔗 Qwen2.5-7B-Coder
🔗 Table 4. Reasoner Training Data Source and Base Model.
🔗 logging
🔗 random
🔗 multiprocessing
🔗 pebble
🔗 subprocess
🔗 threading
🔗 datetime
🔗 time
🔗 hashlib
🔗 calendar
🔗 bcrypt
🔗 os.sys
🔗 os.path
🔗 sys.exit
🔗 os.environ
🔗 Figure 8. Forbidden Python Modules. List of Python modules forbidden to exist in proposed tasks’ programs.
🔗 C. More Results
🔗 C.1. Out-of-Distribution Performance Breakdown
🔗 We plot the out-of-distribution performance, broken down by each benchmark and in aggregate, across training steps for our 7B, 7B-Coder,
🔗 14B, and 14B-Coder models in Figures 28 to 31. We observe a strong correlation between training using AZR and improvements in both
🔗 mathematical and coding reasoning capabilities. Moreover, our models are trained for more steps than typical zero-style reasoners; while
🔗 overfitting can occur with static datasets, it is less likely in AZR due to dynamically proposed tasks.
🔗 C.2. In-Distribution Results
🔗 Since we have defined the task domains as input prediction and output prediction, we can directly evaluate our model’s capabilities in these
🔗 areas using popular code reasoning benchmarks: CruxEval-I(nput), CruxEval-O(utput), and LiveCodeBench-Execution (LCB-E) (Gu
🔗 et al., 2024; Jain et al., 2024), where CruxEval-O and LCB-E is solving the deduction task, and CruxEval-I is solving the abduction task.
🔗 In Figure 14, we visualize the evolution of these metrics during the training of Absolute Zero Reasoner-base-7b. As training
🔗 progresses, we observe a consistent improvement in in-distribution performance across steps. While these three benchmark curves do
🔗 not perfectly correlate with broader coding or math reasoning capabilities (compare this with Figure 28), they serve as useful proxies for
🔗 tracking task-specific progress.
🔗 C.3. Interplay Between Propose and Solve Roles
🔗 We visualize the training dynamics between the propose and solve roles over training steps in Figures 15 to 17. We observe that, in
🔗 general, the solve roles produce more output tokens than the propose role. Intuitively, this makes sense: the propose role emphasizes
🔗 creativity and generation of novel tasks, whereas the solve role requires deeper reasoning, which naturally leads to longer outputs.
🔗 Interestingly, we also observe a consistent ordering in token length across reasoning types—abduction and deduction tasks tend to result
🔗 in shorter outputs than induction tasks during problem solving. This aligns with our intuition, as we observed the model engaging
🔗 in trial-and-error reasoning—repeatedly generating hypothesized inputs, evaluating their outcomes, and reflecting and retrying when
🔗 subsequent deductions fail to produce the correct output. To our knowledge, this is the first time such a clear distinction in token length
🔗 1 VALIDATE_CODE_TEMPLATE = """{code}
🔗 2 repr(f({ inputs }))"""
🔗 3
🔗 4 exec( VALIDATE_CODE_TEMPLATE )
🔗 Figure 9. Python Program to Check Valid Code.
🔗 22