🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 1 EVAL_INPUT_PREDICTION_TEMPLATE = """{code}
🔗 2 {gold_output} == f({ agent_input })"""
🔗 3
🔗 4 exec( EVAL_INPUT_PREDICTION_TEMPLATE )
🔗 Figure 10. Python Code to Check Agent Input Abduction Correctness.
🔗 1 EVAL_OUTPUT_PREDICTION_TEMPLATE = """{code}
🔗 2 eval ({ gold_output }) == eval ({ agent_output })"""
🔗 3
🔗 4 exec( EVAL_OUTPUT_PREDICTION_TEMPLATE )
🔗 Figure 11. Python Code to Check Agent Output Deduction Correctness.
🔗 1 EVAL_FUNCTION_PREDICTION_TEMPLATE = """{code}
🔗 2 matches = []
🔗 3 for gold_input , gold_output in zip({ gold_inputs}, {gold_outputs }):
🔗 4

📐 FORMULE MATHÉMATIQUE:
    match = {gold_output} == f({ gold_input })

🔗 5
🔗 matches.append(match)
🔗 6 """
🔗 7
🔗 8 exec( EVAL_OUTPUT_PREDICTION_TEMPLATE )
🔗 Figure 12. Python Code to Check Agent Function Induction Correctness.
🔗 1 CHECK_DETERMINISM_TEMPLATE = """{code}
🔗 2 returns = f({ inputs })
🔗 3 if returns != f({ inputs }):
🔗 4
🔗 raise
🔗 Exception(’Non -deterministic
🔗 code ’)
🔗 5 repr(returns)"""
🔗 6
🔗 7 exec( CHECK_DETERMINISM_TEMPLATE )
🔗 Figure 13. Python Code to Check Deterministic Program.
0

30

60

90

120

150

180

210

240

270

Training Steps

  0.35
  0.40
  0.45
  0.50
  0.55
  0.60
  0.65
Performance Score

CruxEval-I

CruxEval-O

LiveCodeBench-Execution

🔗 Figure 14. In-distribution Benchmark Score During Training. The evolution of CruxEval-I, CruxEval-O, and LiveCodeBench-
🔗 Execution during training for the Qwen2.5-7B base model trained using AZR.
🔗 23