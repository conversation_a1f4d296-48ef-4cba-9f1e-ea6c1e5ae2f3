🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 3.2. Learning Different Modes of Reasoning: Deduction, Induction, and Abduction
🔗 AZR uses code executor as both a flexible interface and a verifiable environment. This setup enables automatic construction, execution,
🔗 and validation of code reasoning tasks (<PERSON>, 2015; <PERSON><PERSON><PERSON><PERSON> et al., 2024). Give program space P, input space I and output space O
🔗 of a coding language, we define an AZR reasoning task as a triplet (p, i, o), where p ∈P is a program, i ∈I is an input, and o ∈O is
🔗 the corresponding output produced by running program on input, o = p(i). AZR learns by reasoning about different parts of this task
🔗 triplet, using three distinct core reasoning modes, each of which focuses on inferring one part of the triplet given the others:
🔗 1. Deduction: predicting the output o given a program p and input i, capturing step-by-step logical reasoning.
🔗 • As a proposer, AZR is conditioned on the task type α = deduction and K reference examples from the deduction buffer Ddeduction
🔗 (all task buffers are outlined in Section 3.3), and generates a pair (p, i). The environment e then executes p(i) to compute o,
🔗 completing the triplet (p, i, o), which is added to the buffer if non-error output was produced.
🔗 • As a solver, the model receives (p, i) and predicts the output oπ. The predicted output is verified using type-aware value equality
🔗 in python to account for possible variations (such as set ordering or fractions).
🔗 2. Abduction: inferring a plausible input i given the program p and an output o, resembling trial-and-error or online search.
🔗 • As a proposer, the policy πpropose’s input and output is almost the same as the proposer for the deduction task, except that the task
🔗 type α = abduction is changed as an input. The model generates a pair (p, i) conditioned on α and reference examples. Then we
🔗 executes p(i) and get the triplet (p, i, o).
🔗 • As a solver, the model receives (p, o) and predicts iπ. The solution is verified by checking whether p(iπ) = o. Since programs
🔗 may not be bĳective, we use output value equivalence rather than requiring exact input matches.
🔗 3. Induction: synthesizing a program p from a set of in-out examples {(in, on)}, requiring generalization from partial information.
🔗 • As a proposer, AZR samples a valid program p from Dabduction ∪Ddeduction, generates N new inputs and a message m, and uses the

📐 FORMULE MATHÉMATIQUE:
    environment to compute corresponding outputs. This forms an extended task representation (p, {(in, on)}, m), which is stored

🔗 in the induction buffer Dinduction. Since infinitely many functions can map the inputs to the outputs, making the induction task
🔗 under-constrained, the message m helps properly condition the problem for the solver.
🔗 • As a solver, the model is shown the first half of the input-output pairs and the message m, and must synthesize a program pπ that
🔗 correctly maps the remaining hidden inputs to their outputs. The use of held-out examples discourages overfitting through if-else
🔗 logic and promotes generalized induction.
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 3.2. Learning Different Modes of Reasoning: Deduction, Induction, and Abduction
🔗 AZR uses code executor as both a flexible interface and a verifiable environment. This setup enables automatic construction, execution,
🔗 and validation of reasoning tasks (Stuart, 2015; Aryabumi et al., 2024). Give program space P, input space I and output space O of
🔗 a coding language, we define an AZR reasoning task as a triplet (p, i, o), where p →P is a program, i →I is an input, and o →O is
🔗 the corresponding output produced by running program on input, o = p(i). AZR learns by reasoning about different parts of this task
🔗 triplet, using three distinct core reasoning modes, each of which focuses on inferring one part of the triplet given the others:
🔗 1. Deduction: predicting the output o given a program p and input i, capturing step-by-step logical reasoning.
🔗 • As a proposer, AZR is conditioned on the task type α = deduction and K reference examples from the deduction buffer Ddeduction
🔗 (all task buffers are outlined in Section 3.3), and generates a pair (p, i). The environment e then executes p(i) to compute o,
🔗 completing the triplet (p, i, o), which is added to the buffer if non-error output was produced.
🔗 • As a solver, the model receives (p, i) and predicts the output oπ. The predicted output is verified using type-aware value equality
🔗 in python to account for possible variations (such as set ordering or fractions).
🔗 2. Abduction: inferring a plausible input i given the program p and an output o, resembling trial-and-error or online search.
🔗 • As a proposer, the policy πpropose’s input and output is almost the same as the proposer for the deduction task, except that the task
🔗 type α = abduction is changed as an input. The model generates a pair (p, i) conditioned on α and reference examples. Then we
🔗 executes p(i) and get the triplet (p, i, o).
🔗 • As a solver, the model receives (p, o) and predicts iπ. The solution is verified by checking whether p(iπ) = o. Since programs
🔗 may not be bijective, we use output value equivalence rather than requiring exact input matches.
🔗 3. Induction: synthesizing a program p from a set of in-out examples {(in, on)}, requiring generalization from partial information.
🔗 • As a proposer, AZR samples a valid program p from Dabduction ∪Ddeduction, generates N new inputs and a message m, and uses the

📐 FORMULE MATHÉMATIQUE:
    environment to compute corresponding outputs. This forms an extended task representation (p, {(in, on)}, m), which is stored

🔗 in the induction buffer Dinduction. Since infinitely many functions can map the inputs to the outputs, making the induction task
🔗 under-constrained, the message m helps properly condition the problem for the solver.
🔗 • As a solver, the model is shown the first half of the input-output pairs and the message m, and must synthesize a program pπ that
🔗 correctly maps the remaining hidden inputs to their outputs. The use of held-out examples discourages overfitting through if-else
🔗 logic and promotes generalized induction.
🔗 Program Triplet
🔗 Input: "Hello World"
🔗 1
🔗 def f(x):
🔗 2
🔗 return x
🔗 Output: "Hello World"
🔗 Figure 5. The Seed AZR Zero Triplet.
🔗 The above
🔗 identity function triplet was the only triplet provided
🔗 to AZR to initiate its self-bootstrap propose-and-solve
🔗 RLVR loop. We note that the base LLM is fully ca-
🔗 pable of initiating the AZR loop without any seed pro-
🔗 gram; its inclusion illustrates our approach’s flexibility:
🔗 we can optionally initialize seed programs with existing
🔗 datasets of varying complexity, and we initialized ours
🔗 with the simplest program.
🔗 Each reasoning task type leverages code as an expressive and verifiable
🔗 medium, aligning with the Absolute Zero Paradigm’s goals of fully self-
🔗 improving systems in open-ended domains (DeepSeek-AI et al., 2025; Lam-
🔗 bert et al., 2024). All prompts used by three different task types and two
🔗 types of roles within a task type are shown in Figures 34 to 39. Next, we
🔗 outline exact details of our algorithm.
🔗 3.3. Absolute Zero Reasoner Learning Algorithm
🔗 In this section, we will discuss details of our AZR self-play algorithm, includ-
🔗 ing initialization of buffers 3.3.1, usage of thse buffers 3.3.2, construction of
🔗 valid tasks 3.3.3, validating solutions 3.3.4, and finally advantage estimator
🔗 calculation 3.3.5. We outline the overall recipe of the self-play procedure of
🔗 AZR in Algorithm 1.
🔗 3.3.1. BUFFER INITIALIZATION
🔗 To initialize AZR self-play, we first generate a seed set of valid triplets using
🔗 the base language model. Each prompt samples up to K triplets from the
🔗 current seed buffer Dseed as references. When Dseed is empty at time 0, we
🔗 fall back to the zero triplet show in Figure 5. During the seeding stage, we
🔗 use the same proposer prompts detailed in Figures 34 to 36.
🔗 First, for deduction and abduction tasks, the LLM is prompted to generate
🔗 (p, i) pairs, which are filtered, executed, and stored as valid triplets. We
🔗 initialize D0
🔗 abduction = D0

📐 FORMULE MATHÉMATIQUE:
    deduction = Dseed, where |Dseed| = B × S, where


📐 FORMULE MATHÉMATIQUE:
    B is the batch size, and S = 4 is a factor we fix in all experiments. All seed triplet’s program are stripped of global variables and

🔗 comments (Appendix C), but subsequent iterations of adding new triplets to the buffers are unaltered. No model updates occur during
🔗 this phase. Similarly, to initialize the induction buffer, we sample programs from Dseed, generate matching input sets and messages, and
🔗 collect valid examples until |D0

📐 FORMULE MATHÉMATIQUE:
    induction| = B × S.

🔗 6
🔗 Figure 5. The Seed AZR Zero Triplet. The above
🔗 identity function triplet was the only triplet provided
🔗 to AZR to initiate its self-bootstrap propose-and-solve
🔗 RLVR loop. We note that the base LLM is fully capable
🔗 of initiating the AZR loop without any seed program;
🔗 its inclusion illustrates our approach’s flexibility: we
🔗 can optionally initialize seed programs with existing
🔗 datasets of varying complexity, and we initialized ours
🔗 with the simplest program.
🔗 Each reasoning task type leverages code as an expressive and verifiable
🔗 medium, aligning with the Absolute Zero Paradigm’s goals of fully self-
🔗 improving systems in open-ended domains (DeepSeek-AI et al., 2025;
🔗 Lambert et al., 2024). All prompts used by three different task types and
🔗 two types of roles within a task type are shown in Figures 34 to 39. Next,
🔗 we outline exact details of our algorithm.
🔗 3.3. Absolute Zero Reasoner Learning Algorithm
🔗 In this section, we will discuss details of our AZR self-play algorithm, includ-
🔗 ing initialization of buffers 3.3.1, usage of thse buffers 3.3.2, construction of
🔗 valid tasks 3.3.3, validating solutions 3.3.4, and finally advantage estimator
🔗 calculation 3.3.5. We outline the overall recipe of the self-play procedure
🔗 of AZR in Algorithm 1.
🔗 3.3.1. Buffer Initialization
🔗 To initialize AZR self-play, we first generate a seed set of valid triplets using
🔗 the base language model. Each prompt samples up to K triplets from the
🔗 current seed buffer Dseed as references. When Dseed is empty at time 0, we
🔗 fall back to the zero triplet show in Figure 5. During the seeding stage, we
🔗 use the same proposer prompts detailed in Figures 34 to 36.
🔗 First, for deduction and abduction tasks, the LLM is prompted to generate
🔗 (p, i) pairs, which are filtered, executed, and stored as valid triplets. We
🔗 initialize D0
🔗 abduction = D0

📐 FORMULE MATHÉMATIQUE:
    deduction = Dseed, where |Dseed| = B × S, where B is the batch size, and S = 4 is a factor we fix in all

🔗 experiments. All seed triplet’s program are stripped of global variables and comments (Appendix D), but subsequent iterations of adding
🔗 new triplets to the buffers are unaltered. No model updates occur during this phase. Similarly, to initialize the induction buffer, we
🔗 sample programs from Dseed, generate matching input sets and messages, and collect valid examples until |D0

📐 FORMULE MATHÉMATIQUE:
    induction| = B × S.

🔗 6