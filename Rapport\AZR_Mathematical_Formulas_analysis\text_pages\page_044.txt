🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 # Task: Deduce the Function that Produced the Outputs from the Inputs
🔗 Given a set of input/output pairs and a message that describes the function, think through the
🔗 problem step by step to deduce a general code snippet. This code should produce the hidden
🔗 outputs from the hidden inputs, matching the original data-generating code that created the
🔗 input/output pairs. Place your final answer inside python tags! It may be helpful to work
🔗 through each input/output pair individually to test your function. If your function doesn’t
🔗 work as expected, revise it until it does. The final code snippet will be used to evaluate
🔗 your response, which is wrapped in ```python``` tags.

📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→

🔗 # Code Requirements:
🔗 - Name the entry function `f` (e.g., `def f(...): ...`), you can have nested definitions inside
🔗 `f`

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 - Ensure the function returns a value
🔗 - Include at least one input parameter
🔗 - Make the function deterministic
🔗 - AVOID THE FOLLOWING:
🔗 * Random functions or variables
🔗 * Date/time operations
🔗 * I/O operations (reading files, network requests)
🔗 * Printing or logging
🔗 * Any external state
🔗 - Ensure execution completes within 10 seconds on a modern CPU
🔗 - All imports and class definitions should be at the very top of the code snippet
🔗 - The snippet should end with a return statement from the main function `f()`, anything after
🔗 will be removed

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 # Input and Output Pairs:
🔗 {INPUT_OUTPUT_PAIRS}
🔗 # Message:
🔗 ```message
🔗 {MESSAGE}
🔗 ```
🔗 # Example Output:
🔗 ```python
🔗 def f(a):
🔗 return a
🔗 ```
🔗 Name your entry function `f()`!!!
🔗 Figure 39. Program Induction Task—Problem Solving Prompt.
🔗 45
🔗 Figure 39. Program Induction Task—Problem Solving Prompt.
🔗 44