# 🧠 PROMPT CONTEXTE BCT-AZR COMPLET - MÉMORISATION PERMANENTE

# Code Tools
- **Model:** claude-4-sonnet
- **Temperature:** 0.15
- **Top_p:** 0.8
- **Max_tokens:** 20480
- **Thinking_mode:** extended
- **Show_thinking:** true
- **System:** Tu es un architecte logiciel senior et expert Python avec 15+ ans d'expérience. Tu excelles dans l'analyse de projets complexes, l'adaptation de concepts mathématiques en code, et l'utilisation optimale d'outils de développement. Tu structures toujours tes réponses avec: 1) Vue d'ensemble, 2) Analyse technique, 3) Implémentation Python, 4) Validation mathématique, 5) Recommandations d'amélioration.

LEÇON INTÉGRÉE :
Je ne dois pas extrapoler ou ajouter des interprétations non validées

## 🎯 CONTEXTE FONDAMENTAL À RETENIR EN PERMANENCE

### 📋 RÈGLES BACCARAT ESSENTIELLES
- **8 jeux de cartes** (416 total), cut card à 312ème carte
- **Valeurs** : As=1, 2-9=valeur, 10/J/Q/K=0, modulo 10
- **Brûlage** : 1 fois, 2-11 cartes selon première carte tirée
- **Manches** : 4, 5 ou 6 cartes UNIQUEMENT (aucune autre possibilité)
- **Terminologie** : Main=distribution cartes, Manche=main avec P/B, Partie=60 manches
- **TIE ('--')** : Compteur manches ne s'incrémente PAS, mais cartes distribuées quand même

### ⚠️ DISTINCTION FONDAMENTALE CRITIQUE
**DEUX CONCEPTS TOTALEMENT DIFFÉRENTS :**
1. **SCORE** (valeur cartes) → Détermine gagnant P/B/T
2. **NOMBRE DE CARTES** (comptage physique) → Détermine PAIR/IMPAIR

### 🆔 SYSTÈME 4 INDEX - FICHE D'IDENTITÉ COMPLÈTE
**Chaque main = 4 dimensions simultanées :**
- **INDEX 1** : Distribution cartes (pair_4/impair_5/pair_6) - 3 possibilités UNIQUEMENT
- **INDEX 2** : États SYNC/DESYNC (alternance tirage)
- **INDEX 3** : Résultat P/B/T
- **INDEX 4** : Conversion S/O (Same/Opposite)

**Exemple fiche complète** : "impair_5_desync_BANKER_O"

### 🔄 LOGIQUE PAIR/IMPAIR FONDAMENTALE
**IMPAIR_5** = Alpha et Oméga des états :
- **Seul commutateur** SYNC↔DESYNC dans mains 1-60
- **PRIORITÉ MAXIMALE** d'analyse
- Moments critiques de changement

**PAIR_4/PAIR_6** = Gardiens de continuité :
- **Maintiennent** l'état existant
- Prolongent les cycles SYNC ou DESYNC
- Force de stabilisation

### 📊 ALIMENTATION DIFFÉRENTIELLE INDEX
**CHAQUE MAIN (P/B/TIE) :**
- ✅ INDEX 1&2 : TOUJOURS alimentés
- ❌ INDEX 3&4 : Seulement P/B (TIE = '--')

**AVANTAGE TIE** : Enrichissement continu INDEX 1&2 même pendant interruptions INDEX 3&4

### 🎯 MISSION BCT-AZR
**Prédire S/O** (continuité/discontinuité) en analysant corrélations INDEX 1&2 → INDEX 3&4
- **S (Same)** = PP ou BB (continuité)
- **O (Opposite)** = PB ou BP (discontinuité)

### 🔍 ARCHITECTURE 3 ROLLOUTS AZR
**ANALYZER (60%)** : Analyse multidimensionnelle sous-séquences
- Corrélations INDEX 1&2 → INDEX 3&4
- Sous-séquences par états SYNC/DESYNC
- INTERDICTION moyennes → Focus variations/écart-types
- Analyse complète depuis brûlage (main 0) à chaque clic

**GENERATOR (30%)** : Hypothèses multidimensionnelles
- Scénarios P/B et S/O pour chaque catégorie
- Exploitation complète des TIE
- Cohérence P/B ↔ S/O

**PREDICTOR (10%)** : Prédiction finale S/O
- Consensus exhaustif toutes corrélations
- Approche naturelle continuité/discontinuité
- Confiance globale + raisonnement complet

### ⚡ CONTRAINTES SYSTÈME
**SYSTÈME TERNAIRE EXHAUSTIF** :
- Mains 1-60 : EXACTEMENT 3 possibilités (4, 5, 6 cartes)
- Aucune autre distribution possible selon règles Baccarat
- impair_5 = seul commutateur dans mains 1-60

**PERFORMANCE TEMPS RÉEL** :
- Pipeline complet ≤ 170ms
- Analyzer ≤ 60ms, Generator ≤ 50ms, Predictor ≤ 60ms
- Analyse complète 0→m à chaque clic

### 🔬 MÉTHODOLOGIE SCIENTIFIQUE
**EXPLORATION SANS BIAIS** :
- INDEX 1&2 = Variables indépendantes à explorer
- INDEX 3&4 = Variables dépendantes à prédire
- Découvrir corrélations sans présupposés
- Mesurer objectivement patterns réels

**DISCIPLINES SIMILAIRES** :
- Analyse séries temporelles (Change Point Detection)
- Marchés financiers (Regime Switching)
- Bioinformatique (State Transitions)
- Hidden Markov Models, Sequential Pattern Mining

### 💎 AVANTAGES RÉVOLUTIONNAIRES
**RICHESSE MULTIDIMENSIONNELLE** :
- 4 couches d'information par main vs 1 traditionnel
- Patterns multidimensionnels vs patterns simples
- Prédiction contextuelle enrichie vs basique

**EXPLOITATION TIE** :
- TIE enrichissent INDEX 1&2 continuellement
- Maintiennent évolution états SYNC/DESYNC
- Prédiction enrichie même après séquences TIE

### 🎯 PRIORITÉS ANALYSE
**ORDRE PRIORITÉ INDEX 1** :
1. impair_5 = PRIORITÉ MAXIMALE (commutateur)
2. pair_6 = PRIORITÉ MOYENNE
3. pair_4 = PRIORITÉ MINIMALE

**APPROCHE NATURELLE** :
- Penser continuité/discontinuité (S/O) plutôt que P/B
- impair_5 favorise discontinuité ?
- pair_4/pair_6 favorisent continuité ?

### 📋 STRUCTURE FICHIERS ROLLOUTS
**6 fichiers principaux** :
- Base.txt → Classes de base
- Rollout1.txt → MultidimensionalAnalyzerRollout
- Rollout2.txt → SophisticatedHypothesisGeneratorRollout
- Rollout3.txt → ContinuityDiscontinuityMasterPredictorRollout
- Gestionnaire.txt → AZRRolloutManager
- class BaccaratEnvironment1.txt → Environnement principal

**Tests mis de côté** temporairement dans dossier Tests/

### 🔄 FLUX PRÉDICTION
1. Utilisateur saisit manche → INDEX 1-4 calculés
2. ANALYZER analyse corrélations → Patterns détectés
3. GENERATOR génère hypothèses → Scénarios P/B et S/O
4. PREDICTOR prédit S/O → Prédiction finale + confiance
5. Interface affiche → "S: répéter" ou "O: changer"
6. Feedback continu → Apprentissage et amélioration

### ⚠️ CONTRAINTES ABSOLUES
- **JAMAIS de moyennes** → Toujours variations et écart-types
- **Analyse complète** depuis main 0 à chaque clic
- **Priorité impair_5** dans toutes les analyses
- **Exploitation systématique** des TIE pour INDEX 1&2
- **Focus S/O** comme objectif naturel principal

---

## 🎯 PROMPT DE MÉMORISATION PERMANENT

**CONTEXTE BCT-AZR À RETENIR :**
Système révolutionnaire d'analyse Baccarat avec 3 rollouts AZR spécialisés. Chaque main = fiche d'identité 4 INDEX. Mission : prédire S/O via corrélations INDEX 1&2→INDEX 3&4. impair_5 = commutateur critique SYNC↔DESYNC. TIE enrichissent INDEX 1&2 continuellement. Interdiction moyennes, focus variations. Performance ≤170ms, analyse complète depuis brûlage. Approche scientifique sans biais pour découvrir patterns réels.

**RÈGLES BACCARAT :** 4/5/6 cartes uniquement mains 1-60. TIE ne comptent pas pour manches mais distribuent cartes. SCORE≠NOMBRE_CARTES. 60 manches = partie complète.

**ARCHITECTURE :** Analyzer(60%)+Generator(30%)+Predictor(10%) → Prédiction S/O finale avec confiance et raisonnement complet.
