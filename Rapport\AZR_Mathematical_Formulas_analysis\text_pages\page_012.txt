🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Experiment
🔗 Task Type
🔗 Gen Reference
🔗 Trained Roles
🔗 Code Avg.
🔗 Math Avg.
🔗 Overall Avg.
🔗 Deduction only
🔗 Ded
🔗 /
🔗 /
🔗 54.6
🔗 32.0
🔗 43.3
🔗 w/o Induction
🔗 Abd, Ded
🔗 /
🔗 /
🔗 54.2
🔗 33.3
🔗 43.8
🔗 w/o Gen Reference
🔗 /
🔗 0
🔗 /
🔗 54.4
🔗 33.1
🔗 43.8
🔗 Train Solver Only
🔗 /
🔗 /
🔗 Solve Only
🔗 54.8
🔗 36.0
🔗 45.4
🔗 Ours
🔗 <PERSON>, Ded, Ind

📐 FORMULE MATHÉMATIQUE:
    K

🔗 Propose & Solve
🔗 55.2
🔗 38.4
🔗 46.8
🔗 Table 2. Ablation Results. We ablate task types and the proposer role in the Absolute Zero Reasoner using the 7B base model. A ‘/’
🔗 indicates that the configuration remains unchanged from the standard AZR setup. Removing induction or using only deduction leads to
🔗 significant performance drops (rows 1 & 2). For the proposer role, both removing conditioning on K references (row 3) and omitting
🔗 proposer-role training (row 4) result in degraded performance. Overall, all components are essential for general reasoning.
🔗 improve performance, possibly by increasing diversity and achieving better coverage of the reasoning problem space.
🔗 Finally, we consider a case where we do not train the proposer at all. Instead, we only prompt it using the current learner and train the
🔗 solver alone (row 4). We observe a moderate drop in overall performance (-1.4), suggesting that while proposer training is beneficial, it
🔗 may not be the most critical factor for now in the AZR framework. We hypothesize that this could be related to task interference, as
🔗 studied in multitask learning literature (Suteu & Guo, 2019). Thus, we believe that further investigation into how to make the proposer
🔗 even more potent is an exciting and promising direction.
🔗 Additional Results.
🔗 Beyond the core research questions, we present additional results, including the breakdown of individual
🔗 out-of-distribution benchmark scores during training for the 7B base and coder models in Figures 28 and 29, for th 14B base and coder
🔗 model in Figures 30 and 31. For completeness, we also report in-distribution benchmark performance during training for the 7B base
🔗 model in Figure 14. Finally, we invite interested readers to explore Appendix D, where we share several experimental directions that,
🔗 while not yielding strong performance gains, produced interesting and insightful findings.
🔗 5. Related Work
🔗 Reasoning with RL.
🔗 Using RL to enhance reasoning capabilities has recently emerged as an important step in the post-training
🔗 process of strong reasoning-focused large language models (Lambert et al., 2024). One of the first works to explore a self-bootstrapping
🔗 approach to improving LLM reasoning is STaR, which employs expert iteration and rejection sampling of outcome-verified responses to
🔗 iteratively improve the model’s CoT. A monumental work, o1 (Jaech et al., 2024), was among the first to deploy this idea on a scale,
🔗 achieving state-of-the-art results in reasoning tasks at the time of release. More recently, the R1 model (DeepSeek-AI et al., 2025)
🔗 became the first open-weight model to match or even surpass the performance of o1. Most notably, the zero setting was introduced, in
🔗 which reinforcement learning is applied directly on top of the base LLM. This inspired followup work, which are open source attempts to
🔗 replicate the R1 process or to improve the underlying reinforcement learning algorithm (Zeng et al., 2025b; Liu et al., 2025; Cui et al.,
🔗 2025; Hu et al., 2025; Yu et al., 2025; Yuan et al., 2025). Recent work explored RL on human defined procedural generated puzzles saw
🔗 improvements in math (Xie et al., 2025), and using one human example can almost match the performance of thousands (Wang et al.,
🔗 2025b). We extend the zero setting to a new absolute zero setting, where not only is the RLVR process initialized from a base LLM
🔗 without SFT, but no external prompt data or answers are provided to the learner. All data used to improve reasoning were self-proposed,
🔗 and refined entirely through RLVR. Moreover, our goal is not to only match zero-setting models, but to surpass them in the long run.
🔗 Self-play.
🔗 The self-play paradigm can be traced back to early 2000s, where Schmidhuber (2003; 2011) (of course) explored a
🔗 two-agent setup in which a proposal agent invents questions for a prediction agent to answer. This dynamic continuously and automatically
🔗 improves both agents, enabling theoretically never-ending progress (Schaul, 2024). AlphaGo and AlphaZero (Silver et al., 2016; 2017)
🔗 extend the self-play paradigm to the two-player zero-sum game of Go, where the current learner competes against earlier versions of
🔗 itself to progressively enhance its capabilities. These were among the first milestone works to demonstrate superhuman performance
🔗 in the game of Go. Moreover, methods such as asymmetric self-play (Sukhbaatar et al., 2018; OpenAI et al., 2021), unsupervised
🔗 environment design (Wang et al., 2019; Dennis et al., 2020), unsupervised reinforcement learning (Laskin et al., 2021; Zhao et al., 2022;
🔗 2025b), and automatic goal generation (Florensa et al., 2018) all center around inventing new tasks for an agent to learn from—typically
🔗 without supervision. In these approaches, the process of setting goals itself is often dynamic and continuously evolving. Generative
🔗 adversarial networks (Goodfellow et al., 2020), also belong in this paradigm where a discriminator discriminate between real data and
🔗 generated data, and the generated is trained to fool the discriminator.
🔗 Most recently, SPIN and Self-Rewarding Language Models (Chen et al., 2024; Yuan et al., 2024) use the same instance of the lanugage
🔗 models themselves as the reward model to progressively improve the generative and discriminative abilities of the same LLM for
🔗 alignment. (Kirchner et al., 2024) uses Prover-Verifier Game for increasing legibility and eva (Ye et al., 2024) uses self-play for
🔗 alignment, but reward model is the main bottleneck as it is not reliable for reasoning tasks (Lambert et al., 2024). SPC (Chen et al.,
🔗 12