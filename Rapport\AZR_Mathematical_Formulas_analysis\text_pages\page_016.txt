🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>,
<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>,
🔗 H. Tülu 3: Pushing frontiers in open language model post-training. CoRR, abs/2411.15124, 2024. doi: 10.48550/ARXIV.2411.15124.
🔗 URL https://doi.org/10.48550/arXiv.2411.15124.
🔗 <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>g, C., Pinto, L., and <PERSON><PERSON><PERSON>, <PERSON>.
🔗 URLB: unsu-
🔗 pervised reinforcement learning benchmark.
🔗 In <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> (eds.), Proceedings of the Neural In-
🔗 formation Processing Systems Track on Datasets and Benchmarks 1, NeurIPS Datasets and Benchmarks 2021, De-
🔗 cember 2021,
🔗 virtual,
🔗 2021.
🔗 URL https://datasets-benchmarks-proceedings.neurips.cc/paper/2021/hash/
🔗 091d584fced301b442654dd8c23b3fc9-Abstract-round2.html.
🔗 Leike, J. and Sutskever, I. Introducing superalignment. https://openai.com/index/introducing-superalignment/, 2023.
🔗 OpenAI Blog.
🔗 Li, J., Guo, D., Yang, D., Xu, R., Wu, Y., and He, J. Codei/o: Condensing reasoning patterns via code input-output prediction. CoRR,
🔗 abs/2502.07316, 2025. doi: 10.48550/ARXIV.2502.07316. URL https://doi.org/10.48550/arXiv.2502.07316.
🔗 Li, R., Fu, J., Zhang, B., Huang, T., Sun, Z., Lyu, C., Liu, G., Jin, Z., and Li, G. TACO: topics in algorithmic code generation dataset.
🔗 CoRR, abs/2312.14852, 2023. doi: 10.48550/ARXIV.2312.14852. URL https://doi.org/10.48550/arXiv.2312.14852.
🔗 Liu, J. and Zhang, L. Code-r1: Reproducing r1 for code with reliable rewards. GitHub, 2025.
🔗 Liu, J., Xia, C. S., Wang, Y., and Zhang, L. Is your code generated by chatGPT really correct? rigorous evaluation of large
🔗 language models for code generation. In Thirty-seventh Conference on Neural Information Processing Systems, 2023. URL
🔗 https://openreview.net/forum?id=1qvx610Cu7.
🔗 Liu, Z., Chen, C., Li, W., Qi, P., Pang, T., Du, C., Lee, W. S., and Lin, M. Understanding r1-zero-like training: A critical perspective.
🔗 CoRR, abs/2503.20783, 2025. doi: 10.48550/ARXIV.2503.20783. URL https://doi.org/10.48550/arXiv.2503.20783.
🔗 Lopez, R. H. Q. Complexipy: An extremely fast python library to calculate the cognitive complexity of python files, written in rust,
🔗 2025. URL https://github.com/rohaquinlop/complexipy. Accessed: 2025-04-06.
🔗 Loshchilov, I. and Hutter, F. Decoupled weight decay regularization. In 7th International Conference on Learning Representations, ICLR
🔗 2019, New Orleans, LA, USA, May 6-9, 2019. OpenReview.net, 2019. URL https://openreview.net/forum?id=Bkg6RiCqY7.
🔗 Morris, J. There are no new ideas in ai. . . only new datasets. https://blog.jxmo.io/p/there-are-no-new-ideas-in-ai-only,
🔗 2025.
🔗 OpenAI. Openai o3-mini, January 2025a. URL https://openai.com/index/openai-o3-mini/. Accessed: 2025-04-17.
🔗 OpenAI. Introducing openai o3 and o4-mini, April 2025b. URL https://openai.com/index/introducing-o3-and-o4-mini/.
🔗 Accessed: 2025-04-17.
🔗 OpenAI, Plappert, M., Sampedro, R., Xu, T., Akkaya, I., Kosaraju, V., Welinder, P., D’Sa, R., Petron, A., de Oliveira Pinto, H. P.,
🔗 Paino, A., Noh, H., Weng, L., Yuan, Q., Chu, C., and Zaremba, W. Asymmetric self-play for automatic goal discovery in robotic
🔗 manipulation. CoRR, abs/2101.04882, 2021. URL https://arxiv.org/abs/2101.04882.
🔗 Ouyang, L., Wu, J., Jiang, X., Almeida, D., Wainwright, C., Mishkin, P., Zhang, C., Agarwal, S., Slama, K., Ray, A., et al. Training
🔗 language models to follow instructions with human feedback. Advances in neural information processing systems, 35:27730–27744,
🔗 2022.
🔗 Poesia, G., Broman, D., Haber, N., and Goodman, N. D. Learning formal mathematics from intrinsic motivation. In Glober-
🔗 sons, A., Mackey, L., Belgrave, D., Fan, A., Paquet, U., Tomczak, J. M., and Zhang, C. (eds.), Advances in Neural In-
🔗 formation Processing Systems 38: Annual Conference on Neural Information Processing Systems 2024, NeurIPS 2024, Van-
🔗 couver, BC, Canada, December 10 - 15, 2024, 2024.
🔗 URL http://papers.nips.cc/paper_files/paper/2024/hash/
🔗 4b8001fc75f0532827472ea5a16af9ca-Abstract-Conference.html.
🔗 Radford, A., Wu, J., Child, R., Luan, D., Amodei, D., Sutskever, I., et al. Language models are unsupervised multitask learners. OpenAI
🔗 blog, 1(8):9, 2019.
🔗 Ren, Z. Z., Shao, Z., Song, J., Xin, H., Wang, H., Zhao, W., Zhang, L., Fu, Z., Zhu, Q., Yang, D., Wu, Z. F., Gou, Z., Ma, S., Tang, H.,
🔗 Liu, Y., Gao, W., Guo, D., and Ruan, C. Deepseek-prover-v2: Advancing formal mathematical reasoning via reinforcement learning
🔗 for subgoal decomposition, 2025. URL https://arxiv.org/abs/2504.21801.
🔗 Schaul, T. Boundless socratic learning with language games. arXiv preprint arXiv:2411.16905, 2024.
🔗 16