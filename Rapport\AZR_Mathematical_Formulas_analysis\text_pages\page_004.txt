🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Language Model

📐 FORMULE MATHÉMATIQUE:
    𝜋!"#!$%&


📐 FORMULE MATHÉMATIQUE:
    𝜋'#()*


📐 FORMULE MATHÉMATIQUE:
    𝜏


📐 FORMULE MATHÉMATIQUE:
    𝑥, 𝑦⋆, 𝑟"#$"$%&


📐 FORMULE MATHÉMATIQUE:
    𝑦

🔗 Environment

📐 FORMULE MATHÉMATIQUE:
    𝑒, 𝑓


📐 FORMULE MATHÉMATIQUE:
    𝑟%$'(&

🔗 Environment

📐 FORMULE MATHÉMATIQUE:
    𝑒

🔗 Figure 3. The Absolute Zero Loop. The Absolute Zero loop begins with the agent π
🔗 proposing task τ, which is transformed by f with the environment e into a validated
🔗 problem (x, y⋆), and also emits a reward rpropose for learnability. Then, a standard RL step
🔗 follows: the agent solves x by producing y, receiving reward rsolve from e by matching
🔗 with y⋆. πpropose and πsolve are jointly trained and this process can be repeated indefinitely.
🔗 The proposer first samples a proposed
🔗 task conditioned on variable z:

📐 FORMULE MATHÉMATIQUE:
    τ


📐 FORMULE MATHÉMATIQUE:
    ∼

🔗 πpropose

📐 FORMULE MATHÉMATIQUE:
    θ

🔗 (·|z), which will then be validated
🔗 and used to construct a valid reasoning task
🔗 together with the environment e: (x, y⋆) ∼

📐 FORMULE MATHÉMATIQUE:
    fe(·|τ), where x is the task query and y⋆

🔗 is the gold label. Then the solver produces

📐 FORMULE MATHÉMATIQUE:
    an answer y ∼πsolve


📐 FORMULE MATHÉMATIQUE:
    θ

🔗 ( · | x). Each pro-
🔗 posed task τ is scored by a learnability
🔗 reward rpropose

📐 FORMULE MATHÉMATIQUE:
    e

🔗 (τ, πθ), which captures the
🔗 expected improvement in πθ after train-
🔗 ing on the task query x. Moreover, the
🔗 same policy also receives a solution re-
🔗 ward rsolve

📐 FORMULE MATHÉMATIQUE:
    e

🔗 (y, y⋆) for its answer to the task
🔗 query x, with the environment again serv-
🔗 ing as the verifier. A nonnegative coefficient λ balances the trade-off between exploring new, learnable tasks and improving the model’s
🔗 reasoning and problem-solving abilities. We formally define the absolute zero setting’s objective as follows:

📐 FORMULE MATHÉMATIQUE:
    J (θ) := max


📐 FORMULE MATHÉMATIQUE:
    θ


📐 FORMULE MATHÉMATIQUE:
    Ez∼p(z)


📐 FORMULE MATHÉMATIQUE:
    "


📐 FORMULE MATHÉMATIQUE:
    E(x,y⋆)∼fe(·|τ),τ∼πpropose


📐 FORMULE MATHÉMATIQUE:
    θ

🔗 (·|z)

📐 FORMULE MATHÉMATIQUE:
    

🔗 rpropose

📐 FORMULE MATHÉMATIQUE:
    e


📐 FORMULE MATHÉMATIQUE:
    (τ, πθ) + λ Ey∼πsolve


📐 FORMULE MATHÉMATIQUE:
    θ

🔗 (·|x)

📐 FORMULE MATHÉMATIQUE:
    

🔗 rsolve

📐 FORMULE MATHÉMATIQUE:
    e


📐 FORMULE MATHÉMATIQUE:
    (y, y⋆)#


📐 FORMULE MATHÉMATIQUE:
    .

🔗 (3)
🔗 Notice that we shift the burden of scaling data away from human experts and onto the proposer policy πpropose

📐 FORMULE MATHÉMATIQUE:
    θ

🔗 and the environment
🔗 e. These two roles are both responsible for defining/evolving the learning task distribution, validating proposed tasks, and providing
🔗 grounded feedback that supports stable and self-sustainable training. When proposing, z acts as a conditional variable that seeds
🔗 generation of tasks. Practically, z can be instantiated by sampling a small subset of past (task, answer) pairs from a continually updated
🔗 task memory, yet there is no specific implementation tied to the paradigm. To guide the proposing process, we use a learnability reward
🔗 rpropose(τ, πθ), which measures how much the model is expected to improve by solving a proposed task τ. Moreover, the solver reward
🔗 rsolve(y, y∗) evaluates the correctness of the model’s output. Together, these two signals guide the model to propose tasks that are both
🔗 challenging and learnable, while also enhancing its reasoning abilities, ultimately enabling continuous improvement through self-play.
🔗 3. Absolute Zero Reasoner
🔗 In this section, we present Absolute Zero Reasoner (AZR) as the first attempt to embrace the Absolute Zero Paradigm. In AZR, an
🔗 unified LLM serves as both a proposer and a solver: it generates tasks to evolve its learning curriculum and attempts to solve them
🔗 to improve its reasoning capabilities. The model is trained jointly with both roles, learning to create tasks that push the boundary of
🔗 reasoning capacity while enhancing its ability to solve them effectively (Section 3.1). Within this self-play training paradigm, the model
🔗 learns from three distinct type of coding tasks, which corresponding to three fundamental modes of reasoning: abduction, deduction and
🔗 induction (Section 3.2). Using coding tasks is motivated by the Turing-completeness of programming languages (Stuart, 2015) and
🔗 empirical evidence that code-based training improves reasoning (Aryabumi et al., 2024). We adopt code as an open-ended, expressive,
🔗 and verifiable medium for enabling reliable task construction and verification (Section 3.3). Finally, the model is updated using a newly
🔗 proposed advantage estimator designed for multitask learning (Section 3.3.5). We outline the overall algorithm in Algorithm 1 and
🔗 highlight an illustration of our Absolute Zero Reasoner approach in Figure 4. To expedite future exploration in this area, we also present
🔗 several attempts that did not yield fruitful results but still warrant discussion in Appendix D.
🔗 3.1. Two Roles in One: Proposer and Solver
🔗 Large language models are naturally suited for implementing AZR in a multitask learning context (Radford et al., 2019), as both
🔗 the formulation of reasoning tasks and their solutions occur within a unified language space. To this end, we propose rewarding a
🔗 single model for both generating high learning potential tasks and solving them effectively, as specified by the Absolute Zero objective
🔗 in Equation (3). At each iteration of the online rollout, AZR proposes new reasoning tasks by conditioning on the task type (as defined
🔗 in Section 3.2) and K past self-generated examples. The model is explicitly prompted to generate tasks that differ from these examples,
🔗 promoting diversity and broader coverage of the task space. These task proposals are filtered and transformed into valid reasoning
🔗 tasks that can be verified using the environment, outlined later in Section 3.3. AZR then attempts to solve these newly proposed tasks,
🔗 receiving grounded feedback for its model responses. Both task proposal and problem solving are trained using reinforcement learning.
🔗 We now outline the rewards used for each role.
🔗 Reward Design.
🔗 Prior work has shown that setting appropriate task difficulty is critical for promoting effective learning in reasoning
🔗 systems (Zeng et al., 2025b). Motivated by this, we design a reward function for the proposer that encourages generation of tasks
🔗 4