🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 A conversation between User and Assistant. The user asks a question, and the Assistant solves it.
🔗 The assistant first thinks about the reasoning process in the mind and then provides the user
🔗 with the answer. The reasoning process and answer are enclosed within <think> </think> and
🔗 <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> <answer>
🔗 answer here </answer>.

📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→

🔗 User: {TASK_INSTRUCTION}
🔗 Assistant: <think>
🔗 Figure 33. Deepseek R1 Template. All our models were trained using the default Deepseek R1 template.
🔗 40
🔗 Figure 33. Deepseek R1 Template. All our models were trained using the default Deepseek R1 template.
🔗 39