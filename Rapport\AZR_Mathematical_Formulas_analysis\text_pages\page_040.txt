🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 ## Task: Create a Python Code Snippet (where custom classes are allowed, which should be defined
🔗 at the top of the code snippet) with one Matching Input

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 Using the reference code snippets provided below as examples, design a new and unique Python code
🔗 snippet that demands deep algorithmic reasoning to deduce one possible input from a given
🔗 output. Your submission should include both a code snippet and test input pair, where the
🔗 input will be plugged into the code snippet to produce the output, which that function output
🔗 be given to a test subject to come up with any input that will produce the same function
🔗 output. This is meant to be an I.Q. test.

📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→

🔗 ### Code Requirements:
🔗 - Name the entry function `f` (e.g., `def f(...): ...`), you can have nested definitions inside
🔗 `f`

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 - Ensure the function returns a value
🔗 - Include at least one input parameter
🔗 - Make the function deterministic
🔗 - Make the snippet require state tracking across multiple data transformations, ensuring the task
🔗 requires long multi step reasoning

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 - AVOID THE FOLLOWING:
🔗 * Random functions or variables
🔗 * Date/time operations
🔗 * I/O operations (reading files, network requests)
🔗 * Printing or logging
🔗 * Any external state
🔗 - Ensure execution completes within 10 seconds on a modern CPU
🔗 - All imports and class definitions should be at the very top of the code snippet
🔗 - The snippet should end with a return statement from the main function `f`, anything after will
🔗 be removed

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 ### Input Requirements:
🔗 - Provide exactly one test input for your function
🔗 - Format multiple arguments with commas between them
🔗 - Remember to add quotes around string arguments
🔗 ### Formatting:
🔗 - Format your code with: ```python
🔗 def f(...):
🔗 # your code here
🔗 return ...
🔗 ```
🔗 - Format your input with: ```input
🔗 arg1, arg2, ...
🔗 ```
🔗 ### Example Format:
🔗 ```python
🔗 def f(name: str, info: dict):
🔗 # code logic here
🔗 return result
🔗 ```
🔗 ```input
🔗 'John', {{'age': 20, 'city': 'New York'}}
🔗 ```
🔗 ### Evaluation Criteria:
🔗 - Executability, your code should be executable given your input
🔗 - Difficulty in predicting the output from your provided input and code snippet. Focus on either
🔗 algorithmic reasoning or logic complexity. For example, you can define complex data structure
🔗 classes and operate on them like trees, heaps, stacks, queues, graphs, etc, or use complex
🔗 control flow, dynamic programming, recursions, divide and conquer, greedy, backtracking, etc

📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→

🔗 - Creativity, the code needs to be sufficiently different from the provided reference snippets
🔗 - Restricted usage of certain keywords and packages, you are not allowed to use the following
🔗 words in any form, even in comments: {LIST_OF_FORBIDDEN_PACKAGES}

📐 FORMULE MATHÉMATIQUE:
    !→

🔗 First, carefully devise a clear plan: e.g., identify how your snippet will be challenging,
🔗 distinct from reference snippets, and creative. Then, write the final code snippet and its
🔗 inputs.

📐 FORMULE MATHÉMATIQUE:
    !→


📐 FORMULE MATHÉMATIQUE:
    !→

🔗 ### Reference Code Snippets:
🔗 {CODE_REFERENCES_FROM_BUFFER}
🔗 Figure 34. Program Input Abduction Task—Problem Proposal Instruction.
🔗 41
🔗 Figure 34. Program Input Abduction Task—Problem Proposal Instruction.
🔗 40