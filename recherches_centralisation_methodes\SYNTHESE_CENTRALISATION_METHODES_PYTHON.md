# RECHERCHES APPROFONDIES : CENTRALISATION DES MÉTHODES EN PYTHON

## 📋 **CONTEXTE ET OBJECTIFS**

### **Compréhension du Projet AZR**
Après analyse du dossier Rapport, j'ai identifié que :

#### **Clusters dans AZR :**
- **Cluster par défaut** : Ensemble de 3 rollouts coordonnés
- **Rollouts** : Exécutions multiples du modèle pour estimation de difficulté et learnability
- **Types de rollouts identifiés** :
  1. **Online rollouts** : Boucle principale d'entraînement
  2. **Monte Carlo rollouts** : Estimation statistique (n=8 échantillons)
  3. **Rollouts de validation** : Vérification déterministe (j=2 exécutions)

#### **Objectif de Centralisation :**
- **Une méthode universelle** pour tous les clusters/rollouts
- **Configurations multiples** gérées par AZRConfig
- **Réutilisation maximale** du code
- **Évolutivité** pour futurs clusters

---

## 🔍 **PATTERNS DE CENTRALISATION IDENTIFIÉS**

### **1. Factory Method Pattern**
**Source :** Real Python - Factory Method Pattern

#### **Principe :**
```python
class MethodFactory:
    def __init__(self):
        self._creators = {}
    
    def register_method(self, method_id, creator):
        self._creators[method_id] = creator
    
    def execute_method(self, method_id, config, **kwargs):
        creator = self._creators.get(method_id)
        if not creator:
            raise ValueError(f"Method {method_id} not registered")
        return creator(config, **kwargs)
```

#### **Avantages pour AZR :**
- ✅ **Enregistrement dynamique** des méthodes cluster/rollout
- ✅ **Interface commune** pour toutes les exécutions
- ✅ **Extensibilité** sans modification du code existant
- ✅ **Séparation** implémentation/configuration

### **2. Configuration-Driven Architecture**
**Source :** Guillaume Genthial - Techniques for configurable python code

#### **Principe :**
```python
class ConfigurableMethod:
    @classmethod
    def from_config(cls, config):
        return cls(**config.get_method_params())
    
    def execute(self, data, **runtime_params):
        # Méthode universelle utilisant self.config
        pass
```

#### **Avantages pour AZR :**
- ✅ **Centralisation totale** des paramètres
- ✅ **Injection de dépendances** via configuration
- ✅ **Validation** automatique des paramètres
- ✅ **Flexibilité** pour nouveaux cas d'usage

### **3. Plugin System avec Entry Points**
**Source :** Python Packaging Guide - Creating and discovering plugins

#### **Principe :**
```python
# Dans pyproject.toml
[project.entry-points.'azr.methods']
cluster_default = 'azr.methods:ClusterDefaultMethod'
rollout_monte_carlo = 'azr.methods:MonteCarloRolloutMethod'

# Découverte automatique
from importlib.metadata import entry_points

def discover_methods():
    discovered = {}
    for ep in entry_points(group='azr.methods'):
        discovered[ep.name] = ep.load()
    return discovered
```

#### **Avantages pour AZR :**
- ✅ **Découverte automatique** des méthodes
- ✅ **Modularité** maximale
- ✅ **Extensibilité** par packages externes
- ✅ **Standard Python** reconnu

---

## 🏗️ **ARCHITECTURES RECOMMANDÉES**

### **Architecture 1 : Registry Pattern Centralisé**

```python
class AZRMethodRegistry:
    """Registry centralisé pour toutes les méthodes AZR"""

    def __init__(self):
        self._methods = {}
        self._configs = {}

    def register_method(self, method_id: str, method_class: type, default_config: dict):
        """Enregistre une méthode avec sa configuration par défaut"""
        self._methods[method_id] = method_class
        self._configs[method_id] = default_config

    def execute_method(self, method_id: str, config_override: dict = None, **kwargs):
        """Exécute une méthode avec configuration spécifique"""
        if method_id not in self._methods:
            raise ValueError(f"Method {method_id} not registered")

        # Fusion configuration par défaut + override
        final_config = {**self._configs[method_id]}
        if config_override:
            final_config.update(config_override)

        # Création et exécution
        method_instance = self._methods[method_id](final_config)
        return method_instance.execute(**kwargs)

    def get_available_methods(self) -> list:
        """Liste toutes les méthodes disponibles"""
        return list(self._methods.keys())

# Usage
registry = AZRMethodRegistry()
registry.register_method('cluster_default', ClusterDefaultMethod, {
    'rollouts': 3,
    'batch_size': 64,
    'temperature': 1.0
})

# Exécution avec config par défaut
result = registry.execute_method('cluster_default', data=input_data)

# Exécution avec override
result = registry.execute_method('cluster_default',
                                {'rollouts': 5},
                                data=input_data)
```

### **Architecture 2 : Configuration-Driven avec Builder Pattern**

```python
class AZRMethodBuilder:
    """Builder pour création de méthodes configurées"""

    def __init__(self, config_manager):
        self.config_manager = config_manager

    def build_method(self, method_type: str, config_id: str):
        """Construit une méthode selon type et configuration"""
        config = self.config_manager.get_config(method_type, config_id)

        if method_type == 'cluster':
            return self._build_cluster_method(config)
        elif method_type == 'rollout':
            return self._build_rollout_method(config)
        else:
            raise ValueError(f"Unknown method type: {method_type}")

    def _build_cluster_method(self, config):
        """Construction spécialisée pour clusters"""
        rollout_configs = config.get('rollouts', [])
        rollout_methods = []

        for rollout_config in rollout_configs:
            rollout_method = self._build_rollout_method(rollout_config)
            rollout_methods.append(rollout_method)

        return ClusterMethod(rollout_methods, config)

    def _build_rollout_method(self, config):
        """Construction spécialisée pour rollouts"""
        rollout_type = config.get('type', 'monte_carlo')

        if rollout_type == 'monte_carlo':
            return MonteCarloRollout(config)
        elif rollout_type == 'online':
            return OnlineRollout(config)
        elif rollout_type == 'validation':
            return ValidationRollout(config)
        else:
            raise ValueError(f"Unknown rollout type: {rollout_type}")

# Usage avec AZRConfig
class AZRConfig:
    def get_config(self, method_type: str, config_id: str):
        # Retourne configuration depuis centralisation
        pass

builder = AZRMethodBuilder(AZRConfig())
cluster_method = builder.build_method('cluster', 'default_cluster')
result = cluster_method.execute(data)
```

### **Architecture 3 : Hybrid Registry + Plugin System**

```python
class AZRMethodManager:
    """Manager hybride combinant registry et plugin system"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.registry = {}
        self._discover_plugins()

    def _discover_plugins(self):
        """Découverte automatique des plugins via entry points"""
        try:
            from importlib.metadata import entry_points
            for ep in entry_points(group='azr.methods'):
                self.registry[ep.name] = ep.load()
        except ImportError:
            # Fallback pour Python < 3.8
            pass

    def register_method(self, method_id: str, method_class: type):
        """Enregistrement manuel de méthodes"""
        self.registry[method_id] = method_class

    def execute_universal_method(self, method_id: str, config_id: str, **kwargs):
        """Méthode universelle pour toute exécution"""
        if method_id not in self.registry:
            raise ValueError(f"Method {method_id} not found")

        # Récupération configuration centralisée
        config = self.config_manager.get_method_config(method_id, config_id)

        # Création instance configurée
        method_class = self.registry[method_id]
        method_instance = method_class.from_config(config)

        # Exécution universelle
        return method_instance.execute(**kwargs)

    def get_method_info(self, method_id: str) -> dict:
        """Informations sur une méthode"""
        if method_id not in self.registry:
            return None

        method_class = self.registry[method_id]
        return {
            'class': method_class.__name__,
            'module': method_class.__module__,
            'doc': method_class.__doc__,
            'configs': self.config_manager.get_available_configs(method_id)
        }

# Interface universelle pour toutes les méthodes
from abc import ABC, abstractmethod

class UniversalMethod(ABC):
    def __init__(self, config: dict):
        self.config = config
        self.validate_config()

    @classmethod
    def from_config(cls, config: dict):
        return cls(config)

    @abstractmethod
    def validate_config(self):
        """Validation de la configuration"""
        pass

    @abstractmethod
    def execute(self, **kwargs):
        """Exécution universelle"""
        pass

# Implémentation pour cluster
class ClusterMethod(UniversalMethod):
    def validate_config(self):
        required = ['rollouts', 'batch_size']
        for key in required:
            if key not in self.config:
                raise ValueError(f"Missing required config: {key}")

    def execute(self, data, **kwargs):
        # Logique d'exécution du cluster
        rollout_results = []
        for rollout_config in self.config['rollouts']:
            rollout = self._create_rollout(rollout_config)
            result = rollout.execute(data, **kwargs)
            rollout_results.append(result)
        return self._aggregate_results(rollout_results)
```

---

## 📊 **COMPARAISON DES APPROCHES**

| Critère | Registry Pattern | Config-Driven | Plugin System | Hybrid |
|---------|------------------|---------------|---------------|---------|
| **Simplicité** | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐ |
| **Flexibilité** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Performance** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| **Maintenabilité** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Extensibilité** | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Testabilité** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

---

## 🎯 **RECOMMANDATION POUR AZR**

### **Architecture Recommandée : Hybrid Registry + Configuration-Driven**

#### **Justification :**
1. **Centralisation totale** via AZRConfig ✅
2. **Méthode universelle** pour tous clusters/rollouts ✅
3. **Extensibilité** maximale pour futurs besoins ✅
4. **Performance** optimale pour usage intensif ✅
5. **Compatibilité** avec contraintes existantes ✅

#### **Structure Proposée :**
```
azr/
├── config/
│   ├── azr_config.py          # Centralisation totale
│   └── method_configs.py      # Configurations spécialisées
├── methods/
│   ├── registry.py            # Registry centralisé
│   ├── base.py               # Interface universelle
│   ├── clusters/             # Méthodes cluster
│   └── rollouts/             # Méthodes rollout
└── bct.py                    # Utilisation sans valeurs codées
```

#### **Implémentation Clé :**
```python
# Dans bct.py - AUCUNE valeur codée en dur
class BCTProcessor:
    def __init__(self, config_manager):
        self.config = config_manager
        self.method_manager = AZRMethodManager(config_manager)
    
    def process_cluster(self, cluster_id: str, data):
        """Traitement universel de cluster"""
        return self.method_manager.execute_universal_method(
            method_id='cluster',
            config_id=cluster_id,
            data=data
        )
    
    def process_rollout(self, rollout_id: str, data):
        """Traitement universel de rollout"""
        return self.method_manager.execute_universal_method(
            method_id='rollout',
            config_id=rollout_id,
            data=data
        )
```

Cette architecture respecte parfaitement vos contraintes :
- ✅ **Aucune valeur codée en dur** dans bct.py
- ✅ **Centralisation totale** dans AZRConfig
- ✅ **Méthodes universelles** réutilisables
- ✅ **Configurations multiples** supportées
- ✅ **Évolutivité** maximale

---

## 📚 **SOURCES ET RÉFÉRENCES**

1. **Real Python** - Factory Method Pattern in Python
2. **Guillaume Genthial** - Techniques for configurable python code
3. **Python Packaging Guide** - Creating and discovering plugins
4. **Dagster Blog** - Factory Design Patterns in Python
5. **AZR Documentation** - Rollouts et architecture technique

---

*Recherches complètes sur la centralisation des méthodes Python - Optimisé pour le projet AZR*
