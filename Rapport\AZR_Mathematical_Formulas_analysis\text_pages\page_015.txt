🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 <PERSON>, <PERSON>, <PERSON>, and et al. The llama 3 herd of models. CoRR, abs/2407.21783, 2024. doi: 10.48550/ARXIV.2407.21783. URL
🔗 https://doi.org/10.48550/arXiv.2407.21783.
🔗 <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, P. Cyclomatic complexity. IEEE software, 33(6):27–29, 2016.
🔗 <PERSON>, C<PERSON>, <PERSON>, D<PERSON>, <PERSON>, X<PERSON>, and <PERSON><PERSON><PERSON>, <PERSON>. Automatic goal generation for reinforcement learning agents. In Dy, J. <PERSON> and
<PERSON>, <PERSON><PERSON> (eds.), Proceedings of the 35th International Conference on Machine Learning, ICML 2018, Stockholmsmässan,
🔗 Stockholm, Sweden, July 10-15, 2018, volume 80 of Proceedings of Machine Learning Research, pp. 1514–1523. PMLR, 2018. URL
🔗 http://proceedings.mlr.press/v80/florensa18a.html.
🔗 <PERSON>, I<PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Courville, A. C., and <PERSON>, <PERSON><PERSON> Gene<PERSON>
🔗 adversarial networks. Commun. ACM, 63(11):139–144, 2020. doi: 10.1145/3422622. URL https://doi.org/10.1145/3422622.
🔗 Gu, A., Rozière, B., Leather, H. J., Solar-Lezama, A., Synnaeve, G., and Wang, S. Cruxeval: A benchmark for code reasoning,
🔗 understanding and execution. In Forty-first International Conference on Machine Learning, ICML 2024, Vienna, Austria, July 21-27,
🔗 2024. OpenReview.net, 2024. URL https://openreview.net/forum?id=Ffpg52swvg.
🔗 Halstead, M. H. Elements of Software Science (Operating and programming systems series). Elsevier Science Inc., 1977.
🔗 He, C., Luo, R., Bai, Y., Hu, S., Thai, Z. L., Shen, J., Hu, J., Han, X., Huang, Y., Zhang, Y., Liu, J., Qi, L., Liu, Z., and Sun, M.
🔗 Olympiadbench: A challenging benchmark for promoting AGI with olympiad-level bilingual multimodal scientific problems. In Ku,
🔗 L., Martins, A., and Srikumar, V. (eds.), Proceedings of the 62nd Annual Meeting of the Association for Computational Linguistics
🔗 (Volume 1: Long Papers), ACL 2024, Bangkok, Thailand, August 11-16, 2024, pp. 3828–3850. Association for Computational
🔗 Linguistics, 2024. doi: 10.18653/V1/2024.ACL-LONG.211. URL https://doi.org/10.18653/v1/2024.acl-long.211.
🔗 Hendrycks, D., Burns, C., Kadavath, S., Arora, A., Basart, S., Tang, E., Song, D., and Steinhardt, J.
🔗 Measuring math-
🔗 ematical problem solving with the MATH dataset.
🔗 In Vanschoren, J. and Yeung, S. (eds.), Proceedings of the Neu-
🔗 ral Information Processing Systems Track on Datasets and Benchmarks 1, NeurIPS Datasets and Benchmarks 2021,
🔗 December 2021, virtual, 2021.
🔗 URL https://datasets-benchmarks-proceedings.neurips.cc/paper/2021/hash/
🔗 be83ab3ecd0db773eb2dc1b0a17836a1-Abstract-round2.html.
🔗 Hinton, G. E., Vinyals, O., and Dean, J. Distilling the knowledge in a neural network. CoRR, abs/1503.02531, 2015. URL
🔗 http://arxiv.org/abs/1503.02531.
🔗 Hu, J. REINFORCE++: A simple and efficient approach for aligning large language models. CoRR, abs/2501.03262, 2025. doi:
🔗 10.48550/ARXIV.2501.03262. URL https://doi.org/10.48550/arXiv.2501.03262.
🔗 Hu, J., Zhang, Y., Han, Q., Jiang, D., Zhang, X., and Shum, H. Open-reasoner-zero: An open source approach to scaling up
🔗 reinforcement learning on the base model. CoRR, abs/2503.24290, 2025. doi: 10.48550/ARXIV.2503.24290. URL https:
🔗 //doi.org/10.48550/arXiv.2503.24290.
🔗 Hubinger, E., van Merwĳk, C., Mikulik, V., Skalse, J., and Garrabrant, S. Risks from learned optimization in advanced machine learning
🔗 systems. CoRR, abs/1906.01820, 2019. URL http://arxiv.org/abs/1906.01820.
🔗 Hughes, E., Dennis, M. D., Parker-Holder, J., Behbahani, F. M. P., Mavalankar, A., Shi, Y., Schaul, T., and Rocktäschel, T. Position:
🔗 Open-endedness is essential for artificial superhuman intelligence. In Forty-first International Conference on Machine Learning,
🔗 ICML 2024, Vienna, Austria, July 21-27, 2024. OpenReview.net, 2024. URL https://openreview.net/forum?id=Bc4vZ2CX7E.
🔗 Hui, B., Yang, J., Cui, Z., Yang, J., Liu, D., Zhang, L., Liu, T., Zhang, J., Yu, B., Dang, K., Yang, A., Men, R., Huang, F., Ren, X., Ren,
🔗 X., Zhou, J., and Lin, J. Qwen2.5-coder technical report. CoRR, abs/2409.12186, 2024. doi: 10.48550/ARXIV.2409.12186. URL
🔗 https://doi.org/10.48550/arXiv.2409.12186.
🔗 Jaech, A., Kalai, A., Lerer, A., Richardson, A., El-Kishky, A., Low, A., Helyar, A., Madry, A., Beutel, A., Carney, A., et al. Openai o1
🔗 system card. arXiv preprint arXiv:2412.16720, 2024.
🔗 Jain, N., Han, K., Gu, A., Li, W., Yan, F., Zhang, T., Wang, S., Solar-Lezama, A., Sen, K., and Stoica, I. Livecodebench: Holistic and
🔗 contamination free evaluation of large language models for code. CoRR, abs/2403.07974, 2024. doi: 10.48550/ARXIV.2403.07974.
🔗 URL https://doi.org/10.48550/arXiv.2403.07974.
🔗 Kirchner, J. H., Chen, Y., Edwards, H., Leike, J., McAleese, N., and Burda, Y. Prover-verifier games improve legibility of LLM outputs.
🔗 CoRR, abs/2407.13692, 2024. doi: 10.48550/ARXIV.2407.13692. URL https://doi.org/10.48550/arXiv.2407.13692.
🔗 Ladosz, P., Weng, L., Kim, M., and Oh, H. Exploration in deep reinforcement learning: A survey. Inf. Fusion, 85:1–22, 2022. doi:
🔗 10.1016/J.INFFUS.2022.03.003. URL https://doi.org/10.1016/j.inffus.2022.03.003.
🔗 15