🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
model reward

model input/output

rogram

P

utput

O

nput

I

(                 ,               ,                 )

Learnability

Reward

Accuracy

Reward

Absolute

Zero

Reasoner

Absolute

Zero

Reasoner

Verify

Construct & Estimate

PROPOSE

Self-play

SOLVE

Joint Update

Task Types

Induction:

Abduction:

Deduction:

?


📐 FORMULE MATHÉMATIQUE:
    X = F  (     )

P

O

?


📐 FORMULE MATHÉMATIQUE:
    X =   (     )

O

I


📐 FORMULE MATHÉMATIQUE:
    ?  = F  (     )

P

I

🔗 Figure 4. Absolute Zero Reasoner Training Overview. At every iteration, Absolute Zero Reasoner first PROPOSES a batch of tasks,
🔗 conditioned on past self-generated triplets stored in a buffer and a particular task type: abduction, deduction, or induction (Section 3.2).
🔗 From these generated tasks, Python is used to filter and construct valid code-based reasoning questions. A learnability reward rpropose is
🔗 also calculated for each proposed task as defined in Equation (4). The Absolute Zero Reasoner then SOLVES the batch of reasoning
🔗 questions. Python is used again to verify the generated responses and compute the accuracy reward rsolve as described in Equation (5).
🔗 Finally, the Absolute Zero Reasoner is jointly updated using both rpropose and rsolve across all three task types, using TRR++ (Section 3.3.5).
🔗 with meaningful learning potential—neither too easy nor unsolvable for the current solver. Concretely, we use the same language
🔗 model in its solver role to estimate the learnability of a proposed task, a similar type of reward used in unsupervised environment
🔗 design literature (Sukhbaatar et al., 2018). We perform n Monte Carlo rollouts of the solver and compute the average success rate:
🔗 ¯rsolve = 1

📐 FORMULE MATHÉMATIQUE:
    n


📐 FORMULE MATHÉMATIQUE:
    PN


📐 FORMULE MATHÉMATIQUE:
    i=1 r(i)

🔗 solve. The proposer’s reward is then defined as:

📐 FORMULE MATHÉMATIQUE:
    rpropose =


📐 FORMULE MATHÉMATIQUE:
    

🔗 0,
🔗 if ¯rsolve = 0 or ¯rsolve = 1
🔗 1 −¯rsolve,
🔗 otherwise,
🔗 (4)
🔗 The intuition is that if a task is either trivial to solve (¯rsolve = 1) or unsolvable (¯rsolve = 0), the task provides little to no learning signal
🔗 for the proposer. In contrast, tasks of moderate difficulty, where the solver occasionally succeeds are rewarded the most, as they offer the
🔗 richest feedback and greatest potential for learning.
🔗 For the solver, we assign a simple binary reward based on the correctness of its final output,

📐 FORMULE MATHÉMATIQUE:
    rsolve = I(y=y⋆),

🔗 (5)
🔗 where y⋆is the ground-truth answer, and equality is evaluated based on value equality in Python.
🔗 With the primary rewards for the proposing and solving roles defined, we adopt the following composite reward structure, which
🔗 integrates rpropose and rsolve with a format-aware penalty inspired by DeepSeek-AI et al. (2025):

📐 FORMULE MATHÉMATIQUE:
    R(yπ) =


📐 FORMULE MATHÉMATIQUE:
    


📐 FORMULE MATHÉMATIQUE:
    


📐 FORMULE MATHÉMATIQUE:
    

🔗 rrole
🔗 if the response is passable, role ∈{propose,solve}
🔗 −0.5
🔗 if the response is wrong but well-formatted,
🔗 −1
🔗 if the answer has formatting errors,
🔗 (6)
🔗 where yπ is the response of the language model. The main format that the proposing and solving tasks need to follow is the DeepSeek
🔗 R1 <think> and <answer> format, as shown in Figure 33. Moreover, for the proposer, the reward criterion for format goes beyond
🔗 simply following the XML structure. As detailed in Section 3.3.3, only responses that produce valid triplets and pass the filtering stage
🔗 are considered to be correctly formatted.
🔗 5