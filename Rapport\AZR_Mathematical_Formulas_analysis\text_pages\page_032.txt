🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
🔗 Model
🔗 HEval+ MBPP+ LCBv1-5 AIME’24 AIME’25 AMC’23 MATH500 Minerva OlympiadBench
🔗 Llama3.1-8B
🔗 31.7
🔗 53.7
🔗 0.0
🔗 0.0
🔗 0.0
🔗 2.5
🔗 10.6
🔗 5.5
🔗 2.1
🔗 + Simple-RL-Zoo
🔗 38.4
🔗 55.3
🔗 7.4
🔗 0.0
🔗 0.0
🔗 7.5
🔗 22.2
🔗 8.8
🔗 4.7
🔗 + AZR
🔗 35.4
🔗 50.8
🔗 8.5
🔗 3.3
🔗 0.0
🔗 5.0
🔗 13.2
🔗 14.0
🔗 5.0
🔗 Qwen2.5-3B-Coder
🔗 67.1
🔗 65.9
🔗 20.0
🔗 3.3
🔗 3.3
🔗 20.0
🔗 51.0
🔗 18.4
🔗 16.6
🔗 + AZR
🔗 71.3
🔗 69.0
🔗 24.4
🔗 3.3
🔗 3.3
🔗 37.5
🔗 62.0
🔗 26.1
🔗 27.0
🔗 Qwen2.5-14B-Coder
🔗 76.8
🔗 71.7
🔗 31.4
🔗 0.0
🔗 0.0
🔗 37.5
🔗 54.8
🔗 10.7
🔗 18.5
🔗 + AZR
🔗 80.5
🔗 71.2
🔗 39.0
🔗 23.3
🔗 20.0
🔗 65.0
🔗 78.6
🔗 32.0
🔗 39.3
🔗 Qwen2.5-14B-Base
🔗 78.0
🔗 66.7
🔗 21.7
🔗 6.7
🔗 3.3
🔗 35.0
🔗 66.2
🔗 28.3
🔗 32.4
🔗 + AZR
🔗 70.7
🔗 68.8
🔗 35.2
🔗 10.0
🔗 20.0
🔗 62.5
🔗 76.2
🔗 40.4
🔗 42.5
🔗 Table 5. Detailed Breakdown of Evaluation Benchmarks for Other Model Sizes and Types. Full evaluation of AZR trained on
🔗 other models on three standard code benchmarks (HEval+, MBPP+, LCBv1-5) and six math benchmarks (AIME’24, AIME’25, AMC’23,
🔗 MATH500, Minerva, OlympiadBench).
🔗 has been observed and presented for jointly trained reasoning multi-tasks. Previously, length differences were typically noted between
🔗 correct and incorrect traces (Liu et al., 2025).
🔗 The reward dynamics between the propose and solve roles exhibit mildly adversarial behavior: when one receives higher rewards, the
🔗 other often receives lower rewards. However, this is not entirely adversarial, as the proposer is also penalized for generating unsolvable
🔗 tasks, encouraging overall cooperative behavior in the learning process.
🔗 C.4. Complexity and Diversity Metrics of AZR Proposed Tasks
🔗 We outline several metrics used to probe characteristics of the tasks proposed during the training of AZR from the base model. Specifically,
🔗 we log two sets of metrics: program complexity and task diversity. For complexity, we employ two proxy measures—ComplexiPy score
🔗 and the Halstead metric. To assess diversity, we compute the average abstract syntax tree (AST) edit distance between the proposed
🔗 program and a set of K reference programs, and an answer diversity metric. We calculate this answer diversity metric by tracking all
🔗 historical answers to the generated questions, i.e., the input-output pairs, and form a categorical distribution over these outputs. We
🔗 define answer diversity as 1 −p(answer), where p(answer) is the empirical probability of a specific answer—used as a proxy for the
🔗 diversity of generated outputs.
🔗 We present these metrics in Figure 27. Interestingly, even without incorporating them explicitly into the reward function, the policy
🔗 appears to implicitly optimize for these metrics. This aligns well with intuitive notions of task difficulty and diversity, and that the policy
🔗 learned to propose increasingly challenging tasks over time using our proposed simple reward function in Equation (4).
🔗 C.5. Generated Code Complexity Dynamics Between Abd/Ded and Ind.
🔗 We use the ComplexiPy package to measure code complexity. For each generated program in the induction task, we compute the
🔗 cognitive complexity difference from the corresponding “gold” code, i.e. complexipy(pπpropose
🔗 {abduction,deduction}) −complexipy(pπsolve
🔗 induction) for each
🔗 pair, where the superscript of π indicates the role and the subscript indicates the task type(s), and p denotes the generated programs. On
🔗 average, the difference of proposer and solver while holding the code’s functionality constant is 0.27, indicating that the proposer in the
🔗 abduction/deduction tasks often increases the cognitive complexity to make the code appear more convoluted, whereas the induction
🔗 solver tends to generate more efficient implementations.
🔗 32