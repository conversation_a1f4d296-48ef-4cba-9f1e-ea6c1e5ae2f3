🔗 Absolute Zero: Reinforced Self-play Reasoning with Zero Data
MATH REASONING

CODE REASONING

OVERALL PERFORMANCE

50

75

100

125

150

175

200

225

250

  0.050
  0.100
  0.150
  0.200
Accuracy

AIME 2024

50

75

100

125

150

175

200

225

250

  0.000
  0.050
  0.100
AIME 2025

50

75

100

125

150

175

200

225

250

  0.300
  0.325
  0.350
  0.375
  0.400
Olympiad Bench

50

75

100

125

150

175

200

225

250

  0.280
  0.300
  0.320
  0.340
  0.360
  0.380
Accuracy

Minerva

50

75

100

125

150

175

200

225

250

  0.625
  0.650
  0.675
  0.700
  0.725
  0.750
Math 500

50

75

100

125

150

175

200

225

250

  0.400
  0.450
  0.500
  0.550
  0.600
AMC 2023

50

75

100

125

150

175

200

225

250

  0.700
  0.710
  0.720
  0.730
  0.740
Accuracy

HumanEval+

50

75

100

125

150

175

200

225

250

  0.660
  0.670
  0.680
  0.690
  0.700
MBPP+

50

75

100

125

150

175

200

225

250

  0.240
  0.260
  0.280
LiveCodeBench

50

75

100

125

150

175

200

225

250

  0.300
  0.320
  0.340
  0.360
  0.380
  0.400
Accuracy

Math Average

50

75

100

125

150

175

200

225

250

  0.540
  0.550
  0.560
  0.570
Code Average

50

75

100

125

150

175

200

225

250

  0.420
  0.440
  0.460
  0.480
Overall Average

🔗 Figure 28. Absolute Zero Reasoner-base-7b OOD Performance Breakdown.
🔗 35